import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
import os
warnings.filterwarnings('ignore')

# 修复中文字体显示问题
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.size'] = 12

# 创建输出文件夹
output_dir = '/Users/<USER>/Desktop/量化择时胜率'
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

print("=" * 80)
print("🔧 修复汉字显示问题并重新生成图表")
print("=" * 80)

# 加载数据
file_path = '/Users/<USER>/Desktop/择时胜率.xlsx'
df = pd.read_excel(file_path)

# 数据预处理
df_processed = df.fillna(method='ffill').fillna(method='bfill').copy()
if 'Unnamed: 0' in df_processed.columns:
    df_processed = df_processed.rename(columns={'Unnamed: 0': '日期'})

# 构建目标变量
df_processed['30-10y_next'] = df_processed['30-10y'].shift(-1)
df_processed['30-10y_change'] = df_processed['30-10y_next'] - df_processed['30-10y']
df_processed['direction_next'] = (df_processed['30-10y_change'] > 0).astype(int)
df_processed = df_processed[:-1].copy()

# 定义因子分类
factor_categories = {
    '基本面因子': ['水泥价格指数', '建材综合指数', '南华金属指数', '南华能化指数', '南华工业品指数'],
    '技术面因子': ['偏离度（偏离均线）', '偏离度（偏离MA+std）'],
    '资金面因子': ['R007', 'DR007', '成交量:DR007', '成交量:R007'],
    '权益市场因子': ['1/沪深300PE-10y'],
    '成交因子': ['30Y成交量']
}

all_factors = []
for factors in factor_categories.values():
    all_factors.extend(factors)

available_factors = [f for f in all_factors if f in df_processed.columns]

def calculate_single_factor_win_rate(df, factor, target='direction_next'):
    """计算单因子预测胜率"""
    if factor not in df.columns:
        return None

    # 基于因子分位数的预测策略
    factor_data = df[factor].dropna()
    target_data = df[target].dropna()

    # 确保数据对齐
    valid_idx = ~(df[factor].isna() | df[target].isna())
    factor_values = df.loc[valid_idx, factor]
    target_values = df.loc[valid_idx, target]

    if len(factor_values) < 10:
        return None

    # 计算相关性
    correlation = factor_values.corr(target_values)

    # 计算统计显著性
    try:
        _, p_value = stats.pearsonr(factor_values, target_values)
    except:
        p_value = 1.0

    # 基于中位数分组的预测策略
    factor_median = factor_values.median()

    # 预测策略：基于相关性方向
    if correlation > 0:
        prediction = (factor_values > factor_median).astype(int)
    else:
        prediction = (factor_values <= factor_median).astype(int)

    win_rate = (prediction == target_values).mean()

    return {
        'factor': factor,
        'correlation': correlation,
        'p_value': p_value,
        'significant': p_value < 0.05,
        'win_rate': win_rate,
        'factor_strength': abs(correlation)
    }

def analyze_all_factors(df, factors):
    """分析所有因子"""
    print("分析所有因子的预测能力...")

    results = []
    for factor in factors:
        if factor not in df.columns:
            continue

        result = calculate_single_factor_win_rate(df, factor)
        if result is not None:
            results.append(result)

    return pd.DataFrame(results).sort_values('win_rate', ascending=False)

# 分析因子表现
factor_analysis = analyze_all_factors(df_processed, available_factors)

# 构建策略结果（模拟数据，基于之前的分析结果）
strategies_data = [
    {'strategy_name': '分层权重策略', 'accuracy': 0.5846, 'strategy_type': '分层策略'},
    {'strategy_name': '胜率加权策略', 'accuracy': 0.5785, 'strategy_type': '加权策略'},
    {'strategy_name': '等权重投票策略', 'accuracy': 0.5723, 'strategy_type': '传统策略'},
    {'strategy_name': '优化权重策略', 'accuracy': 0.5631, 'strategy_type': '优化策略'},
    {'strategy_name': '动态阈值策略', 'accuracy': 0.5538, 'strategy_type': '动态策略'},
    {'strategy_name': '逻辑回归策略', 'accuracy': 0.5408, 'strategy_type': '机器学习'},
    {'strategy_name': '随机森林策略', 'accuracy': 0.5204, 'strategy_type': '机器学习'}
]

strategies_df = pd.DataFrame(strategies_data)

def create_fixed_chinese_visualizations(factor_analysis, strategies_df, df, output_dir):
    """创建修复汉字显示的可视化图表"""
    print(f"\n{'='*60}")
    print("🎨 创建修复汉字显示的可视化图表")
    print(f"{'='*60}")

    # 测试中文字体
    fig, ax = plt.subplots(figsize=(6, 4))
    ax.text(0.5, 0.5, '中文字体测试', fontsize=16, ha='center', va='center')
    ax.set_title('字体测试')
    plt.savefig(f'{output_dir}/字体测试.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✓ 字体测试图已生成")

    # 1. 因子胜率排名图（修复版）
    plt.figure(figsize=(16, 10))

    top15_factors = factor_analysis.head(15)

    # 使用简单的颜色方案
    colors = ['#FF6B6B' if i % 2 == 0 else '#4ECDC4' for i in range(len(top15_factors))]

    bars = plt.bar(range(len(top15_factors)), top15_factors['win_rate'],
                   color=colors, alpha=0.8, edgecolor='black', linewidth=1)

    # 添加基准线
    plt.axhline(y=0.5, color='red', linestyle='--', linewidth=2, alpha=0.7, label='随机基准线(50%)')
    baseline = df['direction_next'].mean()
    plt.axhline(y=baseline, color='blue', linestyle='--', linewidth=2, alpha=0.7,
                label=f'基准走阔概率({baseline:.3f})')

    # 设置标签
    plt.xlabel('因子排名', fontsize=14, fontweight='bold')
    plt.ylabel('预测胜率', fontsize=14, fontweight='bold')
    plt.title('TOP15因子预测胜率排名（修复版）', fontsize=16, fontweight='bold', pad=20)

    # 设置x轴标签 - 简化处理
    factor_labels = []
    for i, factor in enumerate(top15_factors['factor']):
        if len(factor) > 10:
            factor = factor[:8] + '..'
        factor_labels.append(f"{i+1}.\n{factor}")

    plt.xticks(range(len(top15_factors)), factor_labels, rotation=45, ha='right', fontsize=10)

    # 添加数值标签
    for i, (bar, rate) in enumerate(zip(bars, top15_factors['win_rate'])):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005,
                f'{rate:.3f}', ha='center', va='bottom', fontweight='bold', fontsize=9)

    plt.legend(loc='upper right', fontsize=12)
    plt.grid(True, alpha=0.3, axis='y')
    plt.tight_layout()
    plt.savefig(f'{output_dir}/TOP15因子胜率排名_修复版.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✓ TOP15因子胜率排名图已生成")

    # 2. 策略胜率对比图（修复版）
    plt.figure(figsize=(14, 8))

    strategies_sorted = strategies_df.sort_values('accuracy', ascending=False)

    # 根据策略类型设置颜色
    color_map = {
        '传统策略': '#3498DB',
        '加权策略': '#E74C3C',
        '优化策略': '#2ECC71',
        '分层策略': '#F39C12',
        '动态策略': '#9B59B6',
        '机器学习': '#1ABC9C'
    }

    strategy_colors = [color_map.get(stype, '#95A5A6') for stype in strategies_sorted['strategy_type']]

    bars = plt.bar(range(len(strategies_sorted)), strategies_sorted['accuracy'],
                   color=strategy_colors, alpha=0.8, edgecolor='black', linewidth=1.5)

    # 添加基准线
    plt.axhline(y=0.5, color='red', linestyle='--', linewidth=2, alpha=0.7, label='随机基准线')
    plt.axhline(y=baseline, color='blue', linestyle='--', linewidth=2, alpha=0.7,
                label=f'基准走阔概率({baseline:.3f})')

    # 设置标签
    plt.xlabel('策略类型', fontsize=14, fontweight='bold')
    plt.ylabel('预测胜率', fontsize=14, fontweight='bold')
    plt.title('因子组合策略胜率对比（修复版）', fontsize=16, fontweight='bold', pad=20)

    # 设置x轴标签
    strategy_labels = []
    for name in strategies_sorted['strategy_name']:
        name = name.replace('策略', '')
        strategy_labels.append(name)

    plt.xticks(range(len(strategies_sorted)), strategy_labels, rotation=45, ha='right', fontsize=11)

    # 添加数值标签
    for i, (bar, acc) in enumerate(zip(bars, strategies_sorted['accuracy'])):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.002,
                f'{acc:.4f}', ha='center', va='bottom', fontweight='bold', fontsize=10)

    plt.legend(loc='upper right', fontsize=12)
    plt.grid(True, alpha=0.3, axis='y')
    plt.ylim(0.48, max(strategies_sorted['accuracy']) + 0.02)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/策略胜率对比_修复版.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✓ 策略胜率对比图已生成")

    # 3. 因子类别表现分析图
    plt.figure(figsize=(12, 8))

    # 按类别分析因子表现
    category_performance = []
    for category, factors in factor_categories.items():
        available_category_factors = [f for f in factors if f in factor_analysis['factor'].values]
        if available_category_factors:
            category_data = factor_analysis[factor_analysis['factor'].isin(available_category_factors)]
            avg_win_rate = category_data['win_rate'].mean()
            max_win_rate = category_data['win_rate'].max()
            factor_count = len(available_category_factors)

            category_performance.append({
                'category': category,
                'avg_win_rate': avg_win_rate,
                'max_win_rate': max_win_rate,
                'factor_count': factor_count
            })

    if category_performance:
        category_df = pd.DataFrame(category_performance)

        x = range(len(category_df))
        width = 0.35

        bars1 = plt.bar([i - width/2 for i in x], category_df['avg_win_rate'], width,
                       label='平均胜率', alpha=0.8, color='lightblue')
        bars2 = plt.bar([i + width/2 for i in x], category_df['max_win_rate'], width,
                       label='最高胜率', alpha=0.8, color='lightcoral')

        plt.axhline(y=0.5, color='red', linestyle='--', alpha=0.5, label='随机基准')

        plt.xlabel('因子类别', fontsize=12, fontweight='bold')
        plt.ylabel('胜率', fontsize=12, fontweight='bold')
        plt.title('各类别因子表现分析', fontsize=14, fontweight='bold')
        plt.xticks(x, category_df['category'], rotation=45, ha='right')
        plt.legend()
        plt.grid(True, alpha=0.3)

        # 添加数值标签
        for bar in bars1:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 0.005,
                    f'{height:.3f}', ha='center', va='bottom', fontsize=9)

        for bar in bars2:
            height = bar.get_height()
            plt.text(bar.get_x() + bar.get_width()/2., height + 0.005,
                    f'{height:.3f}', ha='center', va='bottom', fontsize=9)

        plt.tight_layout()
        plt.savefig(f'{output_dir}/因子类别表现分析_修复版.png', dpi=300, bbox_inches='tight')
        plt.show()
        print("✓ 因子类别表现分析图已生成")

create_fixed_chinese_visualizations(factor_analysis, strategies_df, df_processed, output_dir)

def create_comprehensive_fixed_charts(factor_analysis, strategies_df, df, output_dir):
    """创建全面的修复版图表"""
    print(f"\n{'='*60}")
    print("📊 创建全面的修复版图表")
    print(f"{'='*60}")

    # 4. 因子胜率vs相关性散点图（修复版）
    plt.figure(figsize=(12, 8))

    # 绘制散点图
    scatter = plt.scatter(factor_analysis['factor_strength'], factor_analysis['win_rate'],
                         c=factor_analysis['significant'], s=100, cmap='RdYlGn', alpha=0.7,
                         edgecolors='black', linewidth=1)

    plt.axhline(y=0.5, color='red', linestyle='--', alpha=0.5, label='随机基准')
    plt.axvline(x=0.05, color='orange', linestyle='--', alpha=0.5, label='显著性阈值')

    plt.xlabel('因子强度 (|相关系数|)', fontsize=12, fontweight='bold')
    plt.ylabel('预测胜率', fontsize=12, fontweight='bold')
    plt.title('因子强度与预测胜率关系（修复版）', fontsize=14, fontweight='bold')

    # 添加颜色条
    cbar = plt.colorbar(scatter)
    cbar.set_label('统计显著性 (1=显著, 0=不显著)', fontsize=10)

    # 添加因子名称标注（只标注TOP5）
    top5_factors = factor_analysis.head(5)
    for _, row in top5_factors.iterrows():
        factor_name = row['factor']
        if len(factor_name) > 8:
            factor_name = factor_name[:6] + '..'
        plt.annotate(factor_name,
                    (row['factor_strength'], row['win_rate']),
                    xytext=(5, 5), textcoords='offset points',
                    fontsize=8, alpha=0.8,
                    bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.5))

    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/因子强度与胜率关系_修复版.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✓ 因子强度与胜率关系图已生成")

    # 5. 策略提升效果对比图
    plt.figure(figsize=(14, 8))

    baseline = df['direction_next'].mean()
    strategies_sorted = strategies_df.sort_values('accuracy', ascending=False)

    # 计算提升幅度
    improvements = [(acc - baseline) * 100 for acc in strategies_sorted['accuracy']]

    # 创建条形图
    bars = plt.bar(range(len(strategies_sorted)), improvements,
                   color=['#2ECC71' if imp > 0 else '#E74C3C' for imp in improvements],
                   alpha=0.8, edgecolor='black', linewidth=1)

    plt.axhline(y=0, color='black', linestyle='-', linewidth=1)

    plt.xlabel('策略类型', fontsize=12, fontweight='bold')
    plt.ylabel('相比基准提升 (百分点)', fontsize=12, fontweight='bold')
    plt.title('各策略相比基准的提升效果', fontsize=14, fontweight='bold')

    # 设置x轴标签
    strategy_labels = [name.replace('策略', '') for name in strategies_sorted['strategy_name']]
    plt.xticks(range(len(strategies_sorted)), strategy_labels, rotation=45, ha='right')

    # 添加数值标签
    for i, (bar, imp) in enumerate(zip(bars, improvements)):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1 if imp > 0 else bar.get_height() - 0.3,
                f'{imp:.2f}%', ha='center', va='bottom' if imp > 0 else 'top',
                fontweight='bold', fontsize=10)

    plt.grid(True, alpha=0.3, axis='y')
    plt.tight_layout()
    plt.savefig(f'{output_dir}/策略提升效果对比_修复版.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✓ 策略提升效果对比图已生成")

    # 6. 因子胜率分布直方图
    plt.figure(figsize=(10, 6))

    plt.hist(factor_analysis['win_rate'], bins=15, alpha=0.7, color='skyblue',
             edgecolor='black', linewidth=1)
    plt.axvline(x=0.5, color='red', linestyle='--', linewidth=2, label='随机基准线')
    plt.axvline(x=factor_analysis['win_rate'].mean(), color='green', linestyle='--',
                linewidth=2, label=f'平均胜率({factor_analysis["win_rate"].mean():.3f})')

    plt.xlabel('预测胜率', fontsize=12, fontweight='bold')
    plt.ylabel('因子数量', fontsize=12, fontweight='bold')
    plt.title('因子胜率分布直方图', fontsize=14, fontweight='bold')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/因子胜率分布_修复版.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✓ 因子胜率分布图已生成")

    # 7. 综合策略对比雷达图
    fig, ax = plt.subplots(figsize=(10, 10), subplot_kw=dict(projection='polar'))

    # 选择TOP5策略
    top5_strategies = strategies_sorted.head(5)

    # 设置角度
    angles = np.linspace(0, 2 * np.pi, len(top5_strategies), endpoint=False).tolist()
    angles += angles[:1]  # 闭合图形

    # 绘制每个策略
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']

    for i, (_, strategy) in enumerate(top5_strategies.iterrows()):
        values = [strategy['accuracy']] * len(angles)
        ax.plot(angles, values, 'o-', linewidth=2, label=strategy['strategy_name'].replace('策略', ''),
                color=colors[i % len(colors)])
        ax.fill(angles, values, alpha=0.25, color=colors[i % len(colors)])

    # 设置标签
    strategy_labels = [name.replace('策略', '') for name in top5_strategies['strategy_name']]
    ax.set_xticks(angles[:-1])
    ax.set_xticklabels(strategy_labels)
    ax.set_ylim(0.5, 0.6)
    ax.set_title('TOP5策略胜率雷达图', fontsize=14, fontweight='bold', pad=20)
    ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))

    plt.tight_layout()
    plt.savefig(f'{output_dir}/策略胜率雷达图_修复版.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("✓ 策略胜率雷达图已生成")

create_comprehensive_fixed_charts(factor_analysis, strategies_df, df_processed, output_dir)

def save_fixed_analysis_summary(factor_analysis, strategies_df, output_dir):
    """保存修复版分析汇总"""
    print(f"\n{'='*60}")
    print("💾 保存修复版分析汇总")
    print(f"{'='*60}")

    # 创建汇总Excel
    with pd.ExcelWriter(f'{output_dir}/修复版_因子策略分析汇总.xlsx') as writer:

        # 因子分析结果
        factor_analysis.to_excel(writer, sheet_name='因子胜率分析', index=False)

        # 策略对比结果
        strategies_df.to_excel(writer, sheet_name='策略胜率对比', index=False)

        # TOP因子汇总
        top10_factors = factor_analysis.head(10)
        top10_summary = pd.DataFrame({
            '排名': range(1, 11),
            '因子名称': top10_factors['factor'].values,
            '预测胜率': top10_factors['win_rate'].values,
            '相关系数': top10_factors['correlation'].values,
            '统计显著性': ['是' if sig else '否' for sig in top10_factors['significant'].values]
        })
        top10_summary.to_excel(writer, sheet_name='TOP10因子汇总', index=False)

        # 策略构建指南
        strategy_guide = pd.DataFrame([
            {
                '策略名称': '分层权重策略',
                '预测胜率': '58.46%',
                '构建方法': '技术面因子权重3，成交量因子权重2，基本面因子权重1',
                '适用场景': '推荐作为主策略使用',
                '风险等级': '中等'
            },
            {
                '策略名称': '胜率加权策略',
                '预测胜率': '57.85%',
                '构建方法': '根据各因子历史胜率分配权重',
                '适用场景': '因子表现差异明显时',
                '风险等级': '中等'
            },
            {
                '策略名称': '等权重投票策略',
                '预测胜率': '57.23%',
                '构建方法': '所有因子等权重投票',
                '适用场景': '因子表现相近时',
                '风险等级': '较低'
            },
            {
                '策略名称': '动态阈值策略',
                '预测胜率': '55.38%',
                '构建方法': '根据市场波动性调整决策阈值',
                '适用场景': '市场波动较大时期',
                '风险等级': '较高'
            }
        ])
        strategy_guide.to_excel(writer, sheet_name='策略构建指南', index=False)

    print(f"✅ 修复版分析汇总已保存: {output_dir}/修复版_因子策略分析汇总.xlsx")

save_fixed_analysis_summary(factor_analysis, strategies_df, output_dir)

print(f"\n" + "="*80)
print(f"🎉 汉字显示问题修复完成！")
print(f"="*80)
print(f"📁 所有修复版图表已保存到: {output_dir}")
print(f"📊 生成的修复版图表:")
print(f"  1. TOP15因子胜率排名_修复版.png")
print(f"  2. 策略胜率对比_修复版.png")
print(f"  3. 因子类别表现分析_修复版.png")
print(f"  4. 因子强度与胜率关系_修复版.png")
print(f"  5. 策略提升效果对比_修复版.png")
print(f"  6. 因子胜率分布_修复版.png")
print(f"  7. 策略胜率雷达图_修复版.png")
print(f"  8. 修复版_因子策略分析汇总.xlsx")
print(f"="*80)
