"""
Tencent PCG Core Metrics Monitoring
-----------------------------------
Daily tracking of:
- DAU (Daily Active Users)
- Feature usage frequency (likes, comments, shares)
- Retention rates (D1/D7/D30)
- Conversion funnel metrics
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.ensemble import IsolationForest

def generate_daily_dashboard(user_activity, feature_usage):
    """
    Generate automated dashboard for core metrics monitoring
    with anomaly detection for metric fluctuations.
    
    Parameters:
    user_activity (DataFrame): Daily user activity data
    feature_usage (DataFrame): Feature usage statistics
    
    Returns:
    dict: Dashboard report with metrics and anomaly flags
    """
    # Core metrics calculation
    report = {
        "date": pd.Timestamp.now().strftime("%Y-%m-%d"),
        "dau": calculate_dau(user_activity),
        "feature_usage": calculate_feature_usage(feature_usage),
        "retention": calculate_retention(user_activity),
        "conversion": calculate_conversion(user_activity),
        "anomalies": detect_anomalies(user_activity)
    }
    
    # Generate visualizations
    generate_plots(report)
    
    return report

def calculate_dau(df):
    """Calculate Daily Active Users"""
    return df['user_id'].nunique()

def calculate_feature_usage(df):
    """Calculate feature usage frequencies"""
    return {
        'likes': df['like_count'].sum(),
        'comments': df['comment_count'].sum(),
        'shares': df['share_count'].sum()
    }

def calculate_retention(df):
    """Calculate D1/D7/D30 retention rates"""
    # Placeholder - actual implementation would require cohort analysis
    return {
        'D1': 0.45,
        'D7': 0.25,
        'D30': 0.15
    }

def calculate_conversion(df):
    """Calculate conversion funnel metrics"""
    # Placeholder - actual implementation would track user journey
    return {
        'search_to_play': 0.75,
        'play_to_next': 0.60,
        'next_to_engagement': 0.40
    }

def detect_anomalies(df, sensitivity=0.05):
    """Detect anomalies in metrics using Isolation Forest"""
    # Feature engineering for anomaly detection
    features = df.groupby('date').agg({
        'user_id': 'nunique',
        'session_duration': 'mean',
        'like_count': 'sum',
        'comment_count': 'sum',
        'share_count': 'sum'
    }).reset_index()
    
    # Anomaly detection model
    model = IsolationForest(contamination=sensitivity)
    features['anomaly_score'] = model.fit_predict(features.select_dtypes(include=np.number))
    
    # Return dates with anomalies
    return features[features['anomaly_score'] == -1]['date'].tolist()

def generate_plots(report):
    """Generate visualization plots for dashboard"""
    # Feature usage bar plot
    plt.figure(figsize=(10, 6))
    sns.barplot(x=list(report['feature_usage'].keys()), 
                y=list(report['feature_usage'].values()))
    plt.title('Daily Feature Usage')
    plt.ylabel('Count')
    plt.savefig('reports/feature_usage.png')
    
    # Retention curve
    plt.figure(figsize=(10, 6))
    sns.lineplot(x=list(report['retention'].keys()), 
                 y=list(report['retention'].values()))
    plt.title('User Retention Curve')
    plt.ylabel('Retention Rate')
    plt.savefig('reports/retention_curve.png')
    
    # Conversion funnel
    plt.figure(figsize=(8, 10))
    funnel_data = pd.DataFrame({
        'stage': list(report['conversion'].keys()),
        'rate': list(report['conversion'].values())
    })
    sns.barplot(y='stage', x='rate', data=funnel_data, orient='h')
    plt.title('Conversion Funnel')
    plt.xlabel('Conversion Rate')
    plt.savefig('reports/conversion_funnel.png')
    
    plt.close('all')
