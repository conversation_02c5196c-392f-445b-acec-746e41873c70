import numpy as np
import pandas as pd
from arch import arch_model
from scipy.stats import norm
from scipy.optimize import brentq
from sklearn.linear_model import LogisticRegression
import matplotlib.pyplot as plt
import seaborn as sns

class VolatilityStrategy:
    def __init__(self, risk_free_rate=0.03):
        """
        可转债波动率策略核心类
        
        参数:
        risk_free_rate (float): 无风险利率，默认3%
        """
        self.risk_free_rate = risk_free_rate
    
    def black_scholes_call(self, S, K, T, r, sigma):
        """
        Black-Scholes期权定价模型（看涨期权）
        
        参数:
        S (float): 标的资产当前价格
        K (float): 行权价格
        T (float): 到期时间（年）
        r (float): 无风险利率
        sigma (float): 波动率
        
        返回:
        float: 看涨期权价格
        """
        d1 = (np.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * np.sqrt(T))
        d2 = d1 - sigma * np.sqrt(T)
        return S * norm.cdf(d1) - K * np.exp(-r * T) * norm.cdf(d2)
    
    def calculate_implied_volatility(self, bond_price, bond_par, conversion_ratio, stock_price, time_to_maturity):
        """
        使用BS模型计算可转债隐含波动率
        
        参数:
        bond_price (float): 可转债市场价格
        bond_par (float): 债券面值
        conversion_ratio (float): 转股比例（每张债券可转股数）
        stock_price (float): 正股当前价格
        time_to_maturity (float): 剩余期限（年）
        
        返回:
        float: 隐含波动率
        """
        # 计算纯债价值（简化处理）
        bond_value = bond_par * np.exp(-self.risk_free_rate * time_to_maturity)
        
        # 计算期权部分价值
        option_value = bond_price - bond_value
        
        # 转换为每股期权价值
        option_value_per_share = option_value / conversion_ratio
        
        # 行权价（转股价）
        strike_price = bond_par / conversion_ratio
        
        # 定义隐含波动率求解方程
        def iv_func(sigma):
            return self.black_scholes_call(
                S=stock_price,
                K=strike_price,
                T=time_to_maturity,
                r=self.risk_free_rate,
                sigma=sigma
            ) - option_value_per_share
        
        try:
            # 使用Brent方法求解隐含波动率
            iv = brentq(iv_func, 0.001, 5.0)
            return iv
        except ValueError:
            return np.nan
    
    def calculate_historical_volatility(self, stock_prices, method='std', window=30, **garch_params):
        """
        计算历史波动率
        
        参数:
        stock_prices (pd.Series): 正股历史价格序列
        method (str): 计算方法 ['std', 'garch']
        window (int): 移动窗口大小（仅用于std方法）
        garch_params (dict): GARCH模型参数
        
        返回:
        pd.Series: 波动率序列
        """
        returns = np.log(stock_prices / stock_prices.shift(1)).dropna()
        
        if method == 'std':
            # 标准差方法
            return returns.rolling(window=window).std() * np.sqrt(252)
        
        elif method == 'garch':
            # GARCH(1,1)模型
            # 设置默认GARCH参数
            garch_params.setdefault('vol', 'Garch')
            garch_params.setdefault('p', 1)
            garch_params.setdefault('q', 1)
            model = arch_model(returns, **garch_params)
            res = model.fit(disp='off')
            return res.conditional_volatility * np.sqrt(252)
        
        else:
            raise ValueError("不支持的波动率计算方法")
    
    def volatility_comparison_strategy(self, bond_data):
        """
        隐含波动率与历史波动率比较策略
        寻找期权价值被低估的可转债
        
        参数:
        bond_data (pd.DataFrame): 包含各转债数据的DataFrame
        
        返回:
        pd.DataFrame: 包含信号和配置权重的DataFrame
        """
        # 计算波动率差异
        bond_data['vol_diff'] = bond_data['historical_vol'] - bond_data['implied_vol']
        
        # 生成信号：当历史波动率高于隐含波动率时买入
        bond_data['signal'] = np.where(
            bond_data['vol_diff'] > 0.1,  # 阈值可调整
            1,  # 买入
            0   # 中性
        )
        
        # 计算配置权重（波动率差异越大权重越高）
        positive_diff = bond_data[bond_data['vol_diff'] > 0]
        total_diff = positive_diff['vol_diff'].sum()
        bond_data['weight'] = bond_data.apply(
            lambda row: row['vol_diff'] / total_diff if row['signal'] == 1 else 0,
            axis=1
        )
        
        return bond_data[['bond_code', 'signal', 'weight']]
    
    def mean_reversion_strategy(self, volatility_series, z_threshold=1.0):
        """
        波动率均值回归策略
        
        参数:
        volatility_series (pd.Series): 波动率时间序列
        z_threshold (float): Z-score阈值
        
        返回:
        pd.Series: 交易信号（1:买入, -1:卖出, 0:持有）
        """
        # 计算滚动均值和标准差
        mean = volatility_series.rolling(252).mean()
        std = volatility_series.rolling(252).std()
        
        # 计算Z-score
        z_score = (volatility_series - mean) / std
        
        # 生成交易信号
        signals = pd.Series(0, index=volatility_series.index)
        signals[z_score < -z_threshold] = 1    # 低于阈值买入
        signals[z_score > z_threshold] = -1   # 高于阈值卖出
        
        return signals
    
    def calendar_effect_analysis(self, volatility_data, event_dates, window=5):
        """
        日历效应分析（重要事件前后波动率变化）
        
        参数:
        volatility_data (pd.DataFrame): 波动率数据（含日期和波动率）
        event_dates (list): 重要事件日期列表
        window (int): 事件窗口大小（交易日）
        
        返回:
        pd.DataFrame: 事件窗口内的平均波动率变化
        """
        results = []
        
        for event_date in event_dates:
            # 获取事件窗口
            start_date = event_date - pd.Timedelta(days=window)
            end_date = event_date + pd.Timedelta(days=window)
            
            # 提取窗口数据
            window_data = volatility_data.loc[start_date:end_date].copy()
            
            # 计算相对变化
            base_vol = window_data.loc[event_date - pd.Timedelta(days=window), 'volatility']
            window_data['vol_change'] = (window_data['volatility'] - base_vol) / base_vol
            
            # 标记事件日
            window_data['days_from_event'] = (window_data.index - event_date).days
            
            results.append(window_data)
        
        # 合并所有事件数据
        all_events = pd.concat(results)
        
        # 按事件日分组计算平均变化
        avg_changes = all_events.groupby('days_from_event')['vol_change'].mean()
        
        return avg_changes
    
    def market_volatility_timing(self, all_bonds_data):
        """
        市场波动率择时指标
        
        参数:
        all_bonds_data (pd.DataFrame): 全市场可转债数据
        
        返回:
        pd.Series: 择时信号（1:加仓, -1:减仓, 0:中性）
        """
        # 计算市场隐含波动率中位数
        market_iv = all_bonds_data.groupby('date')['implied_vol'].median()
        
        # 计算波动率分位数
        rolling_quantile = market_iv.rolling(252).apply(
            lambda x: np.percentile(x, 30), raw=False
        )
        
        # 生成择时信号
        signals = pd.Series(0, index=market_iv.index)
        
        # 当波动率低于30%分位数时加仓
        signals[market_iv < rolling_quantile] = 1
        
        # 当波动率高于70%分位数时减仓
        high_quantile = market_iv.rolling(252).apply(
            lambda x: np.percentile(x, 70), raw=False
        )
        signals[market_iv > high_quantile] = -1
        
        return signals
    
    def visualize_volatility(self, volatility_series, title="波动率走势"):
        """可视化波动率序列"""
        plt.figure(figsize=(12, 6))
        volatility_series.plot(title=title)
        plt.ylabel("波动率")
        plt.grid(True)
        plt.savefig(f"{title}.png")
        plt.close()

# 示例使用
if __name__ == "__main__":
    # 模拟数据
    np.random.seed(42)
    dates = pd.date_range('2020-01-01', '2023-12-31', freq='B')
    returns = 1 + np.random.normal(0.0005, 0.02, len(dates))
    price_series = np.cumprod(returns) * 100
    stock_prices = pd.Series(price_series, index=dates)
    
    # 创建策略实例
    strategy = VolatilityStrategy()
    
    # 计算历史波动率
    hist_vol = strategy.calculate_historical_volatility(stock_prices, method='garch')
    
    # 可视化
    strategy.visualize_volatility(hist_vol, "正股历史波动率")
    
    # 均值回归策略示例
    signals = strategy.mean_reversion_strategy(hist_vol)
    print("\n均值回归策略信号:")
    print(signals.tail())
