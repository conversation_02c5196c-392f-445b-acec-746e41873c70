import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.optimize import minimize
import warnings
import os
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression, <PERSON>, Lasso
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import StandardScaler
import matplotlib.dates as mdates
warnings.filterwarnings('ignore')

# 修复中文字体显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.figsize'] = (16, 10)
plt.rcParams['savefig.dpi'] = 300

# 创建输出文件夹
output_dir = '/Users/<USER>/Desktop/利率指标结果'
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

print("=" * 120)
print("🏦 调集所有算力 - 真实数据利率预测指标构建系统")
print("基于真实Excel数据构建周度频率领先利率预测指标")
print("=" * 120)

class RealInterestRateAnalysisSystem:
    """真实数据利率预测指标构建系统"""
    
    def __init__(self, file_path):
        """
        初始化利率预测系统
        """
        self.file_path = file_path
        self.daily_factors = None
        self.bond_yields = None
        self.gdp_data = None
        self.weekly_data = None
        self.leading_indicator = None
        self.prediction_results = {}
        self.factor_weights = {}
        self.optimal_leads = {}
        
        # 定义因子列表
        self.factor_names = [
            '北京:地铁客运量',
            '中国:票房收入:电影', 
            '中国:30大中城市:成交面积:商品房',
            'R007',
            'DR007', 
            'R007-DR007',
            '中国:逆回购利率:7天',
            '南华工业品指数',
            '期货持仓量(活跃合约):国债期货:10年期',
            '期货成交量:国债期货:10年期'
        ]
        
        # 交易日因子
        self.trading_day_factors = [
            'R007', 'DR007', 'R007-DR007', '南华工业品指数', 
            '期货持仓量(活跃合约):国债期货:10年期', '期货成交量:国债期货:10年期'
        ]
        
        print(f"初始化真实数据利率预测系统")
        print(f"目标因子数量: {len(self.factor_names)}")
        
    def load_real_data(self):
        """加载真实Excel数据"""
        print(f"\n{'='*80}")
        print("📊 加载真实Excel数据")
        print(f"{'='*80}")
        
        try:
            # 检查文件是否存在
            if not os.path.exists(self.file_path):
                print(f"✗ 文件不存在: {self.file_path}")
                return False
            
            # 读取三个sheet
            print("正在读取Excel文件...")
            
            # 读取日频数据
            self.daily_factors = pd.read_excel(self.file_path, sheet_name='日频数据')
            print(f"✓ 日频因子数据形状: {self.daily_factors.shape}")
            print(f"✓ 日频数据列名: {list(self.daily_factors.columns)}")
            
            # 读取债券收益率数据
            self.bond_yields = pd.read_excel(self.file_path, sheet_name=1)  # 第二个sheet
            print(f"✓ 债券收益率数据形状: {self.bond_yields.shape}")
            print(f"✓ 债券收益率列名: {list(self.bond_yields.columns)}")
            
            # 读取GDP数据
            self.gdp_data = pd.read_excel(self.file_path, sheet_name=2)     # 第三个sheet
            print(f"✓ GDP数据形状: {self.gdp_data.shape}")
            print(f"✓ GDP数据列名: {list(self.gdp_data.columns)}")
            
            return True
            
        except Exception as e:
            print(f"✗ 数据加载失败: {e}")
            return False
    
    def clean_and_process_data(self):
        """清洗和处理真实数据"""
        print(f"\n{'='*80}")
        print("🧹 清洗和处理真实数据")
        print(f"{'='*80}")
        
        # 处理日频因子数据
        self._clean_daily_factors()
        
        # 处理债券收益率数据
        self._clean_bond_yields()
        
        # 处理GDP数据
        self._clean_gdp_data()
        
        # 合并数据并确定时间范围
        self._merge_and_align_data()
        
    def _clean_daily_factors(self):
        """清洗日频因子数据"""
        print("清洗日频因子数据...")
        
        df = self.daily_factors.copy()
        
        # 确保日期列为datetime格式
        date_col = df.columns[0]  # 假设第一列是日期
        df[date_col] = pd.to_datetime(df[date_col], errors='coerce')
        df = df.rename(columns={date_col: '日期'})
        df = df.sort_values('日期').reset_index(drop=True)
        
        # 删除日期为空的行
        df = df.dropna(subset=['日期'])
        
        print(f"原始数据时间范围: {df['日期'].min()} 到 {df['日期'].max()}")
        
        # 检查因子列是否存在，并处理列名匹配问题
        available_factors = []
        factor_mapping = {}
        
        for factor in self.factor_names:
            # 尝试精确匹配
            if factor in df.columns:
                available_factors.append(factor)
                factor_mapping[factor] = factor
            else:
                # 尝试模糊匹配
                matched_col = None
                for col in df.columns:
                    if factor in col or col in factor:
                        matched_col = col
                        break
                
                if matched_col:
                    available_factors.append(factor)
                    factor_mapping[factor] = matched_col
                    print(f"✓ 因子匹配: '{factor}' -> '{matched_col}'")
                else:
                    print(f"⚠️ 因子 '{factor}' 未找到匹配列")
        
        # 更新因子名称列表
        self.factor_names = available_factors
        print(f"可用因子数量: {len(self.factor_names)}")
        
        # 重命名列
        for original_factor, mapped_col in factor_mapping.items():
            if mapped_col != original_factor:
                df = df.rename(columns={mapped_col: original_factor})
        
        # 标记交易日
        trading_day_mask = pd.Series(False, index=df.index)
        
        for factor in self.trading_day_factors:
            if factor in df.columns:
                trading_day_mask |= df[factor].notna()
        
        if trading_day_mask.any():
            df['is_trading_day'] = trading_day_mask
        else:
            # 如果没有交易日因子，基于日期判断（周一到周五）
            df['is_trading_day'] = df['日期'].dt.weekday < 5
        
        # 只保留交易日数据
        df_trading = df[df['is_trading_day']].copy()
        
        # 处理缺失值
        for factor in self.factor_names:
            if factor in df_trading.columns:
                if factor in self.trading_day_factors:
                    # 交易日因子保持原样
                    pass
                else:
                    # 全日期因子进行前向填充
                    df_trading[factor] = df_trading[factor].fillna(method='ffill')
        
        # 只保留有因子数据的列
        keep_cols = ['日期', 'is_trading_day'] + [f for f in self.factor_names if f in df_trading.columns]
        df_trading = df_trading[keep_cols].copy()
        
        # 删除仍有缺失值的行
        df_trading = df_trading.dropna(subset=self.factor_names)
        
        self.daily_factors = df_trading
        print(f"清洗后日频数据形状: {self.daily_factors.shape}")
        print(f"清洗后数据时间范围: {self.daily_factors['日期'].min()} 到 {self.daily_factors['日期'].max()}")
    
    def _clean_bond_yields(self):
        """清洗债券收益率数据"""
        print("清洗债券收益率数据...")
        
        df = self.bond_yields.copy()
        
        # 确保日期列为datetime格式
        date_col = df.columns[0]
        df[date_col] = pd.to_datetime(df[date_col], errors='coerce')
        df = df.rename(columns={date_col: '日期'})
        df = df.sort_values('日期').reset_index(drop=True)
        
        # 删除日期为空的行
        df = df.dropna(subset=['日期'])
        
        # 查找10y、1y收益率列和利差列
        yield_mapping = {}
        
        for col in df.columns:
            col_lower = col.lower()
            if '10' in col and ('年' in col or 'y' in col_lower) and ('收益率' in col or 'yield' in col_lower):
                yield_mapping['10y收益率'] = col
            elif '1' in col and ('年' in col or 'y' in col_lower) and ('收益率' in col or 'yield' in col_lower):
                yield_mapping['1y收益率'] = col
            elif ('10-1' in col or '10y-1y' in col_lower) and ('利差' in col or 'spread' in col_lower):
                yield_mapping['10-1y利差'] = col
        
        # 重命名列
        for standard_name, original_col in yield_mapping.items():
            df = df.rename(columns={original_col: standard_name})
            print(f"✓ 收益率匹配: '{standard_name}' -> '{original_col}'")
        
        # 如果没有利差列，计算利差
        if '10-1y利差' not in df.columns and '10y收益率' in df.columns and '1y收益率' in df.columns:
            df['10-1y利差'] = df['10y收益率'] - df['1y收益率']
            print("✓ 计算10-1y利差")
        
        # 只保留需要的列
        keep_cols = ['日期']
        for col in ['10y收益率', '1y收益率', '10-1y利差']:
            if col in df.columns:
                keep_cols.append(col)
        
        df = df[keep_cols].copy()
        df = df.dropna()
        
        self.bond_yields = df
        print(f"清洗后债券收益率数据形状: {self.bond_yields.shape}")
        print(f"可用收益率指标: {[col for col in df.columns if col != '日期']}")
        print(f"债券数据时间范围: {self.bond_yields['日期'].min()} 到 {self.bond_yields['日期'].max()}")
    
    def _clean_gdp_data(self):
        """清洗GDP数据"""
        print("清洗GDP数据...")
        
        df = self.gdp_data.copy()
        
        # 确保日期列为datetime格式
        date_col = df.columns[0]
        df[date_col] = pd.to_datetime(df[date_col], errors='coerce')
        df = df.rename(columns={date_col: '日期'})
        df = df.sort_values('日期').reset_index(drop=True)
        
        # 查找GDP同比增速列
        gdp_col = None
        for col in df.columns:
            if 'GDP' in col.upper() and ('同比' in col or '增速' in col or 'growth' in col.lower()):
                gdp_col = col
                break
        
        if gdp_col:
            df = df.rename(columns={gdp_col: 'GDP同比增速'})
            df = df[['日期', 'GDP同比增速']].copy()
            df = df.dropna()
            print(f"✓ GDP列匹配: 'GDP同比增速' -> '{gdp_col}'")
        else:
            print("⚠️ 未找到GDP同比增速列")
            df = pd.DataFrame(columns=['日期', 'GDP同比增速'])
        
        self.gdp_data = df
        print(f"清洗后GDP数据形状: {self.gdp_data.shape}")
    
    def _merge_and_align_data(self):
        """合并和对齐数据，确定分析时间范围"""
        print("合并和对齐数据...")
        
        # 确定重叠的时间范围
        factor_start = self.daily_factors['日期'].min()
        factor_end = self.daily_factors['日期'].max()
        bond_start = self.bond_yields['日期'].min()
        bond_end = self.bond_yields['日期'].max()
        
        # 选择重叠时间范围
        analysis_start = max(factor_start, bond_start)
        analysis_end = min(factor_end, bond_end)
        
        print(f"因子数据范围: {factor_start} 到 {factor_end}")
        print(f"债券数据范围: {bond_start} 到 {bond_end}")
        print(f"分析时间范围: {analysis_start} 到 {analysis_end}")
        
        # 筛选数据到分析时间范围
        self.daily_factors = self.daily_factors[
            (self.daily_factors['日期'] >= analysis_start) & 
            (self.daily_factors['日期'] <= analysis_end)
        ].copy()
        
        self.bond_yields = self.bond_yields[
            (self.bond_yields['日期'] >= analysis_start) & 
            (self.bond_yields['日期'] <= analysis_end)
        ].copy()
        
        print(f"筛选后因子数据: {len(self.daily_factors)}条")
        print(f"筛选后债券数据: {len(self.bond_yields)}条")
    
    def create_weekly_data(self):
        """创建周度数据"""
        print(f"\n{'='*80}")
        print("📅 创建周度数据")
        print(f"{'='*80}")
        
        # 设置日期为索引
        df_daily = self.daily_factors.set_index('日期')
        
        # 添加周标识（ISO周）
        df_daily['week'] = df_daily.index.to_series().dt.isocalendar().week
        df_daily['year'] = df_daily.index.to_series().dt.year
        df_daily['year_week'] = df_daily['year'].astype(str) + '_' + df_daily['week'].astype(str).str.zfill(2)
        
        # 定义聚合方法
        agg_methods = {}
        for factor in self.factor_names:
            if factor in ['北京:地铁客运量', '中国:票房收入:电影', '中国:30大中城市:成交面积:商品房']:
                agg_methods[factor] = 'sum'  # 累计指标
            elif factor in ['期货持仓量(活跃合约):国债期货:10年期', '期货成交量:国债期货:10年期']:
                agg_methods[factor] = 'mean'  # 平均值
            else:
                agg_methods[factor] = 'last'  # 期末值
        
        # 按年周聚合
        df_weekly = df_daily.groupby('year_week').agg(agg_methods)
        
        # 添加周末日期
        week_end_dates = df_daily.groupby('year_week').apply(lambda x: x.index.max())
        df_weekly['日期'] = week_end_dates
        
        df_weekly.reset_index(inplace=True)
        df_weekly = df_weekly.drop('year_week', axis=1)
        
        # 删除包含NaN的行
        df_weekly = df_weekly.dropna(subset=self.factor_names)
        
        # 按日期排序
        df_weekly = df_weekly.sort_values('日期').reset_index(drop=True)
        
        self.weekly_data = df_weekly
        print(f"周度数据形状: {self.weekly_data.shape}")
        print(f"周度数据时间范围: {self.weekly_data['日期'].min()} 到 {self.weekly_data['日期'].max()}")
        
        # 显示周度数据统计
        print("\n周度因子统计:")
        for factor in self.factor_names:
            if factor in self.weekly_data.columns:
                mean_val = self.weekly_data[factor].mean()
                std_val = self.weekly_data[factor].std()
                print(f"  {factor}: 均值={mean_val:.2f}, 标准差={std_val:.2f}")
    
    def optimize_factor_weights(self):
        """优化因子权重"""
        print(f"\n{'='*80}")
        print("⚖️ 优化因子权重")
        print(f"{'='*80}")
        
        # 合并周度数据和债券收益率数据
        merged_data = pd.merge(self.weekly_data, self.bond_yields, on='日期', how='inner')
        
        if len(merged_data) < 20:
            print("⚠️ 合并后数据量不足，使用等权重")
            weights = np.ones(len(self.factor_names)) / len(self.factor_names)
            self.factor_weights = dict(zip(self.factor_names, weights))
            return
        
        print(f"合并后数据量: {len(merged_data)}")
        
        # 标准化因子
        scaler = StandardScaler()
        X = merged_data[self.factor_names].values
        X_scaled = scaler.fit_transform(X)
        
        # 目标变量
        target_cols = [col for col in ['10y收益率', '1y收益率', '10-1y利差'] if col in merged_data.columns]
        
        if not target_cols:
            print("⚠️ 未找到目标变量，使用等权重")
            weights = np.ones(len(self.factor_names)) / len(self.factor_names)
            self.factor_weights = dict(zip(self.factor_names, weights))
            return
        
        # 使用多种方法优化权重
        best_weights = self._optimize_weights_comprehensive(X_scaled, merged_data[target_cols].values)
        
        self.factor_weights = dict(zip(self.factor_names, best_weights))
        
        print("优化后的因子权重:")
        for factor, weight in self.factor_weights.items():
            print(f"  {factor}: {weight:.4f}")
        
        # 保存权重到文件
        weights_df = pd.DataFrame(list(self.factor_weights.items()), 
                                columns=['因子名称', '权重'])
        weights_df.to_excel(f'{output_dir}/因子权重.xlsx', index=False)
        print("✓ 因子权重已保存到Excel文件")
    
    def _optimize_weights_comprehensive(self, X, y):
        """综合方法优化权重"""
        from sklearn.decomposition import PCA
        
        # 方法1: 基于与目标变量的相关性
        correlations = []
        for i in range(X.shape[1]):
            corr_sum = 0
            for j in range(y.shape[1]):
                corr = np.corrcoef(X[:, i], y[:, j])[0, 1]
                if not np.isnan(corr):
                    corr_sum += abs(corr)
            correlations.append(corr_sum)
        
        correlations = np.array(correlations)
        if correlations.sum() > 0:
            corr_weights = correlations / correlations.sum()
        else:
            corr_weights = np.ones(len(correlations)) / len(correlations)
        
        # 方法2: 基于PCA的权重
        try:
            pca = PCA(n_components=min(3, X.shape[1]))
            pca.fit(X)
            pca_weights = np.abs(pca.components_[0])  # 使用第一主成分
            pca_weights = pca_weights / pca_weights.sum()
        except:
            pca_weights = np.ones(X.shape[1]) / X.shape[1]
        
        # 方法3: 基于随机森林的特征重要性
        try:
            rf = RandomForestRegressor(n_estimators=100, random_state=42)
            rf.fit(X, y.mean(axis=1))  # 使用目标变量的平均值
            rf_weights = rf.feature_importances_
        except:
            rf_weights = np.ones(X.shape[1]) / X.shape[1]
        
        # 方法4: 基于经济学直觉的权重
        econ_weights = self._get_economic_weights()
        
        # 综合权重
        final_weights = 0.3 * corr_weights + 0.25 * pca_weights + 0.25 * rf_weights + 0.2 * econ_weights
        final_weights = final_weights / final_weights.sum()
        
        return final_weights
    
    def _get_economic_weights(self):
        """基于经济学直觉的权重"""
        # 基于经济理论的权重分配
        econ_weights_dict = {
            'R007': 0.18,  # 核心资金面指标
            'DR007': 0.15,  # 重要资金面指标
            'R007-DR007': 0.12,  # 资金面利差
            '中国:逆回购利率:7天': 0.15,  # 政策利率
            '南华工业品指数': 0.12,  # 通胀预期
            '期货持仓量(活跃合约):国债期货:10年期': 0.08,  # 市场情绪
            '期货成交量:国债期货:10年期': 0.08,  # 市场活跃度
            '北京:地铁客运量': 0.04,  # 经济活跃度
            '中国:票房收入:电影': 0.04,  # 消费指标
            '中国:30大中城市:成交面积:商品房': 0.04  # 房地产指标
        }
        
        weights = []
        for factor in self.factor_names:
            weights.append(econ_weights_dict.get(factor, 1.0/len(self.factor_names)))
        
        weights = np.array(weights)
        return weights / weights.sum()

    def construct_leading_indicator(self):
        """构建领先指标"""
        print(f"\n{'='*80}")
        print("🔮 构建领先指标")
        print(f"{'='*80}")

        # 标准化因子
        scaler = StandardScaler()
        factor_data = self.weekly_data[self.factor_names].values
        factor_data_scaled = scaler.fit_transform(factor_data)

        # 计算加权领先指标
        weights = np.array([self.factor_weights[factor] for factor in self.factor_names])
        leading_indicator_values = np.dot(factor_data_scaled, weights)

        # 创建领先指标数据框
        self.leading_indicator = pd.DataFrame({
            '日期': self.weekly_data['日期'],
            '领先指标': leading_indicator_values
        })

        print(f"领先指标构建完成，数据量: {len(self.leading_indicator)}")
        print(f"领先指标统计: 均值={self.leading_indicator['领先指标'].mean():.4f}, 标准差={self.leading_indicator['领先指标'].std():.4f}")

        # 保存领先指标
        self.leading_indicator.to_excel(f'{output_dir}/周度领先指标.xlsx', index=False)
        print("✓ 领先指标已保存到Excel文件")

    def find_optimal_lead_time(self):
        """寻找最优领先时间"""
        print(f"\n{'='*80}")
        print("⏰ 寻找最优领先时间")
        print(f"{'='*80}")

        # 合并领先指标和债券收益率数据
        merged_data = pd.merge(self.leading_indicator, self.bond_yields, on='日期', how='inner')

        if len(merged_data) < 20:
            print("⚠️ 合并后数据量不足")
            return

        target_cols = [col for col in ['10y收益率', '1y收益率', '10-1y利差'] if col in merged_data.columns]

        # 测试不同的领先时间（1-12周）
        lead_times = range(1, 13)
        optimal_leads = {}

        for target in target_cols:
            print(f"\n分析 {target} 的最优领先时间:")

            best_corr = 0
            best_lead = 1
            correlations = []
            p_values = []

            for lead in lead_times:
                # 创建领先数据
                if lead < len(merged_data):
                    indicator_lead = merged_data['领先指标'].iloc[:-lead].values
                    target_current = merged_data[target].iloc[lead:].values

                    # 计算相关性和显著性
                    if len(indicator_lead) > 10:
                        corr, p_val = stats.pearsonr(indicator_lead, target_current)
                        correlations.append(abs(corr))
                        p_values.append(p_val)

                        if abs(corr) > best_corr:
                            best_corr = abs(corr)
                            best_lead = lead
                    else:
                        correlations.append(0)
                        p_values.append(1)
                else:
                    correlations.append(0)
                    p_values.append(1)

            optimal_leads[target] = {
                'best_lead': best_lead,
                'best_corr': best_corr,
                'all_correlations': correlations,
                'p_values': p_values
            }

            print(f"  最优领先时间: {best_lead}周")
            print(f"  最高相关性: {best_corr:.4f}")
            print(f"  显著性p值: {p_values[best_lead-1]:.4f}")

        self.optimal_leads = optimal_leads

        # 保存最优领先时间结果
        lead_results = []
        for target, info in optimal_leads.items():
            lead_results.append({
                '目标变量': target,
                '最优领先时间(周)': info['best_lead'],
                '最高相关性': info['best_corr'],
                'p值': info['p_values'][info['best_lead']-1]
            })

        lead_df = pd.DataFrame(lead_results)
        lead_df.to_excel(f'{output_dir}/最优领先时间.xlsx', index=False)
        print("✓ 最优领先时间已保存到Excel文件")

        return optimal_leads

    def perform_prediction_analysis(self):
        """执行预测分析"""
        print(f"\n{'='*80}")
        print("📈 执行预测分析")
        print(f"{'='*80}")

        # 合并数据
        merged_data = pd.merge(self.leading_indicator, self.bond_yields, on='日期', how='inner')
        target_cols = [col for col in ['10y收益率', '1y收益率', '10-1y利差'] if col in merged_data.columns]

        prediction_results = {}

        for target in target_cols:
            print(f"\n预测分析: {target}")

            lead_time = self.optimal_leads[target]['best_lead']

            # 准备数据
            if lead_time < len(merged_data):
                X = merged_data['领先指标'].iloc[:-lead_time].values.reshape(-1, 1)
                y = merged_data[target].iloc[lead_time:].values
                dates = merged_data['日期'].iloc[lead_time:].values

                # 分割训练和测试数据
                train_size = int(len(X) * 0.8)
                X_train, X_test = X[:train_size], X[train_size:]
                y_train, y_test = y[:train_size], y[train_size:]
                dates_test = dates[train_size:]

                # 多种预测模型
                models = {
                    'Linear Regression': LinearRegression(),
                    'Ridge Regression': Ridge(alpha=1.0),
                    'Random Forest': RandomForestRegressor(n_estimators=100, random_state=42)
                }

                model_results = {}

                for model_name, model in models.items():
                    # 训练模型
                    model.fit(X_train, y_train)
                    y_pred = model.predict(X_test)

                    # 计算评估指标
                    mse = mean_squared_error(y_test, y_pred)
                    rmse = np.sqrt(mse)
                    mae = mean_absolute_error(y_test, y_pred)
                    r2 = r2_score(y_test, y_pred)

                    # 计算方向准确率
                    if len(y_test) > 1:
                        actual_direction = np.diff(y_test) > 0
                        pred_direction = np.diff(y_pred) > 0
                        direction_accuracy = np.mean(actual_direction == pred_direction)
                    else:
                        direction_accuracy = 0

                    model_results[model_name] = {
                        'mse': mse,
                        'rmse': rmse,
                        'mae': mae,
                        'r2': r2,
                        'direction_accuracy': direction_accuracy,
                        'predictions': y_pred,
                        'actual': y_test,
                        'dates': dates_test
                    }

                    print(f"  {model_name}:")
                    print(f"    RMSE: {rmse:.4f}")
                    print(f"    MAE: {mae:.4f}")
                    print(f"    R²: {r2:.4f}")
                    print(f"    方向准确率: {direction_accuracy:.4f}")

                prediction_results[target] = {
                    'lead_time': lead_time,
                    'models': model_results,
                    'X_test': X_test,
                    'y_test': y_test,
                    'dates_test': dates_test
                }

        self.prediction_results = prediction_results

        # 保存预测结果
        self._save_prediction_results()

        return prediction_results

    def _save_prediction_results(self):
        """保存预测结果到Excel"""
        prediction_summary = []
        detailed_predictions = {}

        for target, results in self.prediction_results.items():
            for model_name, metrics in results['models'].items():
                prediction_summary.append({
                    '目标变量': target,
                    '模型': model_name,
                    '领先时间(周)': results['lead_time'],
                    'RMSE': metrics['rmse'],
                    'MAE': metrics['mae'],
                    'R²': metrics['r2'],
                    '方向准确率': metrics['direction_accuracy']
                })

                # 详细预测结果
                if model_name == 'Linear Regression':  # 只保存线性回归的详细结果
                    detailed_predictions[f'{target}_预测'] = pd.DataFrame({
                        '日期': metrics['dates'],
                        '实际值': metrics['actual'],
                        '预测值': metrics['predictions'],
                        '误差': metrics['actual'] - metrics['predictions']
                    })

        # 保存汇总结果
        prediction_df = pd.DataFrame(prediction_summary)

        with pd.ExcelWriter(f'{output_dir}/预测分析结果.xlsx') as writer:
            prediction_df.to_excel(writer, sheet_name='预测结果汇总', index=False)

            for sheet_name, df in detailed_predictions.items():
                df.to_excel(writer, sheet_name=sheet_name, index=False)

        print("✓ 预测分析结果已保存到Excel文件")

    def create_comprehensive_visualizations(self):
        """创建全面的可视化图表"""
        print(f"\n{'='*80}")
        print("📊 创建全面的可视化图表")
        print(f"{'='*80}")

        # 1. 因子权重图
        self._plot_factor_weights()

        # 2. 领先指标时间序列图
        self._plot_leading_indicator_series()

        # 3. 最优领先时间图
        self._plot_optimal_lead_times()

        # 4. 预测效果图
        self._plot_prediction_results()

        # 5. 相关性分析图
        self._plot_correlation_analysis()

        # 6. 因子贡献度分析图
        self._plot_factor_contribution()

    def _plot_factor_weights(self):
        """绘制因子权重图"""
        plt.figure(figsize=(16, 10))

        factors = list(self.factor_weights.keys())
        weights = list(self.factor_weights.values())

        # 按权重排序
        sorted_indices = np.argsort(weights)[::-1]
        factors_sorted = [factors[i] for i in sorted_indices]
        weights_sorted = [weights[i] for i in sorted_indices]

        # 创建颜色映射
        colors = plt.cm.viridis(np.linspace(0, 1, len(factors)))

        bars = plt.bar(range(len(factors_sorted)), weights_sorted, color=colors)
        plt.xlabel('因子', fontsize=14, fontweight='bold')
        plt.ylabel('权重', fontsize=14, fontweight='bold')
        plt.title('利率预测因子权重分布（按重要性排序）', fontsize=16, fontweight='bold')
        plt.xticks(range(len(factors_sorted)), factors_sorted, rotation=45, ha='right')
        plt.grid(True, alpha=0.3)

        # 添加数值标签
        for i, (bar, weight) in enumerate(zip(bars, weights_sorted)):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.002,
                    f'{weight:.3f}', ha='center', va='bottom', fontsize=10)

        plt.tight_layout()
        plt.savefig(f'{output_dir}/因子权重分布图.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✓ 因子权重分布图.png")

    def _plot_leading_indicator_series(self):
        """绘制领先指标时间序列图"""
        # 合并数据用于绘图
        merged_data = pd.merge(self.leading_indicator, self.bond_yields, on='日期', how='inner')

        # 创建子图
        fig, axes = plt.subplots(2, 1, figsize=(18, 12))

        # 上图：领先指标
        axes[0].plot(merged_data['日期'], merged_data['领先指标'],
                    linewidth=2, color='blue', label='周度利率领先指标')
        axes[0].set_ylabel('领先指标值', fontsize=12, fontweight='bold')
        axes[0].set_title('周度利率领先指标时间序列', fontsize=14, fontweight='bold')
        axes[0].grid(True, alpha=0.3)
        axes[0].legend()

        # 格式化x轴日期
        axes[0].xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        axes[0].xaxis.set_major_locator(mdates.MonthLocator(interval=6))

        # 下图：债券收益率
        target_cols = [col for col in ['10y收益率', '1y收益率', '10-1y利差'] if col in merged_data.columns]
        colors = ['red', 'green', 'orange']

        for i, target in enumerate(target_cols):
            axes[1].plot(merged_data['日期'], merged_data[target],
                        linewidth=2, color=colors[i], label=target)

        axes[1].set_xlabel('日期', fontsize=12, fontweight='bold')
        axes[1].set_ylabel('收益率 (%)', fontsize=12, fontweight='bold')
        axes[1].set_title('债券收益率时间序列', fontsize=14, fontweight='bold')
        axes[1].grid(True, alpha=0.3)
        axes[1].legend()

        # 格式化x轴日期
        axes[1].xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        axes[1].xaxis.set_major_locator(mdates.MonthLocator(interval=6))

        plt.tight_layout()
        plt.savefig(f'{output_dir}/领先指标时间序列图.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✓ 领先指标时间序列图.png")

    def _plot_optimal_lead_times(self):
        """绘制最优领先时间图"""
        target_cols = list(self.optimal_leads.keys())
        lead_times = range(1, 13)

        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        axes = axes.flatten()

        for i, target in enumerate(target_cols):
            if i < len(axes):
                correlations = self.optimal_leads[target]['all_correlations']
                best_lead = self.optimal_leads[target]['best_lead']

                axes[i].plot(lead_times, correlations, 'o-', linewidth=2, markersize=6, color='blue')
                axes[i].axvline(x=best_lead, color='red', linestyle='--', alpha=0.7,
                               label=f'最优领先时间: {best_lead}周')
                axes[i].set_xlabel('领先时间 (周)', fontsize=12)
                axes[i].set_ylabel('相关系数 (绝对值)', fontsize=12)
                axes[i].set_title(f'{target} - 领先时间分析', fontsize=13, fontweight='bold')
                axes[i].grid(True, alpha=0.3)
                axes[i].legend()
                axes[i].set_ylim(0, max(correlations) * 1.1)

        # 隐藏多余的子图
        for i in range(len(target_cols), len(axes)):
            axes[i].set_visible(False)

        plt.tight_layout()
        plt.savefig(f'{output_dir}/最优领先时间分析图.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✓ 最优领先时间分析图.png")

    def _plot_prediction_results(self):
        """绘制预测结果图"""
        target_cols = list(self.prediction_results.keys())

        for target in target_cols:
            fig, axes = plt.subplots(2, 2, figsize=(18, 14))
            fig.suptitle(f'{target} 预测结果分析', fontsize=16, fontweight='bold')

            models = self.prediction_results[target]['models']
            dates_test = self.prediction_results[target]['dates_test']

            model_names = list(models.keys())

            # 时间序列对比图
            axes[0, 0].plot(dates_test, models['Linear Regression']['actual'],
                           'b-', linewidth=2, label='实际值')
            axes[0, 0].plot(dates_test, models['Linear Regression']['predictions'],
                           'r--', linewidth=2, label='预测值')
            axes[0, 0].set_title('预测vs实际时间序列', fontsize=13)
            axes[0, 0].legend()
            axes[0, 0].grid(True, alpha=0.3)
            axes[0, 0].xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))

            # 散点图
            for i, model_name in enumerate(model_names[:3]):
                row = (i + 1) // 2
                col = (i + 1) % 2

                y_actual = models[model_name]['actual']
                y_pred = models[model_name]['predictions']
                r2 = models[model_name]['r2']

                axes[row, col].scatter(y_actual, y_pred, alpha=0.6, s=30)
                axes[row, col].plot([y_actual.min(), y_actual.max()],
                                   [y_actual.min(), y_actual.max()], 'r--', linewidth=2)
                axes[row, col].set_xlabel('实际值', fontsize=12)
                axes[row, col].set_ylabel('预测值', fontsize=12)
                axes[row, col].set_title(f'{model_name} (R² = {r2:.3f})', fontsize=13)
                axes[row, col].grid(True, alpha=0.3)

            plt.tight_layout()
            plt.savefig(f'{output_dir}/{target}_预测结果图.png', dpi=300, bbox_inches='tight')
            plt.close()
            print(f"✓ {target}_预测结果图.png")

    def _plot_correlation_analysis(self):
        """绘制相关性分析图"""
        # 合并所有数据
        merged_data = pd.merge(self.weekly_data, self.bond_yields, on='日期', how='inner')
        merged_data = pd.merge(merged_data, self.leading_indicator, on='日期', how='inner')

        # 选择关键列进行相关性分析
        corr_cols = self.factor_names + ['领先指标']
        target_cols = [col for col in ['10y收益率', '1y收益率', '10-1y利差'] if col in merged_data.columns]
        corr_cols.extend(target_cols)

        corr_matrix = merged_data[corr_cols].corr()

        plt.figure(figsize=(16, 14))
        mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
        sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='coolwarm', center=0,
                   square=True, linewidths=0.5, cbar_kws={"shrink": .8}, fmt='.2f',
                   annot_kws={'size': 8})
        plt.title('因子与收益率相关性热力图', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(f'{output_dir}/相关性热力图.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✓ 相关性热力图.png")

    def _plot_factor_contribution(self):
        """绘制因子贡献度分析图"""
        # 计算每个因子对领先指标的贡献
        factor_data_scaled = StandardScaler().fit_transform(self.weekly_data[self.factor_names])
        contributions = {}

        for i, factor in enumerate(self.factor_names):
            weight = self.factor_weights[factor]
            factor_values = factor_data_scaled[:, i]
            contribution = factor_values * weight
            contributions[factor] = contribution

        # 创建贡献度时间序列图
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(18, 12))

        # 上图：各因子贡献度堆叠图
        dates = self.weekly_data['日期']
        bottom = np.zeros(len(dates))

        colors = plt.cm.Set3(np.linspace(0, 1, len(self.factor_names)))

        for i, (factor, contribution) in enumerate(contributions.items()):
            ax1.fill_between(dates, bottom, bottom + contribution,
                           label=factor, alpha=0.7, color=colors[i])
            bottom += contribution

        ax1.plot(dates, self.leading_indicator['领先指标'], 'k-', linewidth=2, label='总领先指标')
        ax1.set_ylabel('贡献度', fontsize=12, fontweight='bold')
        ax1.set_title('各因子对领先指标的贡献度分解', fontsize=14, fontweight='bold')
        ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax1.grid(True, alpha=0.3)

        # 下图：因子权重饼图
        weights = [self.factor_weights[factor] for factor in self.factor_names]
        ax2.pie(weights, labels=self.factor_names, autopct='%1.1f%%', startangle=90)
        ax2.set_title('因子权重分布', fontsize=14, fontweight='bold')

        plt.tight_layout()
        plt.savefig(f'{output_dir}/因子贡献度分析图.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✓ 因子贡献度分析图.png")

    def save_all_results(self):
        """保存所有结果到Excel"""
        print(f"\n{'='*80}")
        print("💾 保存所有结果到Excel")
        print(f"{'='*80}")

        with pd.ExcelWriter(f'{output_dir}/完整分析结果.xlsx') as writer:

            # 1. 清洗后的数据
            self.daily_factors.to_excel(writer, sheet_name='清洗后日频数据', index=False)
            self.weekly_data.to_excel(writer, sheet_name='周度数据', index=False)
            self.bond_yields.to_excel(writer, sheet_name='债券收益率数据', index=False)

            # 2. 因子权重
            weights_df = pd.DataFrame(list(self.factor_weights.items()),
                                    columns=['因子名称', '权重'])
            weights_df.to_excel(writer, sheet_name='因子权重', index=False)

            # 3. 领先指标
            self.leading_indicator.to_excel(writer, sheet_name='领先指标', index=False)

            # 4. 最优领先时间
            lead_times_data = []
            for target, info in self.optimal_leads.items():
                lead_times_data.append({
                    '目标变量': target,
                    '最优领先时间(周)': info['best_lead'],
                    '最高相关性': info['best_corr'],
                    'p值': info['p_values'][info['best_lead']-1] if info['p_values'] else 'N/A'
                })
            lead_times_df = pd.DataFrame(lead_times_data)
            lead_times_df.to_excel(writer, sheet_name='最优领先时间', index=False)

            # 5. 预测结果汇总
            if self.prediction_results:
                prediction_summary = []
                for target, results in self.prediction_results.items():
                    for model_name, metrics in results['models'].items():
                        prediction_summary.append({
                            '目标变量': target,
                            '模型': model_name,
                            '领先时间(周)': results['lead_time'],
                            'RMSE': metrics['rmse'],
                            'MAE': metrics['mae'],
                            'R²': metrics['r2'],
                            '方向准确率': metrics['direction_accuracy']
                        })

                prediction_df = pd.DataFrame(prediction_summary)
                prediction_df.to_excel(writer, sheet_name='预测结果汇总', index=False)

        print("✓ 完整分析结果.xlsx")

    def run_complete_analysis(self):
        """运行完整分析"""
        print("🚀 开始完整的真实数据分析流程...")

        # 1. 加载真实数据
        if not self.load_real_data():
            return False

        # 2. 清洗和处理数据
        self.clean_and_process_data()

        # 3. 创建周度数据
        self.create_weekly_data()

        # 4. 优化因子权重
        self.optimize_factor_weights()

        # 5. 构建领先指标
        self.construct_leading_indicator()

        # 6. 寻找最优领先时间
        self.find_optimal_lead_time()

        # 7. 执行预测分析
        self.perform_prediction_analysis()

        # 8. 创建可视化
        self.create_comprehensive_visualizations()

        # 9. 保存所有结果
        self.save_all_results()

        print(f"\n{'='*120}")
        print("🎉 完整的真实数据分析流程完成！")
        print(f"{'='*120}")

        return True

# 运行分析
if __name__ == "__main__":
    # 初始化系统
    file_path = '/Users/<USER>/Desktop/利率指标底稿.xlsx'
    system = RealInterestRateAnalysisSystem(file_path)

    # 运行完整分析
    success = system.run_complete_analysis()

    if success:
        print(f"\n📁 所有结果已保存到: {output_dir}")
        print("📊 生成的文件:")
        print("  Excel文件:")
        print("    - 完整分析结果.xlsx (所有数据和结果)")
        print("    - 因子权重.xlsx (因子权重分配)")
        print("    - 周度领先指标.xlsx (构建的领先指标)")
        print("    - 最优领先时间.xlsx (最优领先时间分析)")
        print("    - 预测分析结果.xlsx (预测模型结果)")
        print("  图表文件:")
        print("    - 因子权重分布图.png")
        print("    - 领先指标时间序列图.png")
        print("    - 最优领先时间分析图.png")
        print("    - 各目标变量预测结果图.png")
        print("    - 相关性热力图.png")
        print("    - 因子贡献度分析图.png")
    else:
        print("❌ 分析过程中出现错误，请检查数据文件")
