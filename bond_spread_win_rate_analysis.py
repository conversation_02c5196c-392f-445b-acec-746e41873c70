import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
import os
from itertools import combinations
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['savefig.dpi'] = 300

# 创建输出文件夹
output_dir = '/Users/<USER>/Desktop/量化择时胜率'
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

print("=" * 80)
print("30-10y利差量化预测策略深度分析")
print("作为固收量化专家的专业分析")
print("=" * 80)

# 加载数据
file_path = '/Users/<USER>/Desktop/择时胜率.xlsx'
try:
    print("正在加载数据...")
    df = pd.read_excel(file_path)
    print(f"✓ 数据加载成功")
    print(f"数据形状: {df.shape}")
    print(f"预期样本数量: 346个交易日")

    if df.shape[0] != 346:
        print(f"⚠️  实际样本数量({df.shape[0]})与预期不符，请检查")
    else:
        print("✓ 样本数量符合预期")

except Exception as e:
    print(f"✗ 数据加载失败: {e}")
    exit()

# 显示数据基本信息
print(f"\n数据列名: {list(df.columns)}")

# 定义因子分类
factor_categories = {
    '基本面因子': ['水泥价格指数', '建材综合指数', '南华金属指数', '南华能化指数', '南华工业品指数'],
    '技术面因子': ['偏离度（偏离均线）', '偏离度（偏离MA+std）'],
    '资金面因子': ['R007', 'DR007', '成交量:DR007', '成交量:R007'],
    '权益市场因子': ['1/沪深300PE-10y'],
    '成交因子': ['30Y成交量']
}

# 所有预测因子
all_factors = []
for factors in factor_categories.values():
    all_factors.extend(factors)

# 目标变量
target_var = '30-10y'
target_30y = '30y'

print(f"\n因子分类验证:")
total_available = 0
for category, factors in factor_categories.items():
    available_factors = [f for f in factors if f in df.columns]
    missing_factors = [f for f in factors if f not in df.columns]
    total_available += len(available_factors)
    print(f"{category}:")
    print(f"  可用因子({len(available_factors)}): {available_factors}")
    if missing_factors:
        print(f"  缺失因子({len(missing_factors)}): {missing_factors}")

print(f"\n总计可用因子数量: {total_available}")

def preprocess_data_for_prediction(df):
    """数据预处理 - 专门用于预测分析"""
    print(f"\n{'='*50}")
    print("数据预处理与目标变量构建")
    print(f"{'='*50}")

    # 检查缺失值
    missing_stats = df.isnull().sum()
    print(f"缺失值检查:")
    missing_cols = missing_stats[missing_stats > 0]
    if len(missing_cols) > 0:
        for col, missing in missing_cols.items():
            print(f"  {col}: {missing}个 ({missing/len(df)*100:.1f}%)")
    else:
        print("  ✓ 无缺失值")

    # 处理缺失值
    df_processed = df.fillna(method='ffill').fillna(method='bfill').copy()

    # 确保第一列是日期
    if 'Unnamed: 0' in df_processed.columns:
        df_processed = df_processed.rename(columns={'Unnamed: 0': '日期'})

    # 构建预测目标：下一日利差变化方向
    df_processed['30-10y_next'] = df_processed[target_var].shift(-1)  # 下一日利差
    df_processed['30-10y_change'] = df_processed['30-10y_next'] - df_processed[target_var]  # 利差变化

    # 目标变量：1=走阔(下一日利差>当日利差), 0=收窄(下一日利差<=当日利差)
    df_processed['direction_next'] = (df_processed['30-10y_change'] > 0).astype(int)

    # 同样构建30y收益率变化
    df_processed['30y_next'] = df_processed[target_30y].shift(-1)
    df_processed['30y_change'] = df_processed['30y_next'] - df_processed[target_30y]
    df_processed['30y_direction_next'] = (df_processed['30y_change'] > 0).astype(int)

    # 删除最后一行（因为shift(-1)后为NaN）
    df_processed = df_processed[:-1].copy()

    # 统计目标变量分布
    widen_days = df_processed['direction_next'].sum()
    narrow_days = len(df_processed) - widen_days
    widen_prob = df_processed['direction_next'].mean()

    print(f"\n利差变化统计 (基于{len(df_processed)}个有效样本):")
    print(f"  利差走阔天数: {widen_days}天")
    print(f"  利差收窄天数: {narrow_days}天")
    print(f"  走阔概率: {widen_prob:.3f} ({widen_prob*100:.1f}%)")
    print(f"  收窄概率: {1-widen_prob:.3f} ({(1-widen_prob)*100:.1f}%)")

    # 利差基本统计
    print(f"\n30-10y利差基本统计:")
    print(f"  均值: {df_processed[target_var].mean():.2f}bp")
    print(f"  标准差: {df_processed[target_var].std():.2f}bp")
    print(f"  最小值: {df_processed[target_var].min():.2f}bp")
    print(f"  最大值: {df_processed[target_var].max():.2f}bp")
    print(f"  中位数: {df_processed[target_var].median():.2f}bp")

    return df_processed

df_processed = preprocess_data_for_prediction(df)

def calculate_single_factor_win_rate(df, factor, target='direction_next'):
    """计算单因子预测胜率"""

    if factor not in df.columns:
        return None

    # 基于因子分位数的预测策略
    factor_data = df[factor].dropna()
    target_data = df[target].dropna()

    # 确保数据对齐
    valid_idx = ~(df[factor].isna() | df[target].isna())
    factor_values = df.loc[valid_idx, factor]
    target_values = df.loc[valid_idx, target]

    if len(factor_values) < 10:  # 数据太少
        return None

    # 计算相关性
    correlation = factor_values.corr(target_values)

    # 计算统计显著性
    try:
        _, p_value = stats.pearsonr(factor_values, target_values)
    except:
        p_value = 1.0

    # 基于中位数分组的预测策略
    factor_median = factor_values.median()

    # 高因子组和低因子组
    high_factor_mask = factor_values > factor_median
    low_factor_mask = factor_values <= factor_median

    high_group_target = target_values[high_factor_mask]
    low_group_target = target_values[low_factor_mask]

    # 各组走阔概率
    high_prob = high_group_target.mean() if len(high_group_target) > 0 else 0.5
    low_prob = low_group_target.mean() if len(low_group_target) > 0 else 0.5

    # 预测策略：基于相关性方向
    if correlation > 0:
        # 正相关：高因子值预测走阔，低因子值预测收窄
        high_prediction = 1  # 预测走阔
        low_prediction = 0   # 预测收窄
    else:
        # 负相关：高因子值预测收窄，低因子值预测走阔
        high_prediction = 0  # 预测收窄
        low_prediction = 1   # 预测走阔

    # 计算预测准确率
    high_correct = (high_group_target == high_prediction).sum()
    low_correct = (low_group_target == low_prediction).sum()
    total_correct = high_correct + low_correct
    total_samples = len(target_values)

    win_rate = total_correct / total_samples if total_samples > 0 else 0

    # 计算更详细的统计
    high_accuracy = (high_group_target == high_prediction).mean() if len(high_group_target) > 0 else 0.5
    low_accuracy = (low_group_target == low_prediction).mean() if len(low_group_target) > 0 else 0.5

    return {
        'factor': factor,
        'correlation': correlation,
        'p_value': p_value,
        'significant': p_value < 0.05,
        'win_rate': win_rate,
        'high_group_prob': high_prob,
        'low_group_prob': low_prob,
        'high_group_accuracy': high_accuracy,
        'low_group_accuracy': low_accuracy,
        'high_group_size': len(high_group_target),
        'low_group_size': len(low_group_target),
        'total_samples': total_samples,
        'factor_strength': abs(correlation)
    }

def analyze_all_factors(df, factors, target='direction_next'):
    """分析所有因子的预测能力"""
    print(f"\n{'='*50}")
    print("单因子预测能力分析")
    print(f"{'='*50}")
    print("分析逻辑：使用当日因子水平预测下一日利差变化方向")

    results = []

    for factor in factors:
        if factor not in df.columns:
            print(f"跳过缺失因子: {factor}")
            continue

        result = calculate_single_factor_win_rate(df, factor, target)
        if result is not None:
            results.append(result)

            print(f"\n{factor}:")
            print(f"  相关系数: {result['correlation']:.4f}")
            print(f"  统计显著性: {'是' if result['significant'] else '否'} (p={result['p_value']:.4f})")
            print(f"  预测胜率: {result['win_rate']:.3f} ({result['win_rate']*100:.1f}%)")
            print(f"  高因子组走阔概率: {result['high_group_prob']:.3f}")
            print(f"  低因子组走阔概率: {result['low_group_prob']:.3f}")
            print(f"  样本数量: {result['total_samples']}")

    return pd.DataFrame(results)

# 分析所有可用因子
available_factors = [f for f in all_factors if f in df_processed.columns]
print(f"\n开始分析{len(available_factors)}个可用因子...")

# 对30-10y利差方向的预测分析
factor_analysis_spread = analyze_all_factors(df_processed, available_factors, 'direction_next')

# 对30y收益率方向的预测分析
factor_analysis_30y = analyze_all_factors(df_processed, available_factors, '30y_direction_next')

# 保存单因子分析结果
factor_analysis_spread.to_excel(f'{output_dir}/单因子预测30-10y利差胜率分析.xlsx', index=False)
factor_analysis_30y.to_excel(f'{output_dir}/单因子预测30y收益率胜率分析.xlsx', index=False)

print(f"\n✓ 单因子分析结果已保存")
print(f"  - 30-10y利差预测: {output_dir}/单因子预测30-10y利差胜率分析.xlsx")
print(f"  - 30y收益率预测: {output_dir}/单因子预测30y收益率胜率分析.xlsx")

def create_factor_analysis_visualizations(factor_analysis, target_name, output_dir):
    """创建因子分析可视化图表"""
    print(f"\n创建{target_name}预测因子分析图表...")

    if len(factor_analysis) == 0:
        print("没有有效因子数据，跳过可视化")
        return

    # 按胜率排序
    factor_sorted = factor_analysis.sort_values('win_rate', ascending=False)

    # 1. 因子胜率排名图
    plt.figure(figsize=(14, 8))
    colors = ['green' if rate > 0.5 else 'red' for rate in factor_sorted['win_rate']]
    bars = plt.bar(range(len(factor_sorted)), factor_sorted['win_rate'], color=colors, alpha=0.7)

    plt.axhline(y=0.5, color='black', linestyle='--', label='随机预测基准线(50%)', linewidth=2)
    plt.xlabel('因子', fontsize=12)
    plt.ylabel('预测胜率', fontsize=12)
    plt.title(f'各因子预测{target_name}的胜率排名', fontsize=14)
    plt.xticks(range(len(factor_sorted)), factor_sorted['factor'], rotation=45, ha='right')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 标注胜率数值
    for i, (bar, rate) in enumerate(zip(bars, factor_sorted['win_rate'])):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{rate:.3f}', ha='center', va='bottom', fontsize=9)

    plt.tight_layout()
    plt.savefig(f'{output_dir}/{target_name}_因子胜率排名.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 2. 因子强度vs胜率散点图
    plt.figure(figsize=(12, 8))
    scatter = plt.scatter(factor_analysis['factor_strength'], factor_analysis['win_rate'],
                         c=factor_analysis['significant'], s=100, cmap='RdYlGn', alpha=0.7)

    plt.axhline(y=0.5, color='red', linestyle='--', alpha=0.5, label='随机预测基准')
    plt.xlabel('因子强度 (|相关系数|)', fontsize=12)
    plt.ylabel('预测胜率', fontsize=12)
    plt.title(f'因子强度与{target_name}预测胜率关系', fontsize=14)
    plt.colorbar(scatter, label='统计显著性 (1=显著, 0=不显著)')

    # 添加因子名称标注
    for i, row in factor_analysis.iterrows():
        plt.annotate(row['factor'], (row['factor_strength'], row['win_rate']),
                    xytext=(5, 5), textcoords='offset points', fontsize=8, alpha=0.8)

    plt.grid(True, alpha=0.3)
    plt.legend()
    plt.tight_layout()
    plt.savefig(f'{output_dir}/{target_name}_因子强度与胜率关系.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 3. 因子分组概率对比图
    fig, ax = plt.subplots(figsize=(14, 10))

    x = np.arange(len(factor_analysis))
    width = 0.35

    bars1 = ax.bar(x - width/2, factor_analysis['high_group_prob'], width,
                   label='高因子组走阔概率', alpha=0.8, color='lightgreen')
    bars2 = ax.bar(x + width/2, factor_analysis['low_group_prob'], width,
                   label='低因子组走阔概率', alpha=0.8, color='lightcoral')

    # 添加整体基准线
    overall_prob = df_processed['direction_next'].mean()
    ax.axhline(y=overall_prob, color='blue', linestyle='--',
               label=f'整体走阔概率({overall_prob:.3f})', alpha=0.7)

    ax.set_xlabel('因子')
    ax.set_ylabel('走阔概率')
    ax.set_title(f'各因子分组的{target_name}走阔概率对比')
    ax.set_xticks(x)
    ax.set_xticklabels(factor_analysis['factor'], rotation=45, ha='right')
    ax.legend()
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(f'{output_dir}/{target_name}_因子分组概率对比.png', dpi=300, bbox_inches='tight')
    plt.show()

# 创建可视化图表
create_factor_analysis_visualizations(factor_analysis_spread, '30-10y利差方向', output_dir)
create_factor_analysis_visualizations(factor_analysis_30y, '30y收益率方向', output_dir)

def analyze_factor_categories_performance(df, factor_categories, factor_analysis, target_name, output_dir):
    """分析各类别因子的表现"""
    print(f"\n分析{target_name}预测的因子类别表现...")

    category_results = []

    for category, factors in factor_categories.items():
        available_factors = [f for f in factors if f in df.columns]
        if not available_factors:
            continue

        category_data = factor_analysis[factor_analysis['factor'].isin(available_factors)]

        if len(category_data) == 0:
            continue

        # 计算类别统计
        avg_win_rate = category_data['win_rate'].mean()
        max_win_rate = category_data['win_rate'].max()
        min_win_rate = category_data['win_rate'].min()
        significant_count = category_data['significant'].sum()
        effective_count = (category_data['win_rate'] > 0.5).sum()
        avg_correlation = category_data['factor_strength'].mean()

        category_results.append({
            'category': category,
            'factor_count': len(available_factors),
            'avg_win_rate': avg_win_rate,
            'max_win_rate': max_win_rate,
            'min_win_rate': min_win_rate,
            'significant_count': significant_count,
            'effective_count': effective_count,
            'effectiveness_ratio': effective_count / len(available_factors) if available_factors else 0,
            'avg_correlation': avg_correlation
        })

        print(f"\n{category}:")
        print(f"  因子数量: {len(available_factors)}")
        print(f"  平均胜率: {avg_win_rate:.3f} ({avg_win_rate*100:.1f}%)")
        print(f"  最高胜率: {max_win_rate:.3f} ({max_win_rate*100:.1f}%)")
        print(f"  最低胜率: {min_win_rate:.3f} ({min_win_rate*100:.1f}%)")
        print(f"  显著因子数: {significant_count}")
        print(f"  有效因子数: {effective_count} (胜率>50%)")
        print(f"  有效率: {effective_count/len(available_factors):.1%}")

    if not category_results:
        print("没有可分析的因子类别")
        return pd.DataFrame()

    category_df = pd.DataFrame(category_results)
    category_df.to_excel(f'{output_dir}/{target_name}_因子类别表现分析.xlsx', index=False)

    # 可视化因子类别表现
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

    # 1. 平均胜率对比
    bars1 = ax1.bar(category_df['category'], category_df['avg_win_rate'], alpha=0.7)
    ax1.axhline(y=0.5, color='red', linestyle='--', alpha=0.5)
    ax1.set_title(f'{target_name}预测 - 各类别因子平均胜率')
    ax1.set_ylabel('平均胜率')
    ax1.tick_params(axis='x', rotation=45)
    for bar, rate in zip(bars1, category_df['avg_win_rate']):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{rate:.3f}', ha='center', va='bottom')

    # 2. 有效率对比
    bars2 = ax2.bar(category_df['category'], category_df['effectiveness_ratio'], alpha=0.7, color='orange')
    ax2.set_title('各类别因子有效率')
    ax2.set_ylabel('有效率')
    ax2.tick_params(axis='x', rotation=45)
    for bar, ratio in zip(bars2, category_df['effectiveness_ratio']):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                f'{ratio:.1%}', ha='center', va='bottom')

    # 3. 显著因子数量
    bars3 = ax3.bar(category_df['category'], category_df['significant_count'], alpha=0.7, color='green')
    ax3.set_title('各类别显著因子数量')
    ax3.set_ylabel('显著因子数')
    ax3.tick_params(axis='x', rotation=45)
    for bar, count in zip(bars3, category_df['significant_count']):
        ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                f'{int(count)}', ha='center', va='bottom')

    # 4. 最高胜率对比
    bars4 = ax4.bar(category_df['category'], category_df['max_win_rate'], alpha=0.7, color='purple')
    ax4.axhline(y=0.5, color='red', linestyle='--', alpha=0.5)
    ax4.set_title('各类别最高胜率')
    ax4.set_ylabel('最高胜率')
    ax4.tick_params(axis='x', rotation=45)
    for bar, rate in zip(bars4, category_df['max_win_rate']):
        ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{rate:.3f}', ha='center', va='bottom')

    plt.tight_layout()
    plt.savefig(f'{output_dir}/{target_name}_因子类别综合表现.png', dpi=300, bbox_inches='tight')
    plt.show()

    return category_df

# 分析因子类别表现
category_analysis_spread = analyze_factor_categories_performance(
    df_processed, factor_categories, factor_analysis_spread, '30-10y利差方向', output_dir)

category_analysis_30y = analyze_factor_categories_performance(
    df_processed, factor_categories, factor_analysis_30y, '30y收益率方向', output_dir)

def build_multi_factor_strategies(df, factor_analysis, target='direction_next', output_dir=None, target_name=''):
    """构建多因子组合策略"""
    print(f"\n{'='*50}")
    print(f"多因子组合策略构建 - {target_name}")
    print(f"{'='*50}")

    # 选择有效因子（胜率>50%且统计显著）
    effective_factors = factor_analysis[
        (factor_analysis['win_rate'] > 0.5) &
        (factor_analysis['significant'] == True)
    ].sort_values('win_rate', ascending=False)

    print(f"有效因子数量: {len(effective_factors)}")
    if len(effective_factors) == 0:
        print("没有找到有效因子，无法构建多因子策略")
        return None, None

    print("有效因子列表:")
    for i, (_, factor) in enumerate(effective_factors.iterrows(), 1):
        print(f"  {i}. {factor['factor']}: 胜率 {factor['win_rate']:.3f}")

    # 构建不同的多因子组合策略
    strategies = {}

    # 策略1：TOP3因子简单投票
    if len(effective_factors) >= 3:
        top3_factors = effective_factors.head(3)['factor'].tolist()
        strategy_name = 'TOP3因子投票策略'

        signals = pd.DataFrame(index=df.index)

        for factor in top3_factors:
            factor_data = factor_analysis[factor_analysis['factor'] == factor].iloc[0]
            factor_median = df[factor].median()

            if factor_data['correlation'] > 0:
                signals[f'{factor}_signal'] = (df[factor] > factor_median).astype(int)
            else:
                signals[f'{factor}_signal'] = (df[factor] < factor_median).astype(int)

        signals['vote_sum'] = signals.sum(axis=1)
        signals['prediction'] = (signals['vote_sum'] >= 2).astype(int)  # 多数投票

        # 计算策略胜率
        actual = df[target]
        strategy_win_rate = (signals['prediction'] == actual).mean()

        strategies[strategy_name] = {
            'factors': top3_factors,
            'win_rate': strategy_win_rate,
            'predictions': signals['prediction'],
            'vote_strength': signals['vote_sum']
        }

        print(f"\n{strategy_name}:")
        print(f"  使用因子: {top3_factors}")
        print(f"  策略胜率: {strategy_win_rate:.3f} ({strategy_win_rate*100:.1f}%)")

    # 策略2：TOP5因子加权投票
    if len(effective_factors) >= 5:
        top5_factors = effective_factors.head(5)['factor'].tolist()
        strategy_name = 'TOP5因子加权投票策略'

        signals = pd.DataFrame(index=df.index)

        for factor in top5_factors:
            factor_data = factor_analysis[factor_analysis['factor'] == factor].iloc[0]
            factor_median = df[factor].median()
            weight = factor_data['win_rate']  # 使用胜率作为权重

            if factor_data['correlation'] > 0:
                signal = (df[factor] > factor_median).astype(int)
            else:
                signal = (df[factor] < factor_median).astype(int)

            signals[f'{factor}_weighted'] = signal * weight

        signals['weighted_sum'] = signals.sum(axis=1)
        signals['prediction'] = (signals['weighted_sum'] > signals['weighted_sum'].median()).astype(int)

        # 计算策略胜率
        strategy_win_rate = (signals['prediction'] == actual).mean()

        strategies[strategy_name] = {
            'factors': top5_factors,
            'win_rate': strategy_win_rate,
            'predictions': signals['prediction'],
            'weighted_score': signals['weighted_sum']
        }

        print(f"\n{strategy_name}:")
        print(f"  使用因子: {top5_factors}")
        print(f"  策略胜率: {strategy_win_rate:.3f} ({strategy_win_rate*100:.1f}%)")

    # 策略3：最佳单因子策略
    best_factor = effective_factors.iloc[0]
    strategy_name = '最佳单因子策略'

    factor_median = df[best_factor['factor']].median()
    if best_factor['correlation'] > 0:
        prediction = (df[best_factor['factor']] > factor_median).astype(int)
    else:
        prediction = (df[best_factor['factor']] < factor_median).astype(int)

    strategy_win_rate = (prediction == actual).mean()

    strategies[strategy_name] = {
        'factors': [best_factor['factor']],
        'win_rate': strategy_win_rate,
        'predictions': prediction,
        'factor_value': df[best_factor['factor']]
    }

    print(f"\n{strategy_name}:")
    print(f"  使用因子: {best_factor['factor']}")
    print(f"  策略胜率: {strategy_win_rate:.3f} ({strategy_win_rate*100:.1f}%)")

    # 保存策略结果
    if output_dir:
        strategy_results = []
        for name, strategy in strategies.items():
            strategy_results.append({
                'strategy_name': name,
                'factors': ', '.join(strategy['factors']),
                'win_rate': strategy['win_rate'],
                'factor_count': len(strategy['factors'])
            })

        strategy_df = pd.DataFrame(strategy_results)
        strategy_df.to_excel(f'{output_dir}/{target_name}_多因子策略对比.xlsx', index=False)

    return strategies, effective_factors

# 构建多因子策略
strategies_spread, effective_factors_spread = build_multi_factor_strategies(
    df_processed, factor_analysis_spread, 'direction_next', output_dir, '30-10y利差方向')

strategies_30y, effective_factors_30y = build_multi_factor_strategies(
    df_processed, factor_analysis_30y, '30y_direction_next', output_dir, '30y收益率方向')

def generate_comprehensive_analysis_report(df, factor_analysis_spread, factor_analysis_30y,
                                         category_analysis_spread, category_analysis_30y,
                                         strategies_spread, strategies_30y, output_dir):
    """生成综合分析报告"""
    print(f"\n{'='*50}")
    print("生成综合分析报告")
    print(f"{'='*50}")

    # 基本统计
    total_samples = len(df)
    spread_widen_days = df['direction_next'].sum()
    spread_narrow_days = total_samples - spread_widen_days
    spread_widen_prob = df['direction_next'].mean()

    yield_30y_up_days = df['30y_direction_next'].sum()
    yield_30y_down_days = total_samples - yield_30y_up_days
    yield_30y_up_prob = df['30y_direction_next'].mean()

    # 生成报告
    report = f"""
30-10y利差量化预测策略深度分析报告
=====================================
分析期间：346个交易日 (2024年1月2日 - 2025年5月23日)

一、数据概况与基本特征
- 总样本数量: {total_samples}个交易日
- 30-10y利差走阔天数: {spread_widen_days}天
- 30-10y利差收窄天数: {spread_narrow_days}天
- 基准走阔概率: {spread_widen_prob:.3f} ({spread_widen_prob*100:.1f}%)

- 30y收益率上升天数: {yield_30y_up_days}天
- 30y收益率下降天数: {yield_30y_down_days}天
- 30y收益率上升概率: {yield_30y_up_prob:.3f} ({yield_30y_up_prob*100:.1f}%)

利差基本统计:
- 均值: {df['30-10y'].mean():.2f}bp
- 标准差: {df['30-10y'].std():.2f}bp
- 最小值: {df['30-10y'].min():.2f}bp
- 最大值: {df['30-10y'].max():.2f}bp

二、单因子分析结果

30-10y利差方向预测:
- 分析因子总数: {len(factor_analysis_spread)}
- 有效因子数量: {len(factor_analysis_spread[factor_analysis_spread['win_rate'] > 0.5])}
- 有效因子比例: {len(factor_analysis_spread[factor_analysis_spread['win_rate'] > 0.5])/len(factor_analysis_spread)*100:.1f}%
"""

    if len(factor_analysis_spread) > 0:
        best_spread_factor = factor_analysis_spread.loc[factor_analysis_spread['win_rate'].idxmax()]
        report += f"""
最佳利差预测因子: {best_spread_factor['factor']}
- 预测胜率: {best_spread_factor['win_rate']:.3f} ({best_spread_factor['win_rate']*100:.1f}%)
- 相关系数: {best_spread_factor['correlation']:.4f}
- 统计显著性: {'是' if best_spread_factor['significant'] else '否'}
"""

        # TOP5因子
        top5_spread = factor_analysis_spread.nlargest(5, 'win_rate')
        report += f"\nTOP5利差预测因子:\n"
        for i, (_, factor) in enumerate(top5_spread.iterrows(), 1):
            report += f"{i}. {factor['factor']}: 胜率 {factor['win_rate']:.3f}\n"

    if len(factor_analysis_30y) > 0:
        best_30y_factor = factor_analysis_30y.loc[factor_analysis_30y['win_rate'].idxmax()]
        report += f"""
30y收益率方向预测:
最佳30y预测因子: {best_30y_factor['factor']}
- 预测胜率: {best_30y_factor['win_rate']:.3f} ({best_30y_factor['win_rate']*100:.1f}%)
- 相关系数: {best_30y_factor['correlation']:.4f}
"""

    # 因子类别分析
    if len(category_analysis_spread) > 0:
        best_category_spread = category_analysis_spread.loc[category_analysis_spread['avg_win_rate'].idxmax()]
        report += f"""
三、因子类别分析

利差预测最有效类别: {best_category_spread['category']}
- 平均胜率: {best_category_spread['avg_win_rate']:.3f}
- 有效率: {best_category_spread['effectiveness_ratio']:.1%}
- 最高胜率: {best_category_spread['max_win_rate']:.3f}
"""

    # 多因子策略结果
    if strategies_spread:
        report += f"""
四、多因子组合策略结果

利差方向预测策略:
"""
        for name, strategy in strategies_spread.items():
            report += f"""
{name}:
- 使用因子: {', '.join(strategy['factors'])}
- 策略胜率: {strategy['win_rate']:.3f} ({strategy['win_rate']*100:.1f}%)
- 因子数量: {len(strategy['factors'])}
"""

    report += f"""
五、投资策略建议

1. 核心交易策略框架:
"""

    if len(factor_analysis_spread) > 0:
        effective_factors = factor_analysis_spread[factor_analysis_spread['win_rate'] > 0.5]
        if len(effective_factors) > 0:
            report += f"""
   利差走阔信号 (买入30年国债，卖出10年国债):
"""
            for _, factor in effective_factors.head(3).iterrows():
                if factor['correlation'] > 0:
                    condition = f"{factor['factor']} > 历史中位数"
                else:
                    condition = f"{factor['factor']} < 历史中位数"
                report += f"   - {condition} (胜率: {factor['win_rate']:.3f})\n"

            report += f"""
   利差收窄信号 (买入10年国债，卖出30年国债):
   - 与走阔信号相反的条件
"""

    report += f"""
2. 策略实施建议:
   - 优先使用胜率>55%的因子
   - 多因子确认可提高预测可靠性
   - 重点关注统计显著的因子
   - 建议结合基本面分析

3. 风险管理:
   - 设置合理的止损位
   - 控制单次交易仓位
   - 定期重新评估因子有效性
   - 关注市场环境变化对因子的影响

六、重要发现与结论

1. 利差变化特征:
   - 走阔概率: {spread_widen_prob*100:.1f}%
   - 收窄概率: {(1-spread_widen_prob)*100:.1f}%
   - 利差变化相对均衡，为策略提供了良好基础

2. 因子有效性:
   - 部分因子具有较强的预测能力
   - 不同类别因子表现存在差异
   - 多因子组合可以提升预测稳定性

3. 策略价值:
   - 该量化策略可作为债券配置的重要参考
   - 建议与基本面分析相结合使用
   - 适合中短期交易决策

七、后续优化方向

1. 因子工程:
   - 探索更多宏观经济指标
   - 考虑因子的滞后效应
   - 引入非线性因子组合

2. 模型优化:
   - 使用机器学习方法
   - 动态调整因子权重
   - 考虑市场状态切换

3. 风险控制:
   - 建立更完善的风险管理体系
   - 考虑极端市场情况
   - 定期模型回测和更新
"""

    # 保存报告
    with open(f'{output_dir}/30-10y利差量化预测策略综合报告.txt', 'w', encoding='utf-8') as f:
        f.write(report)

    print(report)
    print(f"\n✓ 综合报告已保存到: {output_dir}/30-10y利差量化预测策略综合报告.txt")

    # 创建最终汇总Excel
    with pd.ExcelWriter(f'{output_dir}/量化择时胜率完整分析.xlsx') as writer:
        factor_analysis_spread.to_excel(writer, sheet_name='利差方向单因子分析', index=False)
        factor_analysis_30y.to_excel(writer, sheet_name='30y收益率单因子分析', index=False)

        if len(category_analysis_spread) > 0:
            category_analysis_spread.to_excel(writer, sheet_name='利差方向因子类别分析', index=False)
        if len(category_analysis_30y) > 0:
            category_analysis_30y.to_excel(writer, sheet_name='30y收益率因子类别分析', index=False)

        # 有效因子汇总
        effective_spread = factor_analysis_spread[factor_analysis_spread['win_rate'] > 0.5]
        effective_30y = factor_analysis_30y[factor_analysis_30y['win_rate'] > 0.5]

        if len(effective_spread) > 0:
            effective_spread.to_excel(writer, sheet_name='利差方向有效因子', index=False)
        if len(effective_30y) > 0:
            effective_30y.to_excel(writer, sheet_name='30y收益率有效因子', index=False)

    print(f"✓ 完整分析Excel已保存到: {output_dir}/量化择时胜率完整分析.xlsx")

# 生成综合报告
generate_comprehensive_analysis_report(
    df_processed, factor_analysis_spread, factor_analysis_30y,
    category_analysis_spread, category_analysis_30y,
    strategies_spread, strategies_30y, output_dir)

print(f"\n" + "="*80)
print(f"30-10y利差量化预测策略分析完成")
print(f"="*80)
print(f"分析期间: 346个交易日")
print(f"输出目录: {output_dir}")

if len(factor_analysis_spread) > 0:
    best_factor = factor_analysis_spread.loc[factor_analysis_spread['win_rate'].idxmax()]
    print(f"🏆 最佳利差预测因子: {best_factor['factor']}")
    print(f"📊 最高预测胜率: {best_factor['win_rate']:.3f} ({best_factor['win_rate']*100:.1f}%)")

    effective_count = len(factor_analysis_spread[factor_analysis_spread['win_rate'] > 0.5])
    print(f"✅ 有效因子数量: {effective_count}/{len(factor_analysis_spread)}")

    if strategies_spread:
        best_strategy = max(strategies_spread.items(), key=lambda x: x[1]['win_rate'])
        print(f"🔗 最佳组合策略: {best_strategy[0]}")
        print(f"📈 组合策略胜率: {best_strategy[1]['win_rate']:.3f} ({best_strategy[1]['win_rate']*100:.1f}%)")

print(f"="*80)
