import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestRegressor
import matplotlib.pyplot as plt
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']

def load_clean_data():
    df = pd.read_excel("/Users/<USER>/Desktop/量化择时0526.xlsx", 
                      sheet_name=0,
                      parse_dates=['Unnamed: 0'],
                      index_col='Unnamed: 0')
    
    print("原始列名:", df.columns.tolist())
    df = df.rename(columns={
        '30-10y': '利差',
        '30Y成交量': '30Y成交量',
        'R007': 'R007',
        '偏离度（偏离均线）': '偏离度_均线'
    })
    print("\n清洗后列名:", df.columns.tolist())
    
    # 缺失值处理
    print("\n缺失值统计:")
    print(df.isnull().sum())
    
    # 保留原始数据副本用于调试
    raw_df = df.copy()
    
    # 分步处理缺失值
    df = df.dropna(subset=['利差'])  # 删除无利差数据的行
    df = df.ffill().bfill()  # 前后填充
    
    # 计算利差变动并处理边界缺失
    df['利差变动'] = df['利差'].diff(1).fillna(0)  # 首行填充0
    
    print("\n数据样例:")
    print(df[['利差','利差变动']].head(3))
    
    # 二次缺失值检查
    print("\n最终缺失值统计:")
    print(df.isnull().sum())
    
    return df

def build_features(df):
    # 技术指标
    df['MA250'] = df['利差'].rolling(250).mean()
    df['偏离度'] = df['利差'] - df['MA250']
    df['波动率'] = df['利差'].rolling(20).std()
    
    # 滞后特征
    for lag in [1,3,5]:
        df[f'利差滞后{lag}'] = df['利差'].shift(lag)
    
    print("\n特征列清单:", df.columns.tolist())
    return df.dropna()

def analyze_factors(df):
    # 全量候选因子
    candidate_factors = [
        '水泥价格指数', '30大中城市商品房成交面积', '互联网搜索指数:失业金领取条件',
        '建材综合指数', '南华金属指数', 'R007', '30Y成交量', '偏离度_均线',
        '1/沪深300PE-10y', 'DR007', '成交量:DR007', '成交量:R007'
    ]
    
    # 因子有效性检验
    def factor_screening(df, factors):
        results = []
        
        # 计算指标函数
        def calc_metrics(series, target):
            # 方向胜率
            direction_accuracy = np.mean(np.sign(series) == np.sign(target))
            
            # Spearman秩相关系数
            spearman_corr = series.corr(target, method='spearman')
            
            # 分位数收益
            q5_return = target[series <= series.quantile(0.05)].mean()
            q95_return = target[series >= series.quantile(0.95)].mean()
            spread_return = q95_return - q5_return
            
            return {
                '胜率': direction_accuracy,
                '秩相关系数': spearman_corr,
                '五分位收益': q5_return,
                '九五分位收益': q95_return,
                '收益差': spread_return
            }
        
        # 滚动窗口稳定性检验
        def rolling_stability(series, target, window=252):
            stability = []
            for i in range(len(series)-window):
                slice_data = series.iloc[i:i+window]
                slice_target = target.iloc[i:i+window]
                corr = slice_data.corr(slice_target, method='spearman')
                stability.append(corr)
            return np.std(stability)
        
        target = df['利差变动'].shift(-1)  # 预测下一期变动
        
        for factor in factors:
            # 剔除缺失值
            valid_idx = df[factor].notnull() & target.notnull()
            factor_series = df[factor][valid_idx]
            target_series = target[valid_idx]
            
            # 计算基础指标
            metrics = calc_metrics(factor_series, target_series)
            
            # 计算稳定性指标
            stability = rolling_stability(factor_series, target_series)
            
            # 存储结果
            results.append({
                '因子': factor,
                **metrics,
                '滚动相关性波动率': stability
            })
        
        return pd.DataFrame(results)
    
    # 执行因子筛选
    factor_results = factor_screening(df, candidate_factors)
    
    # 特征重要性评估
    # 确保数据对齐和有效填充
    valid_data = df[candidate_factors + ['利差变动']].ffill().bfill().dropna()
    X = valid_data[candidate_factors]
    y = valid_data['利差变动'].shift(-1).dropna()
    X = X.loc[y.index]  # 确保特征和目标对齐
    
    rf_model = RandomForestRegressor(n_estimators=100, random_state=42)
    rf_model.fit(X, y)
    importance = pd.DataFrame({
        '因子': candidate_factors,
        '重要性': rf_model.feature_importances_
    })
    
    # 合并筛选结果
    final_results = pd.merge(factor_results, importance, on='因子')
    
    # 保存分析结果
    final_results.sort_values('重要性', ascending=False).to_csv('factor_screening_results.csv', index=False)
    
    # 可视化top因子
    top_factors = final_results.nlargest(5, '重要性')['因子'].tolist()
    
    # 因子相关性矩阵
    corr_matrix = df[top_factors + ['利差变动']].corr(method='spearman')
    
    plt.figure(figsize=(12,10))
    plt.imshow(corr_matrix, cmap='coolwarm', vmin=-1, vmax=1)
    plt.colorbar()
    plt.xticks(ticks=range(len(top_factors)+1), 
              labels=top_factors + ['利差变动'], 
              rotation=45)
    plt.yticks(ticks=range(len(top_factors)+1),
              labels=top_factors + ['利差变动'])
    plt.title('Top因子秩相关矩阵')
    plt.tight_layout()
    plt.savefig('factor_correlation_v2.png')
    
    return final_results

if __name__ == "__main__":
    try:
        print("=== 开始分析流程 ===")
        data = load_clean_data()
        processed_data = build_features(data)
        print("\n=== 因子分析 ===")
        corr_result = analyze_factors(processed_data)
        print("\n分析成功完成！生成图表: factor_correlation_v2.png")
    except Exception as e:
        print(f"\n错误发生: {str(e)}")
        print("排查建议:")
        print("1. 确认Excel文件路径正确")
        print("2. 检查工作表第一行是否为列标题")
        print("3. 验证30-10y等关键列是否存在")
