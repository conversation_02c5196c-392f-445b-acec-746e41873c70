import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from scipy.signal import argrelextrema
import seaborn as sns
from datetime import datetime, timedelta
from statsmodels.tsa.arima.model import ARIMA
import warnings
import matplotlib.font_manager as fm
import os
import matplotlib as mpl
warnings.filterwarnings('ignore')

# 设置中文字体 - 使用更可靠的方法
# 查找系统中的中文字体
font_paths = [
    '/System/Library/Fonts/PingFang.ttc',  # macOS系统中的中文字体
    '/System/Library/Fonts/STHeiti Light.ttc',
    '/System/Library/Fonts/STHeiti Medium.ttc',
    '/System/Library/Fonts/Hiragino Sans GB.ttc',
    '/Library/Fonts/Arial Unicode.ttf'
]

# 尝试加载可用的中文字体
font_found = False
for font_path in font_paths:
    if os.path.exists(font_path):
        # 添加字体
        font_prop = fm.FontProperties(fname=font_path)
        plt.rcParams['font.family'] = 'sans-serif'
        plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'Microsoft YaHei', 'PingFang HK', 'STHeiti']
        font_found = True
        print(f"使用字体: {font_path}")
        break

if not font_found:
    # 尝试其他常见中文字体
    plt.rcParams['font.family'] = 'sans-serif'
    plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'STHeiti', 'Arial Unicode MS', 'PingFang HK']
    print("使用系统默认中文字体")

plt.rcParams['axes.unicode_minus'] = False  # 用来正常显示负号
plt.rcParams['savefig.dpi'] = 300  # 提高图表分辨率

# 设置输出路径为桌面
desktop_path = '/Users/<USER>/Desktop/'

# 加载数据
file_path = '/Users/<USER>/Desktop/技术分析.xlsx'
gov_spread_df = pd.read_excel(file_path, sheet_name='国债30-10y')
local_spread_df = pd.read_excel(file_path, sheet_name='地债30y-国债30y')

# 将日期列转换为datetime格式
gov_spread_df.iloc[:, 0] = pd.to_datetime(gov_spread_df.iloc[:, 0])
local_spread_df.iloc[:, 0] = pd.to_datetime(local_spread_df.iloc[:, 0])

# 重命名列以提高清晰度
gov_spread_df.columns = ['日期', '国债10年', '国债30年', '利差30年减10年']
local_spread_df.columns = ['日期', '国债30年', '地方债30年', '利差地方债减国债']

# 设置日期为索引
gov_spread_df.set_index('日期', inplace=True)
local_spread_df.set_index('日期', inplace=True)

# 筛选2024-01-02至2025-04-30的数据
start_date = pd.Timestamp('2024-01-02')
end_date = pd.Timestamp('2025-04-30')

# 确保索引是有序的
gov_spread_df = gov_spread_df.sort_index()
local_spread_df = local_spread_df.sort_index()

# 使用布尔索引筛选日期范围
gov_spread_df = gov_spread_df[(gov_spread_df.index >= start_date) & (gov_spread_df.index <= end_date)]
local_spread_df = local_spread_df[(local_spread_df.index >= start_date) & (local_spread_df.index <= end_date)]

# 技术分析函数
def calculate_bollinger_bands(df, column, window=20, num_std=2):
    """计算布林带指标"""
    rolling_mean = df[column].rolling(window=window).mean()
    rolling_std = df[column].rolling(window=window).std()

    df[f'{column}_布林上轨'] = rolling_mean + (rolling_std * num_std)
    df[f'{column}_布林中轨'] = rolling_mean
    df[f'{column}_布林下轨'] = rolling_mean - (rolling_std * num_std)
    return df

def calculate_ma_signals(df, column, window=250, std_levels=[1, 1.5, 2]):
    """计算基于MA250的交易信号"""
    # 计算MA250
    ma = df[column].rolling(window=window).mean()
    std = df[column].rolling(window=window).std()

    # 保存MA和标准差
    df[f'{column}_MA{window}'] = ma
    df[f'{column}_MA{window}_STD'] = std

    # 计算不同标准差水平的上轨
    for level in std_levels:
        df[f'{column}_MA{window}_上轨_{level}STD'] = ma + (std * level)

        # 生成突破信号 (1表示突破上轨，可以买入30y卖出10y)
        signal_col = f'{column}_突破MA{window}_{level}STD'
        df[signal_col] = 0
        df.loc[df[column] > df[f'{column}_MA{window}_上轨_{level}STD'], signal_col] = 1

        # 添加交易建议
        advice_col = f'{column}_MA{window}_{level}STD_建议'
        df[advice_col] = '持有'
        if '国债' in column and '10年' in column:
            df.loc[df[signal_col] == 1, advice_col] = '买入30年国债，卖出10年国债'
        elif '地方债' in column and '国债' in column:
            df.loc[df[signal_col] == 1, advice_col] = '买入30年地方债，卖出30年国债'

    return df

def calculate_rsi(df, column, window=14):
    """计算RSI相对强弱指标"""
    delta = df[column].diff()
    gain = delta.where(delta > 0, 0)
    loss = -delta.where(delta < 0, 0)

    avg_gain = gain.rolling(window=window).mean()
    avg_loss = loss.rolling(window=window).mean()

    # 计算RS和RSI
    rs = avg_gain / avg_loss
    df[f'{column}_RSI'] = 100 - (100 / (1 + rs))
    return df

def calculate_macd(df, column, fast=12, slow=26, signal=9):
    """计算MACD指标"""
    df[f'{column}_MACD_快线'] = df[column].ewm(span=fast, adjust=False).mean()
    df[f'{column}_MACD_慢线'] = df[column].ewm(span=slow, adjust=False).mean()
    df[f'{column}_MACD'] = df[f'{column}_MACD_快线'] - df[f'{column}_MACD_慢线']
    df[f'{column}_MACD_信号线'] = df[f'{column}_MACD'].ewm(span=signal, adjust=False).mean()
    df[f'{column}_MACD_柱状'] = df[f'{column}_MACD'] - df[f'{column}_MACD_信号线']
    return df

def calculate_kdj(df, column, n=9, m1=3, m2=3):
    """计算KDJ指标"""
    low_min = df[column].rolling(window=n).min()
    high_max = df[column].rolling(window=n).max()

    rsv = 100 * ((df[column] - low_min) / (high_max - low_min))
    df[f'{column}_K'] = rsv.ewm(alpha=1/m1, adjust=False).mean()
    df[f'{column}_D'] = df[f'{column}_K'].ewm(alpha=1/m2, adjust=False).mean()
    df[f'{column}_J'] = 3 * df[f'{column}_K'] - 2 * df[f'{column}_D']
    return df

def find_support_resistance(df, column, order=10):
    """寻找支撑位和阻力位"""
    # 寻找局部极大值和极小值
    df[f'{column}_支撑位'] = df.iloc[argrelextrema(df[column].values, np.less_equal, order=order)[0]][column]
    df[f'{column}_阻力位'] = df.iloc[argrelextrema(df[column].values, np.greater_equal, order=order)[0]][column]
    return df

def generate_signals(df, column, is_gov_spread=True):
    """基于技术指标生成交易信号"""
    df[f'{column}_信号'] = 0

    # 布林带信号
    df.loc[df[column] < df[f'{column}_布林下轨'], f'{column}_信号'] = 1  # 买入信号（利差低）
    df.loc[df[column] > df[f'{column}_布林上轨'], f'{column}_信号'] = -1  # 卖出信号（利差高）

    # RSI信号
    df.loc[df[f'{column}_RSI'] < 30, f'{column}_信号'] += 1  # 超卖
    df.loc[df[f'{column}_RSI'] > 70, f'{column}_信号'] -= 1  # 超买

    # MACD信号
    df.loc[df[f'{column}_MACD'] > df[f'{column}_MACD_信号线'], f'{column}_信号'] += 1  # 看涨
    df.loc[df[f'{column}_MACD'] < df[f'{column}_MACD_信号线'], f'{column}_信号'] -= 1  # 看跌

    # KDJ信号
    df.loc[(df[f'{column}_K'] < 20) & (df[f'{column}_K'] > df[f'{column}_D']), f'{column}_信号'] += 1  # 超卖且金叉
    df.loc[(df[f'{column}_K'] > 80) & (df[f'{column}_K'] < df[f'{column}_D']), f'{column}_信号'] -= 1  # 超买且死叉

    # 添加具体的买入建议
    df[f'{column}_建议'] = '持有'

    if is_gov_spread:
        # 国债30y-10y利差
        df.loc[df[f'{column}_信号'] > 1, f'{column}_建议'] = '买入30年国债，卖出10年国债'  # 利差将走阔
        df.loc[df[f'{column}_信号'] < -1, f'{column}_建议'] = '买入10年国债，卖出30年国债'  # 利差将走窄
    else:
        # 地方债30y-国债30y利差
        df.loc[df[f'{column}_信号'] > 1, f'{column}_建议'] = '买入30年地方债，卖出30年国债'  # 利差将走阔
        df.loc[df[f'{column}_信号'] < -1, f'{column}_建议'] = '买入30年国债，卖出30年地方债'  # 利差将走窄

    return df

# 对两个利差数据集应用技术分析
# 国债利差 (30年-10年)
gov_spread_df = calculate_bollinger_bands(gov_spread_df, '利差30年减10年')
gov_spread_df = calculate_rsi(gov_spread_df, '利差30年减10年')
gov_spread_df = calculate_macd(gov_spread_df, '利差30年减10年')
gov_spread_df = calculate_kdj(gov_spread_df, '利差30年减10年')
gov_spread_df = find_support_resistance(gov_spread_df, '利差30年减10年')
gov_spread_df = generate_signals(gov_spread_df, '利差30年减10年', is_gov_spread=True)
# 添加MA250信号
gov_spread_df = calculate_ma_signals(gov_spread_df, '利差30年减10年', window=250, std_levels=[1, 1.5, 2])

# 地方债利差 (地方债30年-国债30年)
local_spread_df = calculate_bollinger_bands(local_spread_df, '利差地方债减国债')
local_spread_df = calculate_rsi(local_spread_df, '利差地方债减国债')
local_spread_df = calculate_macd(local_spread_df, '利差地方债减国债')
local_spread_df = calculate_kdj(local_spread_df, '利差地方债减国债')
local_spread_df = find_support_resistance(local_spread_df, '利差地方债减国债')
local_spread_df = generate_signals(local_spread_df, '利差地方债减国债', is_gov_spread=False)
# 添加MA250信号
local_spread_df = calculate_ma_signals(local_spread_df, '利差地方债减国债', window=250, std_levels=[1, 1.5, 2])

# 可视化设置
plt.style.use('seaborn-v0_8-darkgrid')
plt.rcParams['figure.figsize'] = (14, 10)
plt.rcParams['font.size'] = 12
plt.rcParams['savefig.dpi'] = 300  # 提高图表分辨率

def plot_ma250_signals(df, spread_column, title, is_gov_spread=True):
    """绘制基于MA250的交易信号分析图"""
    fig, ax = plt.subplots(figsize=(14, 8))

    # 绘制利差
    ax.plot(df.index, df[spread_column], label=spread_column, color='blue', linewidth=2)

    # 绘制MA250
    ax.plot(df.index, df[f'{spread_column}_MA250'], label='MA250', color='purple', linewidth=1.5)

    # 绘制不同标准差水平的上轨
    std_levels = [1, 1.5, 2]
    colors = ['green', 'orange', 'red']

    for level, color in zip(std_levels, colors):
        upper_band = f'{spread_column}_MA250_上轨_{level}STD'
        ax.plot(df.index, df[upper_band],
                label=f'MA250+{level}STD', color=color,
                linestyle='--', linewidth=1.5)

        # 标记突破信号
        signal_col = f'{spread_column}_突破MA250_{level}STD'
        signal_points = df[df[signal_col] == 1]

        if not signal_points.empty:
            ax.scatter(signal_points.index, signal_points[spread_column],
                      marker='^', color=color, s=150,
                      label=f'突破MA250+{level}STD信号',
                      edgecolors='black', zorder=5)

            # 为第一个信号点添加注释
            first_point = signal_points.iloc[0]
            if is_gov_spread:
                advice = '买入30年国债，卖出10年国债'
            else:
                advice = '买入30年地方债，卖出30年国债'

            ax.annotate(advice,
                       xy=(first_point.name, first_point[spread_column]),
                       xytext=(30, 30),
                       textcoords='offset points',
                       bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.7),
                       arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0.3'))

    # 设置图表标题和标签
    ax.set_title(f'{title} MA250交易信号分析', fontsize=16)
    ax.set_xlabel('日期', fontsize=14)
    ax.set_ylabel('利差值 (BP)', fontsize=14)
    ax.legend(loc='best', fontsize=12)
    ax.grid(True, alpha=0.3)

    # 格式化x轴日期
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
    ax.xaxis.set_major_locator(mdates.MonthLocator(interval=1))
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

    plt.tight_layout()
    return fig

def plot_bollinger_bands(df, spread_column, title, is_gov_spread=True):
    """绘制布林带分析图"""
    fig, ax = plt.subplots(figsize=(14, 8))

    # 绘制利差和布林带
    ax.plot(df.index, df[spread_column], label=spread_column, color='blue', linewidth=2)
    ax.plot(df.index, df[f'{spread_column}_布林上轨'], 'r--', label='布林上轨', linewidth=1.5)
    ax.plot(df.index, df[f'{spread_column}_布林中轨'], 'g--', label='布林中轨', linewidth=1.5)
    ax.plot(df.index, df[f'{spread_column}_布林下轨'], 'r--', label='布林下轨', linewidth=1.5)

    # 绘制支撑位和阻力位
    if f'{spread_column}_支撑位' in df.columns:
        ax.scatter(df.index[~df[f'{spread_column}_支撑位'].isna()],
                  df[f'{spread_column}_支撑位'].dropna(),
                  color='green', s=80, label='支撑位', marker='o', edgecolors='black')
    if f'{spread_column}_阻力位' in df.columns:
        ax.scatter(df.index[~df[f'{spread_column}_阻力位'].isna()],
                  df[f'{spread_column}_阻力位'].dropna(),
                  color='red', s=80, label='阻力位', marker='o', edgecolors='black')

    # 绘制买入/卖出信号
    buy_signals = df[df[f'{spread_column}_信号'] > 1]
    sell_signals = df[df[f'{spread_column}_信号'] < -1]

    # 添加买入/卖出建议标签
    for idx, row in buy_signals.iterrows():
        if is_gov_spread:
            label = '买入30年国债，卖出10年国债' if idx == buy_signals.index[0] else ''
        else:
            label = '买入30年地方债，卖出30年国债' if idx == buy_signals.index[0] else ''
        ax.scatter(idx, row[spread_column], marker='^', color='lime', s=150, label=label, edgecolors='black', zorder=5)

    for idx, row in sell_signals.iterrows():
        if is_gov_spread:
            label = '买入10年国债，卖出30年国债' if idx == sell_signals.index[0] else ''
        else:
            label = '买入30年国债，卖出30年地方债' if idx == sell_signals.index[0] else ''
        ax.scatter(idx, row[spread_column], marker='v', color='red', s=150, label=label, edgecolors='black', zorder=5)

    # 设置图表标题和标签
    ax.set_title(f'{title}布林带分析', fontsize=16)
    ax.set_xlabel('日期', fontsize=14)
    ax.set_ylabel('利差值 (BP)', fontsize=14)
    ax.legend(loc='best', fontsize=12)
    ax.grid(True, alpha=0.3)

    # 格式化x轴日期
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
    ax.xaxis.set_major_locator(mdates.MonthLocator(interval=1))
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

    plt.tight_layout()
    return fig

def plot_rsi(df, spread_column, title):
    """绘制RSI分析图"""
    fig, ax = plt.subplots(figsize=(14, 6))

    # 绘制RSI
    ax.plot(df.index, df[f'{spread_column}_RSI'], color='purple', label='RSI', linewidth=2)
    ax.axhline(y=70, color='r', linestyle='-', alpha=0.5, label='超买区域')
    ax.axhline(y=30, color='g', linestyle='-', alpha=0.5, label='超卖区域')

    # 标记超买超卖区域
    ax.fill_between(df.index, 70, 100, color='red', alpha=0.1)
    ax.fill_between(df.index, 0, 30, color='green', alpha=0.1)

    # 设置图表标题和标签
    ax.set_title(f'{title}RSI相对强弱指标分析', fontsize=16)
    ax.set_xlabel('日期', fontsize=14)
    ax.set_ylabel('RSI值', fontsize=14)
    ax.set_ylim(0, 100)
    ax.legend(loc='best', fontsize=12)
    ax.grid(True, alpha=0.3)

    # 格式化x轴日期
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
    ax.xaxis.set_major_locator(mdates.MonthLocator(interval=1))
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

    plt.tight_layout()
    return fig

def plot_macd(df, spread_column, title):
    """绘制MACD分析图"""
    fig, ax = plt.subplots(figsize=(14, 6))

    # 绘制MACD
    ax.plot(df.index, df[f'{spread_column}_MACD'], color='blue', label='MACD', linewidth=2)
    ax.plot(df.index, df[f'{spread_column}_MACD_信号线'], color='red', label='信号线', linewidth=2)

    # 绘制柱状图，根据正负值设置不同颜色
    positive = df[f'{spread_column}_MACD_柱状'] > 0
    negative = df[f'{spread_column}_MACD_柱状'] <= 0

    ax.bar(df.index[positive], df[f'{spread_column}_MACD_柱状'][positive], color='green', label='正柱状', alpha=0.6, width=2)
    ax.bar(df.index[negative], df[f'{spread_column}_MACD_柱状'][negative], color='red', label='负柱状', alpha=0.6, width=2)

    # 设置图表标题和标签
    ax.set_title(f'{title}MACD指标分析', fontsize=16)
    ax.set_xlabel('日期', fontsize=14)
    ax.set_ylabel('MACD值', fontsize=14)
    ax.legend(loc='best', fontsize=12)
    ax.grid(True, alpha=0.3)

    # 格式化x轴日期
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
    ax.xaxis.set_major_locator(mdates.MonthLocator(interval=1))
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

    plt.tight_layout()
    return fig

def plot_kdj(df, spread_column, title):
    """绘制KDJ分析图"""
    fig, ax = plt.subplots(figsize=(14, 6))

    # 绘制KDJ
    ax.plot(df.index, df[f'{spread_column}_K'], color='blue', label='K线', linewidth=2)
    ax.plot(df.index, df[f'{spread_column}_D'], color='red', label='D线', linewidth=2)
    ax.plot(df.index, df[f'{spread_column}_J'], color='green', label='J线', linewidth=1.5, alpha=0.7)

    # 标记超买超卖区域
    ax.axhline(y=80, color='r', linestyle='--', alpha=0.5, label='超买区域')
    ax.axhline(y=20, color='g', linestyle='--', alpha=0.5, label='超卖区域')
    ax.fill_between(df.index, 80, 100, color='red', alpha=0.1)
    ax.fill_between(df.index, 0, 20, color='green', alpha=0.1)

    # 设置图表标题和标签
    ax.set_title(f'{title}KDJ指标分析', fontsize=16)
    ax.set_xlabel('日期', fontsize=14)
    ax.set_ylabel('KDJ值', fontsize=14)
    ax.legend(loc='best', fontsize=12)
    ax.grid(True, alpha=0.3)

    # 格式化x轴日期
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
    ax.xaxis.set_major_locator(mdates.MonthLocator(interval=1))
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

    plt.tight_layout()
    return fig

def plot_trading_signals(df, spread_column, title, is_gov_spread=True):
    """绘制交易信号综合分析图"""
    fig, ax = plt.subplots(figsize=(14, 8))

    # 绘制利差
    ax.plot(df.index, df[spread_column], label=spread_column, color='blue', linewidth=2)

    # 绘制买入/卖出信号
    buy_signals = df[df[f'{spread_column}_信号'] > 1]
    sell_signals = df[df[f'{spread_column}_信号'] < -1]

    # 添加买入/卖出建议标签和箭头
    for idx, row in buy_signals.iterrows():
        if is_gov_spread:
            label = '买入30年国债，卖出10年国债' if idx == buy_signals.index[0] else ''
        else:
            label = '买入30年地方债，卖出30年国债' if idx == buy_signals.index[0] else ''
        ax.scatter(idx, row[spread_column], marker='^', color='lime', s=200, label=label, edgecolors='black', zorder=5)
        ax.annotate('买入', xy=(idx, row[spread_column]), xytext=(0, 30),
                   textcoords='offset points', ha='center', va='bottom',
                   bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.7),
                   arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0.3'))

    for idx, row in sell_signals.iterrows():
        if is_gov_spread:
            label = '买入10年国债，卖出30年国债' if idx == sell_signals.index[0] else ''
        else:
            label = '买入30年国债，卖出30年地方债' if idx == sell_signals.index[0] else ''
        ax.scatter(idx, row[spread_column], marker='v', color='red', s=200, label=label, edgecolors='black', zorder=5)
        ax.annotate('卖出', xy=(idx, row[spread_column]), xytext=(0, -30),
                   textcoords='offset points', ha='center', va='top',
                   bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.7),
                   arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0.3'))

    # 设置图表标题和标签
    ax.set_title(f'{title}交易信号分析', fontsize=16)
    ax.set_xlabel('日期', fontsize=14)
    ax.set_ylabel('利差值 (BP)', fontsize=14)
    ax.legend(loc='best', fontsize=12)
    ax.grid(True, alpha=0.3)

    # 格式化x轴日期
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
    ax.xaxis.set_major_locator(mdates.MonthLocator(interval=1))
    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

    plt.tight_layout()
    return fig

# 为两种利差绘制分析图表
# 国债利差 (30年-10年)
gov_bb_fig = plot_bollinger_bands(gov_spread_df, '利差30年减10年', '国债30年-10年', is_gov_spread=True)
gov_rsi_fig = plot_rsi(gov_spread_df, '利差30年减10年', '国债30年-10年')
gov_macd_fig = plot_macd(gov_spread_df, '利差30年减10年', '国债30年-10年')
gov_kdj_fig = plot_kdj(gov_spread_df, '利差30年减10年', '国债30年-10年')
gov_signals_fig = plot_trading_signals(gov_spread_df, '利差30年减10年', '国债30年-10年', is_gov_spread=True)
# 添加MA250信号图
gov_ma250_fig = plot_ma250_signals(gov_spread_df, '利差30年减10年', '国债30年-10年', is_gov_spread=True)

# 地方债利差 (地方债30年-国债30年)
local_bb_fig = plot_bollinger_bands(local_spread_df, '利差地方债减国债', '地方债30年-国债30年', is_gov_spread=False)
local_rsi_fig = plot_rsi(local_spread_df, '利差地方债减国债', '地方债30年-国债30年')
local_macd_fig = plot_macd(local_spread_df, '利差地方债减国债', '地方债30年-国债30年')
local_kdj_fig = plot_kdj(local_spread_df, '利差地方债减国债', '地方债30年-国债30年')
local_signals_fig = plot_trading_signals(local_spread_df, '利差地方债减国债', '地方债30年-国债30年', is_gov_spread=False)
# 添加MA250信号图
local_ma250_fig = plot_ma250_signals(local_spread_df, '利差地方债减国债', '地方债30年-国债30年', is_gov_spread=False)

# 周度回测策略
def backtest_strategy(df, spread_column, freq='W', transaction_cost=0.0002):
    """增强版回测函数，包含交易成本和详细统计"""
    # 生成周度数据
    weekly_df = df.resample(freq).last()

    # 交易信号处理
    weekly_df['持仓'] = weekly_df[f'{spread_column}_信号'].shift(1)
    weekly_df['持仓'].fillna(0, inplace=True)

    # 计算利差变化
    weekly_df['利差变化'] = weekly_df[spread_column].diff()

    # 计算策略收益（考虑交易成本）
    weekly_df['交易'] = weekly_df['持仓'].diff().abs()
    weekly_df['策略收益'] = (weekly_df['持仓'] * weekly_df['利差变化'] -
                         weekly_df['交易'] * transaction_cost)

    # 生成详细交易记录
    trades = []
    current_position = 0
    for date, row in weekly_df.iterrows():
        if row['持仓'] != current_position:
            # 记录交易
            trades.append({
                '日期': date,
                '持仓': row['持仓'],
                '利差值': row[spread_column],
                '信号强度': abs(row[f'{spread_column}_信号']),
                '类型': '开仓'
            })
            if len(trades) > 1:  # 记录平仓
                trades[-2]['平仓日期'] = date
                trades[-2]['平仓价值'] = row[spread_column]
                trades[-2]['持有周期'] = (date - trades[-2]['日期']).days / 7
            current_position = row['持仓']

    # 计算绩效指标
    weekly_df['累计收益'] = weekly_df['策略收益'].cumsum()
    max_drawdown = (weekly_df['累计收益'].cummax() - weekly_df['累计收益']).max()
    annualized_return = weekly_df['策略收益'].mean() * 52

    # 计算胜率
    valid_trades = weekly_df[weekly_df['持仓'] != 0]
    win_rate = valid_trades['策略收益'].gt(0).mean() * 100

    # 生成统计摘要
    stats = {
        '胜率 (%)': win_rate,
        '年化收益率 (%)': annualized_return * 100,
        '最大回撤 (%)': max_drawdown * 100,
        '总交易次数': len(valid_trades),
        '平均持仓周期 (周)': np.mean([t.get('持有周期',0) for t in trades if '持有周期' in t]),
        '夏普比率': (weekly_df['策略收益'].mean() / weekly_df['策略收益'].std()) * np.sqrt(52) if weekly_df['策略收益'].std() != 0 else 0
    }

    return weekly_df, pd.DataFrame(trades).dropna(), stats

# 执行增强版回测
gov_backtest_weekly, gov_trade_log, gov_stats = backtest_strategy(gov_spread_df, '利差30年减10年')
local_backtest_weekly, local_trade_log, local_stats = backtest_strategy(local_spread_df, '利差地方债减国债')

# 创建详细的绩效报告
def create_performance_report(stats, trade_log, spread_name):
    report = f"""
    {spread_name}策略绩效报告
    ========================
    1. 基础统计:
    - 胜率: {stats['胜率 (%)']:.1f}%
    - 年化收益率: {stats['年化收益率 (%)']:.1f}%
    - 最大回撤: {stats['最大回撤 (%)']:.1f}%
    - 夏普比率: {stats['夏普比率']:.2f}

    2. 交易特征:
    - 总交易次数: {stats['总交易次数']}
    - 平均持仓周期: {stats['平均持仓周期 (周)']:.1f}周
    - 强信号比例: {len(trade_log[trade_log['信号强度'] >=2])/len(trade_log) if len(trade_log) > 0 else 0:.1%}

    3. 分信号强度表现:
    """
    for strength in [1, 2]:
        subset = trade_log[trade_log['信号强度'] == strength]
        if not subset.empty:
            win_rate = (subset['平仓价值'] > subset['利差值']).mean()*100
            report += f"\n    - 强度{strength}信号: {len(subset)}次, 胜率{win_rate:.1f}%"

    return report

gov_report = create_performance_report(gov_stats, gov_trade_log, "国债30年-10年利差")
local_report = create_performance_report(local_stats, local_trade_log, "地方债30年-国债30年利差")

# 生成对比分析
comparison_df = pd.DataFrame([gov_stats, local_stats],
                            index=['国债30年-10年利差', '地方债30年-国债30年利差'])

# 绘制回测结果
def plot_backtest_results(backtest_df, spread_column, title, is_gov_spread=True):
    fig, axs = plt.subplots(2, 1, figsize=(14, 12))

    # 绘制利差和持仓
    axs[0].plot(backtest_df.index, backtest_df[spread_column], label=spread_column, color='blue', linewidth=2)

    # 标记买入和卖出区间
    buy_periods = backtest_df[backtest_df['持仓'] > 0]
    sell_periods = backtest_df[backtest_df['持仓'] < 0]

    # 高亮买入区间
    for i in range(len(buy_periods)):
        if i < len(buy_periods) - 1:
            axs[0].axvspan(buy_periods.index[i], buy_periods.index[i+1],
                          alpha=0.2, color='green')
            # 添加买入标签
            mid_point = buy_periods.index[i] + (buy_periods.index[i+1] - buy_periods.index[i])/2
            if is_gov_spread:
                axs[0].text(mid_point, backtest_df[spread_column].max()*0.95,
                           '买入30年国债\n卖出10年国债',
                           ha='center', va='top',
                           bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.7))
            else:
                axs[0].text(mid_point, backtest_df[spread_column].max()*0.95,
                           '买入30年地方债\n卖出30年国债',
                           ha='center', va='top',
                           bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.7))

    # 高亮卖出区间
    for i in range(len(sell_periods)):
        if i < len(sell_periods) - 1:
            axs[0].axvspan(sell_periods.index[i], sell_periods.index[i+1],
                          alpha=0.2, color='red')
            # 添加卖出标签
            mid_point = sell_periods.index[i] + (sell_periods.index[i+1] - sell_periods.index[i])/2
            if is_gov_spread:
                axs[0].text(mid_point, backtest_df[spread_column].min()*1.05,
                           '买入10年国债\n卖出30年国债',
                           ha='center', va='bottom',
                           bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.7))
            else:
                axs[0].text(mid_point, backtest_df[spread_column].min()*1.05,
                           '买入30年国债\n卖出30年地方债',
                           ha='center', va='bottom',
                           bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.7))

    axs[0].set_title(f'{title}交易持仓分析', fontsize=16)
    axs[0].set_xlabel('日期', fontsize=14)
    axs[0].set_ylabel('利差值 (BP)', fontsize=14)
    axs[0].legend(loc='best', fontsize=12)
    axs[0].grid(True, alpha=0.3)

    # 绘制累计收益
    axs[1].plot(backtest_df.index, backtest_df['累计收益'],
               color='blue', label='累计收益', linewidth=2)

    # 标记最大回撤
    max_dd_end = backtest_df['累计收益'].idxmin()
    max_dd_start = backtest_df['累计收益'][:max_dd_end].idxmax()

    if max_dd_start < max_dd_end:
        axs[1].axvspan(max_dd_start, max_dd_end, alpha=0.2, color='red')
        axs[1].annotate('最大回撤区间',
                       xy=(max_dd_end, backtest_df.loc[max_dd_end, '累计收益']),
                       xytext=(30, -30),
                       textcoords='offset points',
                       arrowprops=dict(arrowstyle='->', connectionstyle='arc3,rad=0.3'))

    axs[1].set_title('策略累计收益', fontsize=16)
    axs[1].set_xlabel('日期', fontsize=14)
    axs[1].set_ylabel('累计收益', fontsize=14)
    axs[1].legend(loc='best', fontsize=12)
    axs[1].grid(True, alpha=0.3)

    # 格式化x轴日期
    for ax in axs:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.MonthLocator(interval=1))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

    plt.tight_layout()
    return fig

# 绘制回测结果
gov_backtest_fig = plot_backtest_results(gov_backtest_weekly, '利差30年减10年', '国债30年-10年', is_gov_spread=True)
local_backtest_fig = plot_backtest_results(local_backtest_weekly, '利差地方债减国债', '地方债30年-国债30年', is_gov_spread=False)

# 利差预测模型
def forecast_spread(df, spread_column, forecast_periods=2, title=''):
    """使用ARIMA模型预测未来利差 (仅预测5-6月)"""
    # 准备数据
    data = df[spread_column].dropna()

    # 确保数据足够长
    if len(data) < 30:
        print(f"数据量不足，无法进行可靠预测: {len(data)}个数据点")
        return None, None

    # 寻找最佳ARIMA参数
    best_aic = float('inf')
    best_params = None

    # 尝试不同的ARIMA参数
    for p in range(0, 4):
        for d in range(0, 2):
            for q in range(0, 4):
                try:
                    model = ARIMA(data, order=(p, d, q))
                    results = model.fit()
                    if results.aic < best_aic:
                        best_aic = results.aic
                        best_params = (p, d, q)
                except:
                    continue

    # 使用最佳参数拟合模型
    if best_params:
        print(f"为{spread_column}找到最佳ARIMA参数: {best_params}")
        model = ARIMA(data, order=best_params)
        results = model.fit()

        # 预测未来2个月 (5-6月)
        # 不使用last_date，直接指定预测日期
        future_dates = pd.date_range(start=pd.Timestamp('2025-05-01'),
                                     periods=forecast_periods,
                                     freq='MS')

        forecast = results.forecast(steps=forecast_periods)
        forecast_df = pd.DataFrame({
            spread_column: forecast
        }, index=future_dates)

        # 计算预测区间
        pred_ci = results.get_forecast(forecast_periods).conf_int()
        lower_ci = pred_ci.iloc[:, 0]
        upper_ci = pred_ci.iloc[:, 1]

        # 绘制预测结果
        fig, ax = plt.subplots(figsize=(14, 8))

        # 绘制历史数据 (只显示最近3个月的历史数据)
        recent_data = data.iloc[-90:]  # 约3个月的交易日
        ax.plot(recent_data.index, recent_data, label='历史数据', color='blue', linewidth=2)

        # 绘制预测数据
        ax.plot(forecast_df.index, forecast_df[spread_column],
                label='预测', color='red', linestyle='--', linewidth=2)

        # 绘制预测区间
        ax.fill_between(forecast_df.index,
                        lower_ci,
                        upper_ci,
                        color='pink', alpha=0.3, label='95%置信区间')

        # 添加预测值标签
        for date, value in zip(forecast_df.index, forecast_df[spread_column]):
            ax.annotate(f'{value:.2f}',
                       xy=(date, value),
                       xytext=(0, 10),
                       textcoords='offset points',
                       ha='center',
                       bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.7))

        # 设置图表标题和标签
        ax.set_title(f'{title}利差预测 (2025年5-6月) ARIMA{best_params}', fontsize=16)
        ax.set_xlabel('日期', fontsize=14)
        ax.set_ylabel('利差值 (BP)', fontsize=14)
        ax.legend(loc='best', fontsize=12)
        ax.grid(True, alpha=0.3)

        # 格式化x轴日期
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
        ax.xaxis.set_major_locator(mdates.WeekdayLocator(interval=1))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

        plt.tight_layout()

        return fig, forecast_df
    else:
        print(f"无法为{spread_column}找到合适的ARIMA参数")
        return None, None

# 绘制MA250信号图
gov_ma250_fig = plot_ma250_signals(gov_spread_df, '利差30年减10年', '国债30年-10年', is_gov_spread=True)
local_ma250_fig = plot_ma250_signals(local_spread_df, '利差地方债减国债', '地方债30年-国债30年', is_gov_spread=False)

# 执行利差预测 (仅预测5-6月)
gov_forecast_fig, gov_forecast_df = forecast_spread(gov_spread_df, '利差30年减10年',
                                                  forecast_periods=2,
                                                  title='国债30年-10年')

local_forecast_fig, local_forecast_df = forecast_spread(local_spread_df, '利差地方债减国债',
                                                      forecast_periods=2,
                                                      title='地方债30年-国债30年')

# 显示增强版报告
print("="*50)
print("国债30年-10年利差分析报告")
print("="*50)
print(gov_report)
print("\n")

print("="*50)
print("地方债30年-国债30年利差分析报告")
print("="*50)
print(local_report)
print("\n")

print("="*50)
print("策略对比分析:")
print("="*50)
print(comparison_df)
print("\n")

if gov_forecast_df is not None:
    print("="*50)
    print("国债30年-10年利差预测 (2025年5月-12月):")
    print("="*50)
    print(gov_forecast_df)
    print("\n")

if local_forecast_df is not None:
    print("="*50)
    print("地方债30年-国债30年利差预测 (2025年5月-12月):")
    print("="*50)
    print(local_forecast_df)
    print("\n")

# 保存图表到当前目录
# 国债利差分析图表
gov_bb_fig.savefig('国债30年10年_布林带分析.png')
gov_rsi_fig.savefig('国债30年10年_RSI分析.png')
gov_macd_fig.savefig('国债30年10年_MACD分析.png')
gov_kdj_fig.savefig('国债30年10年_KDJ分析.png')
gov_signals_fig.savefig('国债30年10年_交易信号分析.png')
gov_ma250_fig.savefig('国债30年10年_MA250交易信号.png')
gov_backtest_fig.savefig('国债30年10年_回测结果.png')
if gov_forecast_fig:
    gov_forecast_fig.savefig('国债30年10年_5-6月预测.png')

# 地方债利差分析图表
local_bb_fig.savefig('地方债30年国债30年_布林带分析.png')
local_rsi_fig.savefig('地方债30年国债30年_RSI分析.png')
local_macd_fig.savefig('地方债30年国债30年_MACD分析.png')
local_kdj_fig.savefig('地方债30年国债30年_KDJ分析.png')
local_signals_fig.savefig('地方债30年国债30年_交易信号分析.png')
local_ma250_fig.savefig('地方债30年国债30年_MA250交易信号.png')
local_backtest_fig.savefig('地方债30年国债30年_回测结果.png')
if local_forecast_fig:
    local_forecast_fig.savefig('地方债30年国债30年_5-6月预测.png')

# 绘制策略对比分析图表
plt.figure(figsize=(12, 8))
comparison_plot_df = comparison_df.drop(['总交易次数', '平均持仓周期 (周)'], axis=1)
sns.heatmap(comparison_plot_df, annot=True, fmt=".1f", cmap="YlGnBu", linewidths=0.5)
plt.title('利差交易策略绩效对比分析', fontsize=16)
plt.tight_layout()
plt.savefig('利差交易策略绩效对比.png')

print("所有图表已保存到当前目录")

# 显示所有图表
plt.show()
