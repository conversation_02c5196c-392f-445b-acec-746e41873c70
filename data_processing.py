import pandas as pd
import numpy as np
from sklearn.experimental import enable_iterative_imputer
from sklearn.impute import IterativeImputer
from sklearn.ensemble import RandomForestRegressor

def load_and_preprocess_data():
    # 加载Excel数据（跳过可能存在的标题行）
    data_path = '/Users/<USER>/Desktop/利率领先指数底稿0703.xlsx'
    
    # 尝试不同方式读取文件
    for skip_rows in range(0, 5):
        try:
            df = pd.read_excel(data_path, sheet_name='指数底稿', skiprows=skip_rows)
            # 查找日期列（假设第一列包含日期）
            date_col = df.columns[0]
            df[date_col] = pd.to_datetime(df[date_col], errors='coerce')
            df = df.dropna(subset=[date_col])
            df.set_index(date_col, inplace=True)
            break
        except (KeyError, ValueError, TypeError):
            continue
    else:
        raise ValueError("无法正确解析Excel文件，请检查文件格式")
    
    # 删除包含非数值的元数据行
    df = df.apply(pd.to_numeric, errors='coerce').dropna(how='all')
    
    # 定义各指标的填充策略
    fill_strategies = {
        '季节性指标': ['中国:社会消费品零售总额:餐饮收入:当月同比(1-2月合并)', '中国:销量:汽车:当月同比', 
                  '中国:电影票房收入:当月值', '北京:地铁客运量:月:平均值'],
        '调查类指标': ['中国:消费者信心指数', '中国:制造业PMI:新订单'],
        '金融政策指标': ['R007:月:平均值', 'DR007:月:平均值', 'R007-DR007', 
                    '中国:中期借贷便利(MLF)投放数量:1年:月:平均值', '中国:逆回购数量:7天:月:平均值'],
        '其他连续指标': list(set(df.columns) - set(['中国:社会消费品零售总额:餐饮收入:当月同比(1-2月合并)', '中国:销量:汽车:当月同比',
                                      '中国:电影票房收入:当月值', '北京:地铁客运量:月:平均值', '中国:消费者信心指数',
                                      '中国:制造业PMI:新订单', 'R007:月:平均值', 'DR007:月:平均值', 'R007-DR007',
                                      '中国:中期借贷便利(MLF)投放数量:1年:月:平均值', '中国:逆回购数量:7天:月:平均值']))
    }
    
    # 应用填充策略
    for col in fill_strategies['季节性指标']:
        if col in df.columns:
            # 季节性指标：12个月移动平均
            df[col] = df[col].fillna(df[col].rolling(12, min_periods=1).mean())
    
    for col in fill_strategies['调查类指标']:
        if col in df.columns:
            # 调查类指标：线性插值
            df[col] = df[col].interpolate(method='linear')
    
    for col in fill_strategies['金融政策指标']:
        if col in df.columns:
            # 金融政策指标：前值填充
            df[col] = df[col].fillna(method='ffill')
    
    # 其他连续指标：使用随机森林进行多重插补
    other_cols = fill_strategies['其他连续指标']
    if other_cols:
        imputer = IterativeImputer(
            estimator=RandomForestRegressor(n_estimators=100),
            max_iter=20, random_state=42,  # 增加迭代次数
            tol=0.01  # 放宽收敛阈值
        )
        df[other_cols] = imputer.fit_transform(df[other_cols])
    
    # 全局填充：确保无缺失值
    # 先向前填充，再向后填充
    df = df.fillna(method='ffill').fillna(method='bfill')
    
    # 最终检查
    if df.isnull().any().any():
        raise ValueError("数据仍包含缺失值，请检查预处理流程")
    
    return df

if __name__ == "__main__":
    processed_data = load_and_preprocess_data()
    processed_data.to_csv('processed_data.csv')
    print("数据预处理完成，结果已保存至processed_data.csv")
