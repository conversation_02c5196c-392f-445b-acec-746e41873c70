import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
import os
from itertools import combinations
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['savefig.dpi'] = 300

# 创建输出文件夹
output_dir = '/Users/<USER>/Desktop/量化择时胜率'
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

print("=" * 80)
print("30-10y利差因子组合深度分析")
print("智能因子组合策略构建")
print("=" * 80)

# 加载数据
file_path = '/Users/<USER>/Desktop/择时胜率.xlsx'
df = pd.read_excel(file_path)

# 数据预处理
df_processed = df.fillna(method='ffill').fillna(method='bfill').copy()
if 'Unnamed: 0' in df_processed.columns:
    df_processed = df_processed.rename(columns={'Unnamed: 0': '日期'})

# 构建目标变量
df_processed['30-10y_next'] = df_processed['30-10y'].shift(-1)
df_processed['30-10y_change'] = df_processed['30-10y_next'] - df_processed['30-10y']
df_processed['direction_next'] = (df_processed['30-10y_change'] > 0).astype(int)
df_processed = df_processed[:-1].copy()

# 定义因子
factor_categories = {
    '基本面因子': ['水泥价格指数', '建材综合指数', '南华金属指数', '南华能化指数', '南华工业品指数'],
    '技术面因子': ['偏离度（偏离均线）', '偏离度（偏离MA+std）'],
    '资金面因子': ['R007', 'DR007', '成交量:DR007', '成交量:R007'],
    '权益市场因子': ['1/沪深300PE-10y'],
    '成交因子': ['30Y成交量']
}

all_factors = []
for factors in factor_categories.values():
    all_factors.extend(factors)

available_factors = [f for f in all_factors if f in df_processed.columns]

print(f"可用因子数量: {len(available_factors)}")
print(f"基准走阔概率: {df_processed['direction_next'].mean():.3f}")

def analyze_factor_characteristics(df, factors):
    """深度分析因子特征"""
    print(f"\n{'='*50}")
    print("因子特征深度分析")
    print(f"{'='*50}")

    factor_stats = []

    for factor in factors:
        if factor not in df.columns:
            continue

        # 基本统计
        factor_data = df[factor]
        target_data = df['direction_next']

        # 相关性分析
        correlation = factor_data.corr(target_data)
        _, p_value = stats.pearsonr(factor_data, target_data)

        # 分位数分析
        q25 = factor_data.quantile(0.25)
        q50 = factor_data.quantile(0.50)
        q75 = factor_data.quantile(0.75)

        # 各分位数组的走阔概率
        low_group = df[factor_data <= q25]['direction_next'].mean()
        mid_low_group = df[(factor_data > q25) & (factor_data <= q50)]['direction_next'].mean()
        mid_high_group = df[(factor_data > q50) & (factor_data <= q75)]['direction_next'].mean()
        high_group = df[factor_data > q75]['direction_next'].mean()

        # 计算因子的预测能力（基于最优分组）
        groups = [low_group, mid_low_group, mid_high_group, high_group]
        max_prob = max(groups)
        min_prob = min(groups)
        spread_range = max_prob - min_prob

        # 计算单因子胜率（基于中位数分组）
        if correlation > 0:
            prediction = (factor_data > q50).astype(int)
        else:
            prediction = (factor_data <= q50).astype(int)

        win_rate = (prediction == target_data).mean()

        factor_stats.append({
            'factor': factor,
            'correlation': correlation,
            'p_value': p_value,
            'significant': p_value < 0.05,
            'win_rate': win_rate,
            'spread_range': spread_range,
            'low_group_prob': low_group,
            'mid_low_prob': mid_low_group,
            'mid_high_prob': mid_high_group,
            'high_group_prob': high_group,
            'volatility': factor_data.std(),
            'skewness': factor_data.skew(),
            'kurtosis': factor_data.kurtosis()
        })

        print(f"\n{factor}:")
        print(f"  相关系数: {correlation:.4f} (p={p_value:.4f})")
        print(f"  单因子胜率: {win_rate:.3f}")
        print(f"  预测能力范围: {spread_range:.3f}")
        print(f"  分位数走阔概率: [{low_group:.3f}, {mid_low_group:.3f}, {mid_high_group:.3f}, {high_group:.3f}]")

    return pd.DataFrame(factor_stats)

factor_stats = analyze_factor_characteristics(df_processed, available_factors)

def create_smart_factor_combinations(df, factor_stats, min_factors=2, max_factors=4):
    """创建智能因子组合"""
    print(f"\n{'='*50}")
    print("智能因子组合策略构建")
    print(f"{'='*50}")

    # 选择有潜力的因子（胜率>50%或预测能力范围>0.1）
    potential_factors = factor_stats[
        (factor_stats['win_rate'] > 0.50) |
        (factor_stats['spread_range'] > 0.1) |
        (factor_stats['significant'] == True)
    ]['factor'].tolist()

    print(f"筛选出有潜力的因子: {len(potential_factors)}")
    for factor in potential_factors:
        stats_row = factor_stats[factor_stats['factor'] == factor].iloc[0]
        print(f"  {factor}: 胜率{stats_row['win_rate']:.3f}, 范围{stats_row['spread_range']:.3f}")

    combination_results = []

    # 测试不同的因子组合
    for num_factors in range(min_factors, min(max_factors + 1, len(potential_factors) + 1)):
        print(f"\n测试{num_factors}因子组合...")

        for combo in combinations(potential_factors, num_factors):
            combo_name = " + ".join(combo)

            # 策略1: 简单投票策略
            signals = pd.DataFrame(index=df.index)

            for factor in combo:
                factor_stats_row = factor_stats[factor_stats['factor'] == factor].iloc[0]
                factor_median = df[factor].median()

                if factor_stats_row['correlation'] > 0:
                    signals[f'{factor}_signal'] = (df[factor] > factor_median).astype(int)
                else:
                    signals[f'{factor}_signal'] = (df[factor] <= factor_median).astype(int)

            signals['vote_sum'] = signals.sum(axis=1)
            signals['simple_vote'] = (signals['vote_sum'] > num_factors / 2).astype(int)

            simple_vote_accuracy = (signals['simple_vote'] == df['direction_next']).mean()

            # 策略2: 加权投票策略
            signals['weighted_sum'] = 0
            total_weight = 0

            for factor in combo:
                factor_stats_row = factor_stats[factor_stats['factor'] == factor].iloc[0]
                weight = max(factor_stats_row['win_rate'], factor_stats_row['spread_range'])
                factor_median = df[factor].median()

                if factor_stats_row['correlation'] > 0:
                    factor_signal = (df[factor] > factor_median).astype(int)
                else:
                    factor_signal = (df[factor] <= factor_median).astype(int)

                signals['weighted_sum'] += factor_signal * weight
                total_weight += weight

            signals['weighted_vote'] = (signals['weighted_sum'] > total_weight / 2).astype(int)
            weighted_vote_accuracy = (signals['weighted_vote'] == df['direction_next']).mean()

            # 策略3: 阈值优化策略
            best_threshold_accuracy = 0
            best_threshold = num_factors / 2

            for threshold in np.arange(1, num_factors + 1):
                threshold_pred = (signals['vote_sum'] >= threshold).astype(int)
                threshold_accuracy = (threshold_pred == df['direction_next']).mean()

                if threshold_accuracy > best_threshold_accuracy:
                    best_threshold_accuracy = threshold_accuracy
                    best_threshold = threshold

            # 记录结果
            combination_results.append({
                'combination': combo_name,
                'factors': combo,
                'num_factors': num_factors,
                'simple_vote_accuracy': simple_vote_accuracy,
                'weighted_vote_accuracy': weighted_vote_accuracy,
                'optimized_threshold_accuracy': best_threshold_accuracy,
                'best_threshold': best_threshold,
                'max_accuracy': max(simple_vote_accuracy, weighted_vote_accuracy, best_threshold_accuracy)
            })

    return pd.DataFrame(combination_results)

combination_results = create_smart_factor_combinations(df_processed, factor_stats)

def analyze_advanced_combinations(df, factor_stats):
    """分析高级因子组合策略"""
    print(f"\n{'='*50}")
    print("高级因子组合策略分析")
    print(f"{'='*50}")

    # 选择最佳因子
    top_factors = factor_stats.nlargest(5, 'win_rate')['factor'].tolist()
    print(f"TOP5因子: {top_factors}")

    advanced_strategies = []

    # 策略1: 分层投票策略
    print(f"\n策略1: 分层投票策略")

    # 技术面因子权重更高
    tech_factors = ['偏离度（偏离均线）', '偏离度（偏离MA+std）']
    other_factors = [f for f in top_factors if f not in tech_factors and f in df.columns]

    signals = pd.DataFrame(index=df.index)

    # 技术面信号（权重2）
    tech_score = 0
    for factor in tech_factors:
        if factor in df.columns:
            factor_stats_row = factor_stats[factor_stats['factor'] == factor].iloc[0]
            factor_median = df[factor].median()

            if factor_stats_row['correlation'] > 0:
                signal = (df[factor] > factor_median).astype(int)
            else:
                signal = (df[factor] <= factor_median).astype(int)

            tech_score += signal * 2  # 技术面权重为2

    # 其他因子信号（权重1）
    other_score = 0
    for factor in other_factors[:3]:  # 只取前3个其他因子
        if factor in df.columns:
            factor_stats_row = factor_stats[factor_stats['factor'] == factor].iloc[0]
            factor_median = df[factor].median()

            if factor_stats_row['correlation'] > 0:
                signal = (df[factor] > factor_median).astype(int)
            else:
                signal = (df[factor] <= factor_median).astype(int)

            other_score += signal * 1  # 其他因子权重为1

    total_score = tech_score + other_score
    max_score = len(tech_factors) * 2 + min(3, len(other_factors)) * 1

    signals['layered_prediction'] = (total_score > max_score / 2).astype(int)
    layered_accuracy = (signals['layered_prediction'] == df['direction_next']).mean()

    advanced_strategies.append({
        'strategy': '分层投票策略',
        'accuracy': layered_accuracy,
        'description': '技术面因子权重2，其他因子权重1'
    })

    print(f"  分层投票策略胜率: {layered_accuracy:.3f}")

    # 策略2: 动态阈值策略
    print(f"\n策略2: 动态阈值策略")

    # 基于市场波动性调整阈值
    volatility = df['30-10y'].rolling(20).std()
    high_vol_periods = volatility > volatility.median()

    # 高波动期使用更严格的阈值
    dynamic_prediction = signals['layered_prediction'].copy()

    # 在高波动期，需要更多因子确认
    high_vol_mask = high_vol_periods & (total_score <= max_score * 0.6)
    dynamic_prediction[high_vol_mask] = 0  # 高波动期更保守

    dynamic_accuracy = (dynamic_prediction == df['direction_next']).mean()

    advanced_strategies.append({
        'strategy': '动态阈值策略',
        'accuracy': dynamic_accuracy,
        'description': '根据市场波动性调整预测阈值'
    })

    print(f"  动态阈值策略胜率: {dynamic_accuracy:.3f}")

    # 策略3: 机器学习增强策略
    print(f"\n策略3: 机器学习增强策略")

    # 准备特征
    feature_factors = top_factors[:5]
    X = df[feature_factors].fillna(method='ffill')
    y = df['direction_next']

    # 时间序列分割
    train_size = int(len(X) * 0.7)
    X_train, X_test = X[:train_size], X[train_size:]
    y_train, y_test = y[:train_size], y[train_size:]

    # 随机森林
    rf_model = RandomForestClassifier(n_estimators=100, random_state=42)
    rf_model.fit(X_train, y_train)
    rf_pred = rf_model.predict(X_test)
    rf_accuracy = accuracy_score(y_test, rf_pred)

    # 逻辑回归
    lr_model = LogisticRegression(random_state=42)
    lr_model.fit(X_train, y_train)
    lr_pred = lr_model.predict(X_test)
    lr_accuracy = accuracy_score(y_test, lr_pred)

    advanced_strategies.extend([
        {
            'strategy': '随机森林策略',
            'accuracy': rf_accuracy,
            'description': f'使用{len(feature_factors)}个因子的随机森林模型'
        },
        {
            'strategy': '逻辑回归策略',
            'accuracy': lr_accuracy,
            'description': f'使用{len(feature_factors)}个因子的逻辑回归模型'
        }
    ])

    print(f"  随机森林策略胜率: {rf_accuracy:.3f}")
    print(f"  逻辑回归策略胜率: {lr_accuracy:.3f}")

    return pd.DataFrame(advanced_strategies), signals

advanced_strategies, final_signals = analyze_advanced_combinations(df_processed, factor_stats)

def create_comprehensive_visualizations(combination_results, advanced_strategies, factor_stats, df, output_dir):
    """创建综合可视化分析"""
    print(f"\n{'='*50}")
    print("创建综合可视化分析")
    print(f"{'='*50}")

    # 1. 因子组合胜率对比图
    plt.figure(figsize=(16, 10))

    # 选择最佳组合结果
    top_combinations = combination_results.nlargest(10, 'max_accuracy')

    x = range(len(top_combinations))
    width = 0.25

    plt.bar([i - width for i in x], top_combinations['simple_vote_accuracy'],
            width, label='简单投票', alpha=0.8, color='lightblue')
    plt.bar(x, top_combinations['weighted_vote_accuracy'],
            width, label='加权投票', alpha=0.8, color='lightgreen')
    plt.bar([i + width for i in x], top_combinations['optimized_threshold_accuracy'],
            width, label='优化阈值', alpha=0.8, color='lightcoral')

    plt.axhline(y=0.5, color='red', linestyle='--', label='随机基准线', alpha=0.7)
    plt.axhline(y=df['direction_next'].mean(), color='blue', linestyle='--',
                label=f'基准走阔概率({df["direction_next"].mean():.3f})', alpha=0.7)

    plt.xlabel('因子组合')
    plt.ylabel('预测胜率')
    plt.title('TOP10因子组合策略胜率对比')
    plt.xticks(x, [combo[:30] + '...' if len(combo) > 30 else combo
                   for combo in top_combinations['combination']], rotation=45, ha='right')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/因子组合胜率对比.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 2. 高级策略胜率对比
    plt.figure(figsize=(12, 8))

    strategies = advanced_strategies['strategy'].tolist()
    accuracies = advanced_strategies['accuracy'].tolist()

    colors = ['skyblue', 'lightgreen', 'orange', 'pink']
    bars = plt.bar(strategies, accuracies, color=colors[:len(strategies)], alpha=0.8)

    plt.axhline(y=0.5, color='red', linestyle='--', label='随机基准线', alpha=0.7)
    plt.axhline(y=df['direction_next'].mean(), color='blue', linestyle='--',
                label=f'基准走阔概率({df["direction_next"].mean():.3f})', alpha=0.7)

    # 标注数值
    for bar, acc in zip(bars, accuracies):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005,
                f'{acc:.3f}', ha='center', va='bottom', fontweight='bold')

    plt.xlabel('策略类型')
    plt.ylabel('预测胜率')
    plt.title('高级因子组合策略胜率对比')
    plt.xticks(rotation=45, ha='right')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/高级策略胜率对比.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 3. 因子重要性分析
    plt.figure(figsize=(14, 8))

    factor_importance = factor_stats.sort_values('win_rate', ascending=False)

    # 创建双y轴图
    fig, ax1 = plt.subplots(figsize=(14, 8))

    color = 'tab:blue'
    ax1.set_xlabel('因子')
    ax1.set_ylabel('预测胜率', color=color)
    bars1 = ax1.bar(range(len(factor_importance)), factor_importance['win_rate'],
                    color=color, alpha=0.6, label='预测胜率')
    ax1.tick_params(axis='y', labelcolor=color)
    ax1.axhline(y=0.5, color='red', linestyle='--', alpha=0.5)

    ax2 = ax1.twinx()
    color = 'tab:orange'
    ax2.set_ylabel('预测能力范围', color=color)
    line = ax2.plot(range(len(factor_importance)), factor_importance['spread_range'],
                    color=color, marker='o', linewidth=2, label='预测能力范围')
    ax2.tick_params(axis='y', labelcolor=color)

    plt.title('因子重要性综合分析')
    plt.xticks(range(len(factor_importance)), factor_importance['factor'],
               rotation=45, ha='right')

    # 合并图例
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper right')

    plt.tight_layout()
    plt.savefig(f'{output_dir}/因子重要性分析.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 4. 策略信号时序图
    plt.figure(figsize=(16, 10))

    # 选择最佳策略的信号
    best_strategy_idx = advanced_strategies['accuracy'].idxmax()
    best_strategy_name = advanced_strategies.loc[best_strategy_idx, 'strategy']

    # 绘制利差走势和预测信号
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(16, 12),
                                        gridspec_kw={'height_ratios': [2, 1, 1]})

    # 利差走势
    ax1.plot(df.index, df['30-10y'], label='30-10y利差', color='blue', linewidth=1.5)
    ax1.set_ylabel('利差 (BP)')
    ax1.set_title('30-10y利差走势与预测信号')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 实际方向
    ax2.plot(df.index[:-1], df['direction_next'], label='实际方向',
             color='black', linewidth=1, marker='o', markersize=2)
    ax2.set_ylabel('方向 (1=走阔, 0=收窄)')
    ax2.set_title('实际利差变化方向')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # 预测信号（使用分层投票策略的信号）
    if 'layered_prediction' in final_signals.columns:
        ax3.plot(df.index[:-1], final_signals['layered_prediction'],
                 label=f'{best_strategy_name}预测', color='red',
                 linewidth=1, marker='s', markersize=2)

        # 标记预测正确和错误的点
        correct_pred = final_signals['layered_prediction'] == df['direction_next']
        ax3.scatter(df.index[:-1][correct_pred],
                   final_signals['layered_prediction'][correct_pred],
                   color='green', s=10, alpha=0.6, label='预测正确')
        ax3.scatter(df.index[:-1][~correct_pred],
                   final_signals['layered_prediction'][~correct_pred],
                   color='red', s=10, alpha=0.6, label='预测错误')

    ax3.set_ylabel('预测方向')
    ax3.set_xlabel('时间')
    ax3.set_title(f'{best_strategy_name}预测信号')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(f'{output_dir}/策略信号时序分析.png', dpi=300, bbox_inches='tight')
    plt.show()

create_comprehensive_visualizations(combination_results, advanced_strategies, factor_stats, df_processed, output_dir)

def generate_final_strategy_report(combination_results, advanced_strategies, factor_stats, df, output_dir):
    """生成最终策略报告"""
    print(f"\n{'='*50}")
    print("生成最终策略分析报告")
    print(f"{'='*50}")

    # 找出最佳策略
    best_combination = combination_results.loc[combination_results['max_accuracy'].idxmax()]
    best_advanced = advanced_strategies.loc[advanced_strategies['accuracy'].idxmax()]

    # 基本统计
    baseline_accuracy = df['direction_next'].mean()
    total_samples = len(df)

    # 生成报告
    report = f"""
30-10y利差因子组合深度分析报告
=====================================

一、分析概况
- 总样本数量: {total_samples}个交易日
- 基准走阔概率: {baseline_accuracy:.3f} ({baseline_accuracy*100:.1f}%)
- 分析因子数量: {len(factor_stats)}个
- 测试组合数量: {len(combination_results)}个

二、单因子表现分析
"""

    # TOP5单因子
    top5_factors = factor_stats.nlargest(5, 'win_rate')
    report += "TOP5单因子胜率:\n"
    for i, (_, factor) in enumerate(top5_factors.iterrows(), 1):
        report += f"{i}. {factor['factor']}: {factor['win_rate']:.3f} ({factor['win_rate']*100:.1f}%)\n"

    report += f"""
三、因子组合策略结果

最佳传统组合策略:
- 组合因子: {best_combination['combination']}
- 因子数量: {best_combination['num_factors']}个
- 简单投票胜率: {best_combination['simple_vote_accuracy']:.3f} ({best_combination['simple_vote_accuracy']*100:.1f}%)
- 加权投票胜率: {best_combination['weighted_vote_accuracy']:.3f} ({best_combination['weighted_vote_accuracy']*100:.1f}%)
- 优化阈值胜率: {best_combination['optimized_threshold_accuracy']:.3f} ({best_combination['optimized_threshold_accuracy']*100:.1f}%)
- 最高胜率: {best_combination['max_accuracy']:.3f} ({best_combination['max_accuracy']*100:.1f}%)

最佳高级策略:
- 策略名称: {best_advanced['strategy']}
- 策略胜率: {best_advanced['accuracy']:.3f} ({best_advanced['accuracy']*100:.1f}%)
- 策略描述: {best_advanced['description']}

四、策略性能提升分析
"""

    # 计算提升幅度
    best_overall_accuracy = max(best_combination['max_accuracy'], best_advanced['accuracy'])
    improvement_vs_random = (best_overall_accuracy - 0.5) * 100
    improvement_vs_baseline = (best_overall_accuracy - baseline_accuracy) * 100

    report += f"""
- 相比随机预测提升: {improvement_vs_random:.1f}个百分点
- 相比基准概率提升: {improvement_vs_baseline:.1f}个百分点
- 最高预测胜率: {best_overall_accuracy:.3f} ({best_overall_accuracy*100:.1f}%)

五、推荐交易策略
"""

    # 推荐策略
    if best_advanced['accuracy'] > best_combination['max_accuracy']:
        recommended_strategy = best_advanced['strategy']
        recommended_accuracy = best_advanced['accuracy']

        if '分层投票' in recommended_strategy:
            report += f"""
推荐策略: {recommended_strategy}
预期胜率: {recommended_accuracy:.3f} ({recommended_accuracy*100:.1f}%)

具体实施方案:
1. 技术面因子权重设为2:
   - 偏离度（偏离均线）
   - 偏离度（偏离MA+std）

2. 其他重要因子权重设为1:
   - 选择TOP3非技术面因子

3. 交易信号生成:
   - 当加权得分 > 总权重的50%时，预测利差走阔
   - 当加权得分 ≤ 总权重的50%时，预测利差收窄

4. 交易建议:
   - 利差走阔信号: 买入30年国债，卖出10年国债
   - 利差收窄信号: 买入10年国债，卖出30年国债
"""
    else:
        recommended_strategy = best_combination['combination']
        recommended_accuracy = best_combination['max_accuracy']

        report += f"""
推荐策略: 多因子组合策略
使用因子: {recommended_strategy}
预期胜率: {recommended_accuracy:.3f} ({recommended_accuracy*100:.1f}%)

具体实施方案:
1. 对每个因子生成信号（基于历史中位数）
2. 使用优化阈值进行投票决策
3. 最优投票阈值: {best_combination['best_threshold']}
"""

    report += f"""
六、风险管理建议

1. 仓位管理:
   - 单次交易仓位: 不超过总资金的15-20%
   - 根据信号强度调整仓位大小

2. 止损设置:
   - 技术止损: 利差变化超过2-3BP
   - 时间止损: 持仓超过5个交易日无盈利

3. 信号确认:
   - 建议等待连续2个交易日信号一致
   - 在重要经济数据发布前谨慎交易

4. 模型维护:
   - 每月重新计算因子统计特征
   - 每季度重新评估因子有效性
   - 半年重新优化组合权重

七、预期收益分析

基于{total_samples}个交易日的回测结果:
- 预期胜率: {best_overall_accuracy:.3f}
- 预期年化交易次数: 约200-250次
- 假设每次盈利2BP，亏损1.5BP:
  - 预期年化收益: {(best_overall_accuracy * 2 - (1-best_overall_accuracy) * 1.5) * 225:.1f}BP
  - 年化收益率: 约{(best_overall_accuracy * 2 - (1-best_overall_accuracy) * 1.5) * 225 / 100:.1f}%

八、重要提示

1. 该策略基于历史数据回测，实际表现可能有差异
2. 建议结合基本面分析和市场环境判断
3. 在极端市场条件下，因子有效性可能下降
4. 定期监控策略表现，及时调整参数
"""

    # 保存报告
    with open(f'{output_dir}/因子组合策略深度分析报告.txt', 'w', encoding='utf-8') as f:
        f.write(report)

    print(report)

    # 保存详细结果到Excel
    with pd.ExcelWriter(f'{output_dir}/因子组合策略完整分析.xlsx') as writer:
        factor_stats.to_excel(writer, sheet_name='因子特征分析', index=False)
        combination_results.to_excel(writer, sheet_name='组合策略结果', index=False)
        advanced_strategies.to_excel(writer, sheet_name='高级策略结果', index=False)

        # 最佳策略汇总
        best_strategies = pd.DataFrame([
            {
                'strategy_type': '最佳传统组合',
                'strategy_name': best_combination['combination'],
                'accuracy': best_combination['max_accuracy'],
                'factors': best_combination['combination']
            },
            {
                'strategy_type': '最佳高级策略',
                'strategy_name': best_advanced['strategy'],
                'accuracy': best_advanced['accuracy'],
                'factors': best_advanced['description']
            }
        ])
        best_strategies.to_excel(writer, sheet_name='最佳策略汇总', index=False)

    print(f"\n✓ 完整分析报告已保存到: {output_dir}/因子组合策略深度分析报告.txt")
    print(f"✓ 详细结果Excel已保存到: {output_dir}/因子组合策略完整分析.xlsx")

generate_final_strategy_report(combination_results, advanced_strategies, factor_stats, df_processed, output_dir)

print(f"\n" + "="*80)
print(f"因子组合深度分析完成")
print(f"="*80)

if len(combination_results) > 0:
    best_combo = combination_results.loc[combination_results['max_accuracy'].idxmax()]
    print(f"🏆 最佳组合策略: {best_combo['combination']}")
    print(f"📊 最高组合胜率: {best_combo['max_accuracy']:.3f} ({best_combo['max_accuracy']*100:.1f}%)")

if len(advanced_strategies) > 0:
    best_advanced = advanced_strategies.loc[advanced_strategies['accuracy'].idxmax()]
    print(f"🚀 最佳高级策略: {best_advanced['strategy']}")
    print(f"📈 最高策略胜率: {best_advanced['accuracy']:.3f} ({best_advanced['accuracy']*100:.1f}%)")

print(f"📁 输出目录: {output_dir}")
print(f"="*80)
