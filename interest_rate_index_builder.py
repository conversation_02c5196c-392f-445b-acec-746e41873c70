import pandas as pd
import numpy as np
from statsmodels.tsa.statespace.dynamic_factor import DynamicFactor
from sklearn.preprocessing import StandardScaler
from sklearn.impute import KNNImputer
import matplotlib.pyplot as plt
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['Arial']  # Use Arial to avoid Chinese font issues
matplotlib.rcParams['axes.unicode_minus'] = False  # Properly display minus sign

# Load data from Excel
def load_data():
    # Read both sheets
    indicators = pd.read_excel('/Users/<USER>/Desktop/利率领先指数底稿0703.xlsx', 
                              sheet_name='指数底稿', index_col=0)
    yields = pd.read_excel('/Users/<USER>/Desktop/利率领先指数底稿0703.xlsx', 
                          sheet_name='中债收益率', index_col=0)
    
    # Merge datasets
    full_data = pd.merge(indicators, yields, left_index=True, right_index=True, how='inner')
    return full_data

# Preprocess data
def preprocess_data(data):
    # Handle missing values using KNN imputation
    imputer = KNNImputer(n_neighbors=3)
    imputed = imputer.fit_transform(data)
    data_imputed = pd.DataFrame(imputed, index=data.index, columns=data.columns)
    
    # Normalize features
    scaler = StandardScaler()
    scaled = scaler.fit_transform(data_imputed)
    return pd.DataFrame(scaled, index=data.index, columns=data.columns)

# Build leading index
def build_leading_index(data, target='中债国债到期收益率:10年'):
    # Separate target and features
    y = data[target]
    X = data.drop(columns=[target, '中债国债到期收益率:1年', '10-1Y利差'])
    
    # Dynamic factor model with improved settings
    try:
        model = DynamicFactor(endog=X, k_factors=1, factor_order=2)
        # Increase max iterations for better convergence
        result = model.fit(disp=True, maxiter=1000)  
        
        # Convert factor to pandas Series with proper index
        factor = pd.Series(result.factors.filtered[0], index=X.index)
    except Exception as e:
        print(f"Error building factor model: {str(e)}")
        factor = pd.Series(np.zeros(len(X)), index=X.index)
    
    return factor, result

# Analyze lead-lag relationships
def analyze_lead_lag(factor, yield_10y, max_lag=6):
    correlations = []
    for lag in range(-max_lag, max_lag+1):
        if lag < 0:
            # Leading relationship (factor leads yield)
            shifted_factor = factor.shift(-lag)
            valid_idx = shifted_factor.notnull() & yield_10y.notnull()
            if sum(valid_idx) > 1:
                corr = np.corrcoef(shifted_factor[valid_idx], yield_10y[valid_idx])[0,1]
            else:
                corr = np.nan
        else:
            # Lagging relationship
            shifted_yield = yield_10y.shift(lag)
            valid_idx = factor.notnull() & shifted_yield.notnull()
            if sum(valid_idx) > 1:
                corr = np.corrcoef(factor[valid_idx], shifted_yield[valid_idx])[0,1]
            else:
                corr = np.nan
        correlations.append(corr)
    
    return correlations

# Main execution
if __name__ == "__main__":
    # Define output directory (desktop)
    output_dir = '/Users/<USER>/Desktop/'
    
    # Load and preprocess data with error handling and date parsing
    try:
        raw_data = load_data()
        # Convert index to datetime if not already
        if not isinstance(raw_data.index, pd.DatetimeIndex):
            raw_data.index = pd.to_datetime(raw_data.index)
        processed_data = preprocess_data(raw_data)
    except Exception as e:
        print(f"Data loading error: {str(e)}")
        print("Check Excel file path and sheet names")
        exit(1)
    
    # Ensure yield data is properly formatted
    processed_data['中债国债到期收益率:10年'] = pd.to_numeric(
        processed_data['中债国债到期收益率:10年'], errors='coerce'
    )
    
    # Build leading index
    leading_index, model = build_leading_index(processed_data)
    
    # Analyze lead-lag correlation
    correlations = analyze_lead_lag(
        leading_index, 
        processed_data['中债国债到期收益率:10年']
    )
    
    # Find optimal lead time
    max_corr = max([c for c in correlations if not np.isnan(c)])
    optimal_lag = correlations.index(max_corr) - 6  # Adjust for index offset
    
    # Save results to desktop
    leading_index.to_csv(f'{output_dir}interest_rate_leading_index.csv')
    processed_data.to_csv(f'{output_dir}processed_dataset.csv')
    pd.Series(correlations, index=range(-6,7)).to_csv(f'{output_dir}lead_lag_correlations.csv')
    
    # Generate Chinese statistical report
    with open(f'{output_dir}利率领先指数统计报告.txt', 'w', encoding='utf-8') as f:
        f.write("利率领先指数构建统计报告\n")
        f.write("="*50 + "\n\n")
        f.write(f"数据时间范围: {processed_data.index.min()} 至 {processed_data.index.max()}\n")
        f.write(f"总观测值数量: {len(processed_data)}\n\n")
        
        f.write("指标描述统计:\n")
        f.write(processed_data.describe().to_string() + "\n\n")
        
        f.write("缺失值处理报告:\n")
        f.write("- 使用KNN插补法处理缺失值 (n_neighbors=3)\n")
        f.write(f"- 共处理缺失值: {raw_data.isnull().sum().sum()}个\n\n")
        
        f.write("因子模型摘要:\n")
        f.write(f"- 因子数量: 1\n")
        f.write(f"- 因子阶数: 2\n")
        f.write(f"- 最大似然值: {model.llf:.4f}\n")
        f.write(f"- AIC: {model.aic:.4f}\n")
        f.write(f"- BIC: {model.bic:.4f}\n\n")
        
        f.write("领先滞后分析:\n")
        f.write(f"- 最大相关系数: {max_corr:.4f}\n")
        f.write(f"- 最优领先时间: {abs(optimal_lag)}个月\n")
        f.write(f"- 各滞后期相关系数:\n")
        for lag, corr in zip(range(-6,7), correlations):
            f.write(f"  滞后{lag}月: {corr:.4f}\n")
    
    # Generate visualization with improved formatting
    plt.figure(figsize=(12, 7))
    
    # Primary axis for index
    ax1 = plt.gca()
    ax1.plot(leading_index, 'b-', label='Leading Index')
    ax1.set_xlabel('Date')
    ax1.set_ylabel('Index Value', color='b')
    ax1.tick_params('y', colors='b')
    
    # Secondary axis for yield
    ax2 = ax1.twinx()
    ax2.plot(processed_data['中债国债到期收益率:10年'], 'r-', label='10Y Yield')
    ax2.set_ylabel('Yield (%)', color='r')
    ax2.tick_params('y', colors='r')
    
    # Combined legend
    lines, labels = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines + lines2, labels + labels2, loc='upper left')
    
    plt.title('Interest Rate Leading Index vs 10Y Bond Yield')
    plt.tight_layout()
    plt.savefig(f'{output_dir}index_vs_yield.png', dpi=300)
    
    # Create lead-lag correlation plot
    plt.figure(figsize=(10,6))
    lag_range = range(-6, 7)
    plt.bar(lag_range, correlations)
    plt.axhline(0, color='black', linewidth=0.5)
    plt.xlabel('Lag (months)')
    plt.ylabel('Correlation')
    plt.title('Lead-Lag Correlation Analysis')
    plt.savefig(f'{output_dir}lead_lag_correlation.png', dpi=300)
    
    print("\nIndex construction complete. Results saved to Desktop:")
    print("- interest_rate_leading_index.csv")
    print("- processed_dataset.csv")
    print("- lead_lag_correlations.csv")
    print("- 利率领先指数统计报告.txt")
    print("- index_vs_yield.png")
    print("- lead_lag_correlation.png")
    
    print(f"\n最优领先时间: {abs(optimal_lag)}个月" if optimal_lag < 0 
          else f"最优滞后时间: {optimal_lag}个月")
