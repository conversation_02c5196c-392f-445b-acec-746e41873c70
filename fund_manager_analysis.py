import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
import os
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score
warnings.filterwarnings('ignore')

# 修复中文字体显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.figsize'] = (14, 10)
plt.rcParams['savefig.dpi'] = 300

# 创建输出文件夹
output_dir = '/Users/<USER>/Desktop/量化择时累计收益率'
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

print("=" * 100)
print("🏦 固收量化基金经理深度分析")
print("周度与日度因子模型调仓策略分析")
print("=" * 100)

# 加载数据
file_path = '/Users/<USER>/Desktop/择时胜率0527.xlsx'
try:
    df = pd.read_excel(file_path)
    print(f"✓ 数据加载成功，数据形状: {df.shape}")
except Exception as e:
    print(f"✗ 数据加载失败: {e}")
    exit()

# 数据预处理
df_processed = df.fillna(method='ffill').fillna(method='bfill').copy()
if 'Unnamed: 0' in df_processed.columns:
    df_processed = df_processed.rename(columns={'Unnamed: 0': '日期'})

# 确保日期列为datetime格式
if '日期' in df_processed.columns:
    df_processed['日期'] = pd.to_datetime(df_processed['日期'])
    df_processed = df_processed.sort_values('日期').reset_index(drop=True)
else:
    df_processed['日期'] = pd.date_range(start='2024-01-02', periods=len(df_processed), freq='D')

print(f"数据时间范围: {df_processed['日期'].min()} 到 {df_processed['日期'].max()}")

class QuantitativeFundManager:
    """固收量化基金经理分析类"""

    def __init__(self, data):
        self.data = data.copy()
        self.daily_data = None
        self.weekly_data = None
        self.signals = {}
        self.performance_metrics = {}

    def prepare_daily_data(self):
        """准备日频数据和因子"""
        print(f"\n{'='*60}")
        print("📊 准备日频数据和因子")
        print(f"{'='*60}")

        df = self.data.copy()

        # 创建日频目标变量
        df['30y_next'] = df['30y'].shift(-1)
        df['30y_change'] = df['30y_next'] - df['30y']
        df['30y_direction'] = (df['30y_change'] > 0).astype(int)

        # 创建日频工程因子
        df['30y_momentum_3d'] = df['30y'].pct_change(3)
        df['30y_momentum_5d'] = df['30y'].pct_change(5)
        df['30y_volatility_5d'] = df['30y'].rolling(5).std()
        df['30y_volatility_10d'] = df['30y'].rolling(10).std()

        if '偏离度（偏离均线）' in df.columns:
            df['deviation_momentum'] = df['偏离度（偏离均线）'].diff(1)
            df['deviation_abs'] = abs(df['偏离度（偏离均线）'])

        # 资金面因子
        if 'R007' in df.columns and 'DR007' in df.columns:
            df['funding_spread'] = df['R007'] - df['DR007']
            df['funding_momentum'] = df['funding_spread'].diff(1)

        # 机构行为因子
        institutional_cols = [col for col in df.columns if '净买入' in col]
        if len(institutional_cols) >= 2:
            df['institutional_total'] = df[institutional_cols].sum(axis=1)
            df['institutional_momentum'] = df['institutional_total'].diff(1)

        # 交互因子
        if '水泥价格指数' in df.columns and '偏离度（偏离均线）' in df.columns:
            df['fundamental_technical_interaction'] = df['水泥价格指数'] * df['偏离度（偏离均线）']

        # 删除包含NaN的行
        df = df.dropna()

        self.daily_data = df
        print(f"日频数据样本数量: {len(df)}")

    def prepare_weekly_data(self):
        """准备周频数据和因子"""
        print(f"\n{'='*60}")
        print("📊 准备周频数据和因子")
        print(f"{'='*60}")

        # 设置日期为索引
        df_daily = self.data.set_index('日期')

        # 添加周标识
        df_daily['week'] = df_daily.index.to_series().dt.to_period('W')

        # 定义聚合方法
        agg_methods = {}
        price_factors = ['30y', '10y', '30-10y', 'R007', 'DR007', '偏离度（偏离均线）']
        volume_factors = ['30Y成交量', '成交量:DR007', '成交量:R007']
        institutional_factors = [col for col in df_daily.columns if '净买入' in col]

        for col in df_daily.columns:
            if col == 'week':
                continue
            elif col in price_factors:
                agg_methods[col] = 'last'
            elif col in volume_factors + institutional_factors:
                agg_methods[col] = 'sum'
            else:
                agg_methods[col] = 'last'

        # 按周聚合
        df_weekly = df_daily.groupby('week').agg(agg_methods)
        df_weekly.reset_index(inplace=True)
        df_weekly['日期'] = df_weekly['week'].dt.end_time
        df_weekly = df_weekly.drop('week', axis=1)

        # 创建周频目标变量
        df_weekly['30y_next'] = df_weekly['30y'].shift(-1)
        df_weekly['30y_change'] = df_weekly['30y_next'] - df_weekly['30y']
        df_weekly['30y_direction'] = (df_weekly['30y_change'] > 0).astype(int)

        # 创建周频工程因子
        df_weekly['30y_momentum_2w'] = df_weekly['30y'].pct_change(2)
        df_weekly['30y_momentum_4w'] = df_weekly['30y'].pct_change(4)
        df_weekly['30y_volatility_4w'] = df_weekly['30y'].rolling(4).std()
        df_weekly['30y_volatility_8w'] = df_weekly['30y'].rolling(8).std()

        if '偏离度（偏离均线）' in df_weekly.columns:
            df_weekly['deviation_momentum_w'] = df_weekly['偏离度（偏离均线）'].diff(1)
            df_weekly['deviation_abs_w'] = abs(df_weekly['偏离度（偏离均线）'])

        # 资金面因子
        if 'R007' in df_weekly.columns and 'DR007' in df_weekly.columns:
            df_weekly['funding_spread_w'] = df_weekly['R007'] - df_weekly['DR007']
            df_weekly['funding_momentum_w'] = df_weekly['funding_spread_w'].diff(1)

        # 机构行为因子
        if len(institutional_factors) >= 2:
            df_weekly['institutional_total_w'] = df_weekly[institutional_factors].sum(axis=1)
            df_weekly['institutional_momentum_w'] = df_weekly['institutional_total_w'].diff(1)

        # 删除包含NaN的行
        df_weekly = df_weekly.dropna()

        self.weekly_data = df_weekly
        print(f"周频数据样本数量: {len(df_weekly)}")

    def generate_trading_signals(self):
        """生成交易信号"""
        print(f"\n{'='*60}")
        print("📈 生成交易信号")
        print(f"{'='*60}")

        # 日频信号
        daily_signals = self._generate_daily_signals()

        # 周频信号
        weekly_signals = self._generate_weekly_signals()

        self.signals = {
            'daily': daily_signals,
            'weekly': weekly_signals
        }

        return self.signals

    def _generate_daily_signals(self):
        """生成日频交易信号"""
        df = self.daily_data.copy()

        # 策略1: 技术面主导策略
        tech_score = 0
        if '偏离度（偏离均线）' in df.columns:
            deviation_median = df['偏离度（偏离均线）'].median()
            tech_score += (df['偏离度（偏离均线）'] > deviation_median).astype(int) * 3

        if 'deviation_momentum' in df.columns:
            momentum_median = df['deviation_momentum'].median()
            tech_score += (df['deviation_momentum'] > momentum_median).astype(int) * 2

        # 策略2: 资金面主导策略
        funding_score = 0
        if 'funding_spread' in df.columns:
            funding_median = df['funding_spread'].median()
            funding_score += (df['funding_spread'] < funding_median).astype(int) * 3  # 资金面紧张时看涨债券

        if 'R007' in df.columns:
            r007_median = df['R007'].median()
            funding_score += (df['R007'] < r007_median).astype(int) * 2

        # 策略3: 机构行为策略
        institutional_score = 0
        if 'institutional_total' in df.columns:
            inst_median = df['institutional_total'].median()
            institutional_score += (df['institutional_total'] > inst_median).astype(int) * 3

        if 'institutional_momentum' in df.columns:
            inst_mom_median = df['institutional_momentum'].median()
            institutional_score += (df['institutional_momentum'] > inst_mom_median).astype(int) * 2

        # 策略4: 综合策略
        total_score = tech_score + funding_score + institutional_score
        max_score = 10  # 3+2+3+2

        # 生成信号
        signals = {
            'technical_signal': (tech_score > 2.5).astype(int),
            'funding_signal': (funding_score > 2.5).astype(int),
            'institutional_signal': (institutional_score > 2.5).astype(int),
            'comprehensive_signal': (total_score > max_score/2).astype(int),
            'date': df['日期'],
            '30y': df['30y'],
            '30y_change': df['30y_change']
        }

        return pd.DataFrame(signals)

    def _generate_weekly_signals(self):
        """生成周频交易信号"""
        df = self.weekly_data.copy()

        # 策略1: 周度技术面策略
        tech_score = 0
        if '偏离度（偏离均线）' in df.columns:
            deviation_median = df['偏离度（偏离均线）'].median()
            tech_score += (df['偏离度（偏离均线）'] > deviation_median).astype(int) * 4

        if 'deviation_momentum_w' in df.columns:
            momentum_median = df['deviation_momentum_w'].median()
            tech_score += (df['deviation_momentum_w'] > momentum_median).astype(int) * 3

        # 策略2: 周度资金面策略
        funding_score = 0
        if 'funding_spread_w' in df.columns:
            funding_median = df['funding_spread_w'].median()
            funding_score += (df['funding_spread_w'] < funding_median).astype(int) * 4

        if 'R007' in df.columns:
            r007_median = df['R007'].median()
            funding_score += (df['R007'] < r007_median).astype(int) * 3

        # 策略3: 周度机构行为策略
        institutional_score = 0
        if 'institutional_total_w' in df.columns:
            inst_median = df['institutional_total_w'].median()
            institutional_score += (df['institutional_total_w'] > inst_median).astype(int) * 4

        if 'institutional_momentum_w' in df.columns:
            inst_mom_median = df['institutional_momentum_w'].median()
            institutional_score += (df['institutional_momentum_w'] > inst_mom_median).astype(int) * 3

        # 策略4: 周度综合策略
        total_score = tech_score + funding_score + institutional_score
        max_score = 14  # 4+3+4+3

        # 生成信号
        signals = {
            'technical_signal': (tech_score > 3.5).astype(int),
            'funding_signal': (funding_score > 3.5).astype(int),
            'institutional_signal': (institutional_score > 3.5).astype(int),
            'comprehensive_signal': (total_score > max_score/2).astype(int),
            'date': df['日期'],
            '30y': df['30y'],
            '30y_change': df['30y_change']
        }

        return pd.DataFrame(signals)

# 初始化基金经理分析
fund_manager = QuantitativeFundManager(df_processed)

# 准备数据
fund_manager.prepare_daily_data()
fund_manager.prepare_weekly_data()

# 生成交易信号
signals = fund_manager.generate_trading_signals()

print(f"\n日频信号数据形状: {signals['daily'].shape}")
print(f"周频信号数据形状: {signals['weekly'].shape}")

# 显示信号统计
daily_signals = signals['daily']
weekly_signals = signals['weekly']

print(f"\n日频信号统计:")
for signal_type in ['technical_signal', 'funding_signal', 'institutional_signal', 'comprehensive_signal']:
    if signal_type in daily_signals.columns:
        signal_rate = daily_signals[signal_type].mean()
        print(f"  {signal_type}: {signal_rate:.3f} ({signal_rate*100:.1f}%)")

print(f"\n周频信号统计:")
for signal_type in ['technical_signal', 'funding_signal', 'institutional_signal', 'comprehensive_signal']:
    if signal_type in weekly_signals.columns:
        signal_rate = weekly_signals[signal_type].mean()
        print(f"  {signal_type}: {signal_rate:.3f} ({signal_rate*100:.1f}%)")

def calculate_portfolio_returns(signals_df, frequency='daily'):
    """计算投资组合收益率"""
    print(f"\n{'='*60}")
    print(f"💰 计算{frequency}投资组合收益率")
    print(f"{'='*60}")

    df = signals_df.copy()

    # 基准收益率：买入持有30年国债
    df['benchmark_return'] = df['30y_change'] * -1  # 收益率下降时债券价格上升

    # 策略收益率计算
    strategies = ['technical_signal', 'funding_signal', 'institutional_signal', 'comprehensive_signal']

    for strategy in strategies:
        if strategy in df.columns:
            # 信号为1时做多债券（预期收益率下降），信号为0时做空债券（预期收益率上升）
            df[f'{strategy}_return'] = np.where(
                df[strategy] == 1,
                df['30y_change'] * -1,  # 做多债券
                df['30y_change'] * 1    # 做空债券
            )

    # 计算累计收益率
    df['benchmark_cumret'] = (1 + df['benchmark_return']).cumprod() - 1

    for strategy in strategies:
        if f'{strategy}_return' in df.columns:
            df[f'{strategy}_cumret'] = (1 + df[f'{strategy}_return']).cumprod() - 1

    return df

def calculate_performance_metrics(returns_df, frequency='daily'):
    """计算绩效指标"""
    print(f"\n{'='*60}")
    print(f"📊 计算{frequency}绩效指标")
    print(f"{'='*60}")

    strategies = ['technical_signal', 'funding_signal', 'institutional_signal', 'comprehensive_signal']

    metrics = {}

    # 基准指标
    benchmark_ret = returns_df['benchmark_return']
    metrics['benchmark'] = {
        'total_return': returns_df['benchmark_cumret'].iloc[-1],
        'annualized_return': benchmark_ret.mean() * (252 if frequency == 'daily' else 52),
        'volatility': benchmark_ret.std() * np.sqrt(252 if frequency == 'daily' else 52),
        'sharpe_ratio': (benchmark_ret.mean() / benchmark_ret.std()) * np.sqrt(252 if frequency == 'daily' else 52),
        'max_drawdown': calculate_max_drawdown(returns_df['benchmark_cumret']),
        'win_rate': (benchmark_ret > 0).mean()
    }

    # 策略指标
    for strategy in strategies:
        if f'{strategy}_return' in returns_df.columns:
            strategy_ret = returns_df[f'{strategy}_return']
            metrics[strategy] = {
                'total_return': returns_df[f'{strategy}_cumret'].iloc[-1],
                'annualized_return': strategy_ret.mean() * (252 if frequency == 'daily' else 52),
                'volatility': strategy_ret.std() * np.sqrt(252 if frequency == 'daily' else 52),
                'sharpe_ratio': (strategy_ret.mean() / strategy_ret.std()) * np.sqrt(252 if frequency == 'daily' else 52),
                'max_drawdown': calculate_max_drawdown(returns_df[f'{strategy}_cumret']),
                'win_rate': (strategy_ret > 0).mean(),
                'excess_return': returns_df[f'{strategy}_cumret'].iloc[-1] - returns_df['benchmark_cumret'].iloc[-1],
                'information_ratio': (strategy_ret - benchmark_ret).mean() / (strategy_ret - benchmark_ret).std() * np.sqrt(252 if frequency == 'daily' else 52)
            }

    return metrics

def calculate_max_drawdown(cumret_series):
    """计算最大回撤"""
    peak = cumret_series.expanding().max()
    drawdown = (cumret_series - peak) / (1 + peak)
    return drawdown.min()

def create_performance_charts(daily_returns, weekly_returns, output_dir):
    """创建绩效图表"""
    print(f"\n{'='*60}")
    print("📈 创建绩效图表")
    print(f"{'='*60}")

    # 1. 日频累计收益率图
    plt.figure(figsize=(16, 10))

    plt.plot(daily_returns['date'], daily_returns['benchmark_cumret'] * 100,
             label='基准收益率(买入持有30Y)', linewidth=2, color='black', linestyle='--')

    strategies = ['technical_signal', 'funding_signal', 'institutional_signal', 'comprehensive_signal']
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
    strategy_names = ['技术面策略', '资金面策略', '机构行为策略', '综合策略']

    for i, strategy in enumerate(strategies):
        if f'{strategy}_cumret' in daily_returns.columns:
            plt.plot(daily_returns['date'], daily_returns[f'{strategy}_cumret'] * 100,
                    label=strategy_names[i], linewidth=2, color=colors[i])

    plt.xlabel('日期', fontsize=14, fontweight='bold')
    plt.ylabel('累计收益率 (%)', fontsize=14, fontweight='bold')
    plt.title('日频策略累计收益率对比', fontsize=16, fontweight='bold', pad=20)
    plt.legend(loc='upper left', fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/日频策略累计收益率对比.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✓ 日频策略累计收益率对比.png")

    # 2. 周频累计收益率图
    plt.figure(figsize=(16, 10))

    plt.plot(weekly_returns['date'], weekly_returns['benchmark_cumret'] * 100,
             label='基准收益率(买入持有30Y)', linewidth=2, color='black', linestyle='--')

    for i, strategy in enumerate(strategies):
        if f'{strategy}_cumret' in weekly_returns.columns:
            plt.plot(weekly_returns['date'], weekly_returns[f'{strategy}_cumret'] * 100,
                    label=strategy_names[i], linewidth=2, color=colors[i])

    plt.xlabel('日期', fontsize=14, fontweight='bold')
    plt.ylabel('累计收益率 (%)', fontsize=14, fontweight='bold')
    plt.title('周频策略累计收益率对比', fontsize=16, fontweight='bold', pad=20)
    plt.legend(loc='upper left', fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/周频策略累计收益率对比.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✓ 周频策略累计收益率对比.png")

    # 3. 策略对比雷达图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8), subplot_kw=dict(projection='polar'))

    # 日频雷达图
    daily_metrics = calculate_performance_metrics(daily_returns, 'daily')
    categories = ['年化收益率', '夏普比率', '胜率', '信息比率']

    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    angles += angles[:1]  # 闭合图形

    for i, strategy in enumerate(strategies):
        if strategy in daily_metrics:
            values = [
                daily_metrics[strategy]['annualized_return'] * 100,
                daily_metrics[strategy]['sharpe_ratio'],
                daily_metrics[strategy]['win_rate'] * 100,
                daily_metrics[strategy]['information_ratio']
            ]
            values += values[:1]  # 闭合图形

            ax1.plot(angles, values, 'o-', linewidth=2, label=strategy_names[i], color=colors[i])
            ax1.fill(angles, values, alpha=0.25, color=colors[i])

    ax1.set_xticks(angles[:-1])
    ax1.set_xticklabels(categories, fontsize=12)
    ax1.set_title('日频策略绩效雷达图', fontsize=14, fontweight='bold', pad=20)
    ax1.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    ax1.grid(True)

    # 周频雷达图
    weekly_metrics = calculate_performance_metrics(weekly_returns, 'weekly')

    for i, strategy in enumerate(strategies):
        if strategy in weekly_metrics:
            values = [
                weekly_metrics[strategy]['annualized_return'] * 100,
                weekly_metrics[strategy]['sharpe_ratio'],
                weekly_metrics[strategy]['win_rate'] * 100,
                weekly_metrics[strategy]['information_ratio']
            ]
            values += values[:1]  # 闭合图形

            ax2.plot(angles, values, 'o-', linewidth=2, label=strategy_names[i], color=colors[i])
            ax2.fill(angles, values, alpha=0.25, color=colors[i])

    ax2.set_xticks(angles[:-1])
    ax2.set_xticklabels(categories, fontsize=12)
    ax2.set_title('周频策略绩效雷达图', fontsize=14, fontweight='bold', pad=20)
    ax2.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    ax2.grid(True)

    plt.tight_layout()
    plt.savefig(f'{output_dir}/策略绩效雷达图对比.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✓ 策略绩效雷达图对比.png")

def save_performance_excel(daily_metrics, weekly_metrics, daily_returns, weekly_returns, output_dir):
    """保存绩效Excel文件"""
    print(f"\n{'='*60}")
    print("📊 保存绩效Excel文件")
    print(f"{'='*60}")

    with pd.ExcelWriter(f'{output_dir}/策略绩效分析报告.xlsx') as writer:

        # 日频绩效指标
        daily_metrics_df = pd.DataFrame(daily_metrics).T
        daily_metrics_df.index.name = '策略名称'
        daily_metrics_df.to_excel(writer, sheet_name='日频绩效指标')

        # 周频绩效指标
        weekly_metrics_df = pd.DataFrame(weekly_metrics).T
        weekly_metrics_df.index.name = '策略名称'
        weekly_metrics_df.to_excel(writer, sheet_name='周频绩效指标')

        # 日频累计收益率
        daily_cumret_df = daily_returns[['date', 'benchmark_cumret', 'technical_signal_cumret',
                                        'funding_signal_cumret', 'institutional_signal_cumret',
                                        'comprehensive_signal_cumret']].copy()
        daily_cumret_df.columns = ['日期', '基准累计收益率', '技术面策略累计收益率',
                                  '资金面策略累计收益率', '机构行为策略累计收益率', '综合策略累计收益率']
        daily_cumret_df.to_excel(writer, sheet_name='日频累计收益率', index=False)

        # 周频累计收益率
        weekly_cumret_df = weekly_returns[['date', 'benchmark_cumret', 'technical_signal_cumret',
                                          'funding_signal_cumret', 'institutional_signal_cumret',
                                          'comprehensive_signal_cumret']].copy()
        weekly_cumret_df.columns = ['日期', '基准累计收益率', '技术面策略累计收益率',
                                   '资金面策略累计收益率', '机构行为策略累计收益率', '综合策略累计收益率']
        weekly_cumret_df.to_excel(writer, sheet_name='周频累计收益率', index=False)

    print("✓ 策略绩效分析报告.xlsx")

# 计算收益率
daily_returns = calculate_portfolio_returns(daily_signals, 'daily')
weekly_returns = calculate_portfolio_returns(weekly_signals, 'weekly')

# 计算绩效指标
daily_metrics = calculate_performance_metrics(daily_returns, 'daily')
weekly_metrics = calculate_performance_metrics(weekly_returns, 'weekly')

# 创建图表
create_performance_charts(daily_returns, weekly_returns, output_dir)

# 保存Excel文件
save_performance_excel(daily_metrics, weekly_metrics, daily_returns, weekly_returns, output_dir)

def generate_fund_manager_report(daily_metrics, weekly_metrics, output_dir):
    """生成基金经理分析报告"""
    print(f"\n{'='*60}")
    print("📋 生成基金经理分析报告")
    print(f"{'='*60}")

    report = f"""
🏦 固收量化基金经理深度分析报告
=====================================
周度与日度因子模型调仓策略分析

📊 模型合理性分析

1. 日度模型分析:
   - 样本数量: {len(daily_returns)}个交易日
   - 基准策略: 买入持有30年国债
   - 基准年化收益率: {daily_metrics['benchmark']['annualized_return']*100:.2f}%
   - 基准夏普比率: {daily_metrics['benchmark']['sharpe_ratio']:.3f}
   - 基准最大回撤: {daily_metrics['benchmark']['max_drawdown']*100:.2f}%

2. 周度模型分析:
   - 样本数量: {len(weekly_returns)}个交易周
   - 基准策略: 买入持有30年国债
   - 基准年化收益率: {weekly_metrics['benchmark']['annualized_return']*100:.2f}%
   - 基准夏普比率: {weekly_metrics['benchmark']['sharpe_ratio']:.3f}
   - 基准最大回撤: {weekly_metrics['benchmark']['max_drawdown']*100:.2f}%

🎯 调仓信号分析

日频调仓信号:
1. 技术面策略信号:
   - 信号释义: 基于偏离度和技术动量的多空判断
   - 做多信号: 偏离度 > 中位数 且 偏离度动量 > 中位数
   - 做空信号: 偏离度 ≤ 中位数 或 偏离度动量 ≤ 中位数
   - 年化收益率: {daily_metrics['technical_signal']['annualized_return']*100:.2f}%
   - 信息比率: {daily_metrics['technical_signal']['information_ratio']:.3f}

2. 资金面策略信号:
   - 信号释义: 基于资金面紧张程度的反向操作
   - 做多信号: 资金面利差 < 中位数 且 R007 < 中位数 (资金面紧张时买入债券)
   - 做空信号: 资金面利差 ≥ 中位数 或 R007 ≥ 中位数 (资金面宽松时卖出债券)
   - 年化收益率: {daily_metrics['funding_signal']['annualized_return']*100:.2f}%
   - 信息比率: {daily_metrics['funding_signal']['information_ratio']:.3f}

3. 机构行为策略信号:
   - 信号释义: 跟随机构净买入行为
   - 做多信号: 机构净买入总和 > 中位数 且 机构净买入动量 > 中位数
   - 做空信号: 机构净买入总和 ≤ 中位数 或 机构净买入动量 ≤ 中位数
   - 年化收益率: {daily_metrics['institutional_signal']['annualized_return']*100:.2f}%
   - 信息比率: {daily_metrics['institutional_signal']['information_ratio']:.3f}

4. 综合策略信号:
   - 信号释义: 技术面、资金面、机构行为的综合评分
   - 做多信号: 综合得分 > 总分的50%
   - 做空信号: 综合得分 ≤ 总分的50%
   - 年化收益率: {daily_metrics['comprehensive_signal']['annualized_return']*100:.2f}%
   - 信息比率: {daily_metrics['comprehensive_signal']['information_ratio']:.3f}

周频调仓信号:
1. 周度技术面策略:
   - 权重更高的偏离度和动量因子
   - 年化收益率: {weekly_metrics['technical_signal']['annualized_return']*100:.2f}%
   - 信息比率: {weekly_metrics['technical_signal']['information_ratio']:.3f}

2. 周度资金面策略:
   - 基于周度累积的资金面数据
   - 年化收益率: {weekly_metrics['funding_signal']['annualized_return']*100:.2f}%
   - 信息比率: {weekly_metrics['funding_signal']['information_ratio']:.3f}

3. 周度机构行为策略:
   - 基于周度累积的机构净买入数据
   - 年化收益率: {weekly_metrics['institutional_signal']['annualized_return']*100:.2f}%
   - 信息比率: {weekly_metrics['institutional_signal']['information_ratio']:.3f}

4. 周度综合策略:
   - 年化收益率: {weekly_metrics['comprehensive_signal']['annualized_return']*100:.2f}%
   - 信息比率: {weekly_metrics['comprehensive_signal']['information_ratio']:.3f}

📈 绩效对比分析

最佳日频策略: """

    # 找出最佳日频策略
    best_daily_strategy = None
    best_daily_return = -999
    for strategy in ['technical_signal', 'funding_signal', 'institutional_signal', 'comprehensive_signal']:
        if strategy in daily_metrics and daily_metrics[strategy]['annualized_return'] > best_daily_return:
            best_daily_return = daily_metrics[strategy]['annualized_return']
            best_daily_strategy = strategy

    strategy_names_map = {
        'technical_signal': '技术面策略',
        'funding_signal': '资金面策略',
        'institutional_signal': '机构行为策略',
        'comprehensive_signal': '综合策略'
    }

    if best_daily_strategy:
        report += f"{strategy_names_map[best_daily_strategy]}\n"
        report += f"- 年化收益率: {daily_metrics[best_daily_strategy]['annualized_return']*100:.2f}%\n"
        report += f"- 夏普比率: {daily_metrics[best_daily_strategy]['sharpe_ratio']:.3f}\n"
        report += f"- 最大回撤: {daily_metrics[best_daily_strategy]['max_drawdown']*100:.2f}%\n"
        report += f"- 胜率: {daily_metrics[best_daily_strategy]['win_rate']*100:.1f}%\n"
        report += f"- 超额收益: {daily_metrics[best_daily_strategy]['excess_return']*100:.2f}%\n"

    # 找出最佳周频策略
    best_weekly_strategy = None
    best_weekly_return = -999
    for strategy in ['technical_signal', 'funding_signal', 'institutional_signal', 'comprehensive_signal']:
        if strategy in weekly_metrics and weekly_metrics[strategy]['annualized_return'] > best_weekly_return:
            best_weekly_return = weekly_metrics[strategy]['annualized_return']
            best_weekly_strategy = strategy

    report += f"\n最佳周频策略: "
    if best_weekly_strategy:
        report += f"{strategy_names_map[best_weekly_strategy]}\n"
        report += f"- 年化收益率: {weekly_metrics[best_weekly_strategy]['annualized_return']*100:.2f}%\n"
        report += f"- 夏普比率: {weekly_metrics[best_weekly_strategy]['sharpe_ratio']:.3f}\n"
        report += f"- 最大回撤: {weekly_metrics[best_weekly_strategy]['max_drawdown']*100:.2f}%\n"
        report += f"- 胜率: {weekly_metrics[best_weekly_strategy]['win_rate']*100:.1f}%\n"
        report += f"- 超额收益: {weekly_metrics[best_weekly_strategy]['excess_return']*100:.2f}%\n"

    report += f"""
💡 基金经理建议

1. 调仓频率建议:
   - 日频调仓: 适合捕捉短期市场波动，但交易成本较高
   - 周频调仓: 平衡了收益和成本，推荐作为主要调仓频率

2. 策略选择建议:
   - 技术面策略: 在趋势明确时表现较好
   - 资金面策略: 在货币政策变化时敏感度高
   - 机构行为策略: 跟随聪明钱，稳定性较好
   - 综合策略: 分散风险，适合作为核心配置

3. 风险控制建议:
   - 设置止损线: 单日/周亏损超过1%时减仓
   - 仓位管理: 单一策略仓位不超过50%
   - 动态调整: 根据市场波动率调整仓位大小

4. 实施建议:
   - 建议采用{strategy_names_map[best_weekly_strategy]}作为主策略
   - 配合{strategy_names_map[best_daily_strategy]}进行短期调整
   - 定期评估策略有效性，必要时进行参数优化

⚠️ 风险提示

1. 模型风险: 历史表现不代表未来收益
2. 市场风险: 极端市场条件下策略可能失效
3. 流动性风险: 大额交易可能影响市场价格
4. 操作风险: 需要严格执行交易纪律

该分析为30年国债投资提供了科学的量化决策框架！
"""

    # 保存报告
    with open(f'{output_dir}/基金经理分析报告.txt', 'w', encoding='utf-8') as f:
        f.write(report)

    print(report)
    print(f"\n✅ 基金经理分析报告已保存: {output_dir}/基金经理分析报告.txt")

# 生成基金经理分析报告
generate_fund_manager_report(daily_metrics, weekly_metrics, output_dir)

print(f"\n" + "="*100)
print(f"🏦 固收量化基金经理深度分析完成！")
print(f"="*100)

# 输出关键结果
print(f"📊 分析结果概览:")
print(f"  日频数据样本: {len(daily_returns)}个交易日")
print(f"  周频数据样本: {len(weekly_returns)}个交易周")

# 找出最佳策略
best_daily_strategy = None
best_daily_return = -999
for strategy in ['technical_signal', 'funding_signal', 'institutional_signal', 'comprehensive_signal']:
    if strategy in daily_metrics and daily_metrics[strategy]['annualized_return'] > best_daily_return:
        best_daily_return = daily_metrics[strategy]['annualized_return']
        best_daily_strategy = strategy

best_weekly_strategy = None
best_weekly_return = -999
for strategy in ['technical_signal', 'funding_signal', 'institutional_signal', 'comprehensive_signal']:
    if strategy in weekly_metrics and weekly_metrics[strategy]['annualized_return'] > best_weekly_return:
        best_weekly_return = weekly_metrics[strategy]['annualized_return']
        best_weekly_strategy = strategy

strategy_names_map = {
    'technical_signal': '技术面策略',
    'funding_signal': '资金面策略',
    'institutional_signal': '机构行为策略',
    'comprehensive_signal': '综合策略'
}

if best_daily_strategy:
    print(f"🏆 最佳日频策略: {strategy_names_map[best_daily_strategy]}")
    print(f"📈 日频年化收益率: {daily_metrics[best_daily_strategy]['annualized_return']*100:.2f}%")
    print(f"📊 日频夏普比率: {daily_metrics[best_daily_strategy]['sharpe_ratio']:.3f}")

if best_weekly_strategy:
    print(f"🏆 最佳周频策略: {strategy_names_map[best_weekly_strategy]}")
    print(f"📈 周频年化收益率: {weekly_metrics[best_weekly_strategy]['annualized_return']*100:.2f}%")
    print(f"📊 周频夏普比率: {weekly_metrics[best_weekly_strategy]['sharpe_ratio']:.3f}")

print(f"📁 输出目录: {output_dir}")
print(f"="*100)
print(f"🎯 生成的文件:")
print(f"  图表 (3张):")
print(f"    1. 日频策略累计收益率对比.png")
print(f"    2. 周频策略累计收益率对比.png")
print(f"    3. 策略绩效雷达图对比.png")
print(f"  Excel (1个):")
print(f"    1. 策略绩效分析报告.xlsx")
print(f"  报告 (1个):")
print(f"    1. 基金经理分析报告.txt")
print(f"="*100)
