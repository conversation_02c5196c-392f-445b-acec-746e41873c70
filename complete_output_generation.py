import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
import os
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score
warnings.filterwarnings('ignore')

# 修复中文字体显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.figsize'] = (14, 10)
plt.rcParams['savefig.dpi'] = 300

# 创建输出文件夹
output_dir = '/Users/<USER>/Desktop/量化择时0527'
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

print("=" * 100)
print("🚀 生成完整的图表和Excel文件到量化择时0527文件夹")
print("=" * 100)

# 加载数据
file_path = '/Users/<USER>/Desktop/择时胜率0527.xlsx'
df = pd.read_excel(file_path)

# 数据预处理
df_processed = df.fillna(method='ffill').fillna(method='bfill').copy()
if 'Unnamed: 0' in df_processed.columns:
    df_processed = df_processed.rename(columns={'Unnamed: 0': '日期'})

# 构建目标变量
df_processed['30-10y_next'] = df_processed['30-10y'].shift(-1)
df_processed['30-10y_change'] = df_processed['30-10y_next'] - df_processed['30-10y']
df_processed['spread_direction'] = (df_processed['30-10y_change'] > 0).astype(int)

df_processed['30y_next'] = df_processed['30y'].shift(-1)
df_processed['30y_change'] = df_processed['30y_next'] - df_processed['30y']
df_processed['yield_direction'] = (df_processed['30y_change'] > 0).astype(int)

df_processed = df_processed[:-1].copy()

print(f"数据样本数量: {len(df_processed)}")
print(f"利差走阔概率: {df_processed['spread_direction'].mean():.3f}")
print(f"30y收益率上升概率: {df_processed['yield_direction'].mean():.3f}")

# 定义因子分类
factor_categories = {
    '基本面因子': ['水泥价格指数', '建材综合指数', '南华金属指数', '南华能化指数', '南华工业品指数'],
    '技术面因子': ['偏离度（偏离均线）', '偏离度（偏离MA+std）'],
    '资金面因子': ['R007', 'DR007', '成交量:DR007', '成交量:R007'],
    '权益市场因子': ['1/沪深300PE-10y'],
    '成交因子': ['30Y成交量'],
    '机构行为因子': ['农商行30Y净买入', '券商30Y净买入', '险资30Y净买入', '公募30Y净买入']
}

all_original_factors = []
for factors in factor_categories.values():
    all_original_factors.extend(factors)

available_original_factors = [f for f in all_original_factors if f in df_processed.columns]

def create_engineered_factors(df):
    """创造工程因子"""
    print("创建工程因子...")

    # 1. 动量系列因子
    df['利差动量_3日'] = df['30-10y'].pct_change(3)
    df['利差动量_5日'] = df['30-10y'].pct_change(5)
    df['利差动量_10日'] = df['30-10y'].pct_change(10)
    df['30y动量_3日'] = df['30y'].pct_change(3)
    df['30y动量_5日'] = df['30y'].pct_change(5)

    # 2. 波动率系列因子
    df['利差波动率_5日'] = df['30-10y'].rolling(5).std()
    df['利差波动率_10日'] = df['30-10y'].rolling(10).std()
    df['30y波动率_5日'] = df['30y'].rolling(5).std()

    # 3. 技术面增强因子
    if '偏离度（偏离均线）' in df.columns:
        df['偏离度动量'] = df['偏离度（偏离均线）'].diff(1)
        df['偏离度绝对值'] = abs(df['偏离度（偏离均线）'])
        df['偏离度平方'] = df['偏离度（偏离均线）'] ** 2
        df['偏离度标准化'] = df['偏离度（偏离均线）'] / df['利差波动率_10日']

    # 4. 复合基本面因子
    commodity_factors = ['南华金属指数', '南华能化指数', '南华工业品指数']
    available_commodity = [f for f in commodity_factors if f in df.columns]
    if len(available_commodity) >= 2:
        df['商品综合指数'] = df[available_commodity].mean(axis=1)
        df['商品动量_5日'] = df['商品综合指数'].pct_change(5)
        df['商品波动率_10日'] = df['商品综合指数'].rolling(10).std()

    # 5. 资金面复合因子
    if 'R007' in df.columns and 'DR007' in df.columns:
        df['资金面利差'] = df['R007'] - df['DR007']
        df['资金面利差动量'] = df['资金面利差'].diff(1)
        df['资金面紧张度'] = (df['R007'] - df['R007'].rolling(20).mean()) / df['R007'].rolling(20).std()

    # 6. 成交量增强因子
    volume_factors = ['成交量:DR007', '成交量:R007', '30Y成交量']
    available_volume = [f for f in volume_factors if f in df.columns]
    if len(available_volume) >= 2:
        df['成交量综合'] = df[available_volume].mean(axis=1)
        df['成交量动量'] = df['成交量综合'].pct_change(5)

    # 7. 机构行为因子
    institutional_cols = [col for col in df.columns if '净买入' in col]
    if len(institutional_cols) >= 2:
        df['机构净买入总和'] = df[institutional_cols].sum(axis=1)
        df['机构净买入动量'] = df['机构净买入总和'].diff(1)
        df['机构行为一致性'] = (df[institutional_cols] > 0).sum(axis=1)

    # 8. 交互因子
    if '水泥价格指数' in df.columns and '偏离度（偏离均线）' in df.columns:
        df['基本面技术面交互'] = df['水泥价格指数'] * df['偏离度（偏离均线）']

    if 'R007' in df.columns and '30Y成交量' in df.columns:
        df['资金面成交交互'] = df['R007'] * df['30Y成交量']

    # 9. 相对价值因子
    if '10y' in df.columns and '30y' in df.columns:
        df['收益率曲线斜率'] = df['30y'] - df['10y']
        df['收益率曲线斜率动量'] = df['收益率曲线斜率'].diff(1)

    # 10. 市场状态因子
    df['高波动状态'] = (df['利差波动率_10日'] > df['利差波动率_10日'].rolling(20).quantile(0.7)).astype(int)
    df['极端偏离状态'] = (abs(df['偏离度（偏离均线）']) > df['偏离度绝对值'].rolling(20).quantile(0.8)).astype(int)

    # 删除包含NaN的行
    df = df.dropna()

    # 统计新创建的因子
    exclude_cols = available_original_factors + ['30-10y', '10y', '30y', '日期', '30-10y_next', '30-10y_change',
                                                'spread_direction', '30y_next', '30y_change', 'yield_direction',
                                                '30-10y（250MA）', '市盈率:沪深300指数', 'std']

    new_factors = [col for col in df.columns if col not in exclude_cols]

    print(f"✅ 成功创建 {len(new_factors)} 个工程因子")

    return df, new_factors

df_engineered, new_factors = create_engineered_factors(df_processed)

def calculate_factor_performance(df, factor, target_spread='spread_direction', target_yield='yield_direction'):
    """计算因子表现"""
    if factor not in df.columns:
        return None

    factor_data = df[factor]
    spread_data = df[target_spread]
    yield_data = df[target_yield]

    # 确保数据对齐
    valid_idx = ~(factor_data.isna() | spread_data.isna() | yield_data.isna())
    factor_values = factor_data[valid_idx]
    spread_values = spread_data[valid_idx]
    yield_values = yield_data[valid_idx]

    if len(factor_values) < 10:
        return None

    # 计算相关性
    spread_correlation = factor_values.corr(spread_values)
    yield_correlation = factor_values.corr(yield_values)

    # 计算统计显著性
    try:
        _, spread_p_value = stats.pearsonr(factor_values, spread_values)
        _, yield_p_value = stats.pearsonr(factor_values, yield_values)
    except:
        spread_p_value = 1.0
        yield_p_value = 1.0

    # 分位数分析
    q50 = factor_values.median()

    # 计算单因子胜率
    # 利差预测：基于相关性方向
    if spread_correlation > 0:
        spread_prediction = (factor_values > q50).astype(int)
        spread_signal_rule = f"当{factor} > 中位数时，预测利差走阔"
    else:
        spread_prediction = (factor_values <= q50).astype(int)
        spread_signal_rule = f"当{factor} <= 中位数时，预测利差走阔"

    spread_win_rate = (spread_prediction == spread_values).mean()

    # 收益率预测：基于相关性方向
    if yield_correlation > 0:
        yield_prediction = (factor_values > q50).astype(int)
        yield_signal_rule = f"当{factor} > 中位数时，预测30y收益率上升"
    else:
        yield_prediction = (factor_values <= q50).astype(int)
        yield_signal_rule = f"当{factor} <= 中位数时，预测30y收益率上升"

    yield_win_rate = (yield_prediction == yield_values).mean()

    return {
        'factor': factor,
        'spread_correlation': spread_correlation,
        'yield_correlation': yield_correlation,
        'spread_p_value': spread_p_value,
        'yield_p_value': yield_p_value,
        'spread_significant': spread_p_value < 0.05,
        'yield_significant': yield_p_value < 0.05,
        'spread_win_rate': spread_win_rate,
        'yield_win_rate': yield_win_rate,
        'spread_signal_rule': spread_signal_rule,
        'yield_signal_rule': yield_signal_rule,
        'factor_median': q50
    }

def analyze_all_factors(df, original_factors, new_factors):
    """分析所有因子"""
    print("分析所有因子表现...")

    all_factors = original_factors + new_factors
    factor_results = []

    for factor in all_factors:
        if factor not in df.columns:
            continue

        result = calculate_factor_performance(df, factor)
        if result is not None:
            # 标记因子类型
            if factor in original_factors:
                result['factor_type'] = '原始因子'
            else:
                result['factor_type'] = '工程因子'

            factor_results.append(result)

    factor_df = pd.DataFrame(factor_results)

    # 按利差预测胜率排序
    factor_df_spread = factor_df.sort_values('spread_win_rate', ascending=False)

    # 按收益率预测胜率排序
    factor_df_yield = factor_df.sort_values('yield_win_rate', ascending=False)

    return factor_df, factor_df_spread, factor_df_yield

all_factor_performance, spread_ranking, yield_ranking = analyze_all_factors(df_engineered, available_original_factors, new_factors)

def build_strategies(df, factor_performance, target='spread_direction', strategy_type='利差'):
    """构建策略"""
    print(f"构建{strategy_type}预测策略...")

    # 选择TOP因子
    if target == 'spread_direction':
        top_factors = factor_performance.sort_values('spread_win_rate', ascending=False).head(15)['factor'].tolist()
        win_rate_col = 'spread_win_rate'
        correlation_col = 'spread_correlation'
    else:
        top_factors = factor_performance.sort_values('yield_win_rate', ascending=False).head(15)['factor'].tolist()
        win_rate_col = 'yield_win_rate'
        correlation_col = 'yield_correlation'

    strategies_results = []

    # 策略1: 分层权重策略
    tech_factors = [f for f in top_factors if any(x in f for x in ['偏离', '动量', '趋势', '波动', '技术'])][:4]
    volume_factors = [f for f in top_factors if any(x in f for x in ['成交量', '30Y成交量', '成交'])][:3]
    fundamental_factors = [f for f in top_factors if any(x in f for x in ['水泥', '建材', '南华', '商品', '基本面'])][:3]
    interaction_factors = [f for f in top_factors if '交互' in f][:2]
    institutional_factors = [f for f in top_factors if any(x in f for x in ['净买入', '机构', '农商行', '券商', '险资', '公募'])][:2]
    funding_factors = [f for f in top_factors if any(x in f for x in ['R007', 'DR007', '资金面'])][:3]

    # 计算分层得分
    total_score = 0
    max_score = 0

    for factor in interaction_factors:
        if factor in df.columns:
            factor_stats = factor_performance[factor_performance['factor'] == factor].iloc[0]
            factor_corr = factor_stats[correlation_col]
            factor_median = df[factor].median()

            if factor_corr > 0:
                signal = (df[factor] > factor_median).astype(int)
            else:
                signal = (df[factor] <= factor_median).astype(int)

            total_score += signal * 5  # 交互因子权重5
            max_score += 5

    for factor in tech_factors:
        if factor in df.columns:
            factor_stats = factor_performance[factor_performance['factor'] == factor].iloc[0]
            factor_corr = factor_stats[correlation_col]
            factor_median = df[factor].median()

            if factor_corr > 0:
                signal = (df[factor] > factor_median).astype(int)
            else:
                signal = (df[factor] <= factor_median).astype(int)

            total_score += signal * 4  # 技术面权重4
            max_score += 4

    for factor in institutional_factors:
        if factor in df.columns:
            factor_stats = factor_performance[factor_performance['factor'] == factor].iloc[0]
            factor_corr = factor_stats[correlation_col]
            factor_median = df[factor].median()

            if factor_corr > 0:
                signal = (df[factor] > factor_median).astype(int)
            else:
                signal = (df[factor] <= factor_median).astype(int)

            total_score += signal * 3  # 机构行为权重3
            max_score += 3

    for factor in volume_factors + funding_factors:
        if factor in df.columns:
            factor_stats = factor_performance[factor_performance['factor'] == factor].iloc[0]
            factor_corr = factor_stats[correlation_col]
            factor_median = df[factor].median()

            if factor_corr > 0:
                signal = (df[factor] > factor_median).astype(int)
            else:
                signal = (df[factor] <= factor_median).astype(int)

            total_score += signal * 2  # 成交量/资金面权重2
            max_score += 2

    for factor in fundamental_factors:
        if factor in df.columns:
            factor_stats = factor_performance[factor_performance['factor'] == factor].iloc[0]
            factor_corr = factor_stats[correlation_col]
            factor_median = df[factor].median()

            if factor_corr > 0:
                signal = (df[factor] > factor_median).astype(int)
            else:
                signal = (df[factor] <= factor_median).astype(int)

            total_score += signal * 1  # 基本面权重1
            max_score += 1

    layered_prediction = (total_score > max_score / 2).astype(int)
    layered_accuracy = (layered_prediction == df[target]).mean()

    strategies_results.append({
        'strategy_name': '分层权重策略',
        'accuracy': layered_accuracy,
        'description': '交互因子权重5，技术面权重4，机构行为权重3，成交量/资金面权重2，基本面权重1',
        'factors_used': f'交互({len(interaction_factors)}): {interaction_factors}; 技术({len(tech_factors)}): {tech_factors}; 机构({len(institutional_factors)}): {institutional_factors}; 成交量({len(volume_factors)}): {volume_factors}; 资金面({len(funding_factors)}): {funding_factors}; 基本面({len(fundamental_factors)}): {fundamental_factors}'
    })

    # 策略2: 胜率加权策略
    weighted_score = 0
    total_weight = 0

    for factor in top_factors[:12]:
        if factor not in df.columns:
            continue

        factor_stats = factor_performance[factor_performance['factor'] == factor].iloc[0]
        factor_corr = factor_stats[correlation_col]
        factor_median = df[factor].median()
        weight = factor_stats[win_rate_col]

        if factor_corr > 0:
            signal = (df[factor] > factor_median).astype(int)
        else:
            signal = (df[factor] <= factor_median).astype(int)

        weighted_score += signal * weight
        total_weight += weight

    weighted_prediction = (weighted_score > total_weight / 2).astype(int)
    weighted_accuracy = (weighted_prediction == df[target]).mean()

    strategies_results.append({
        'strategy_name': '胜率加权策略',
        'accuracy': weighted_accuracy,
        'description': '根据各因子历史胜率分配权重',
        'factors_used': ', '.join(top_factors[:12])
    })

    # 策略3: 动态阈值策略
    volatility = df['30-10y'].rolling(10).std()
    high_vol_periods = volatility > volatility.median()

    dynamic_prediction = layered_prediction.copy()
    high_vol_threshold = max_score * 0.65
    low_vol_threshold = max_score * 0.45

    dynamic_prediction[high_vol_periods] = (total_score[high_vol_periods] > high_vol_threshold).astype(int)
    dynamic_prediction[~high_vol_periods] = (total_score[~high_vol_periods] > low_vol_threshold).astype(int)

    dynamic_accuracy = (dynamic_prediction == df[target]).mean()

    strategies_results.append({
        'strategy_name': '动态阈值策略',
        'accuracy': dynamic_accuracy,
        'description': '高波动期阈值65%，低波动期阈值45%',
        'factors_used': '基于分层权重策略，动态调整阈值'
    })

    # 策略4: 逻辑回归策略
    feature_factors = top_factors[:10]
    X = df[feature_factors].fillna(method='ffill')
    y = df[target]

    train_size = int(len(X) * 0.7)
    X_train, X_test = X[:train_size], X[train_size:]
    y_train, y_test = y[:train_size], y[train_size:]

    lr_model = LogisticRegression(random_state=42, max_iter=1000)
    lr_model.fit(X_train, y_train)
    lr_pred = lr_model.predict(X_test)
    lr_accuracy = accuracy_score(y_test, lr_pred)

    strategies_results.append({
        'strategy_name': '逻辑回归策略',
        'accuracy': lr_accuracy,
        'description': f'使用TOP10因子的逻辑回归模型',
        'factors_used': ', '.join(feature_factors)
    })

    return pd.DataFrame(strategies_results)

# 构建策略
spread_strategies = build_strategies(df_engineered, all_factor_performance, 'spread_direction', '利差')
yield_strategies = build_strategies(df_engineered, all_factor_performance, 'yield_direction', '30y收益率')

print(f"利差预测最佳策略胜率: {spread_strategies['accuracy'].max():.4f}")
print(f"30y预测最佳策略胜率: {yield_strategies['accuracy'].max():.4f}")

def create_charts(spread_ranking, yield_ranking, spread_strategies, yield_strategies, output_dir):
    """创建4张图表"""
    print("\n生成4张图表...")

    baseline_spread = df_engineered['spread_direction'].mean()
    baseline_yield = df_engineered['yield_direction'].mean()

    # 1. 单因子-利差预测胜率排名
    plt.figure(figsize=(16, 10))
    top20_spread = spread_ranking.head(20)

    colors = ['#FF6B6B' if row['factor_type'] == '工程因子' else '#4ECDC4' for _, row in top20_spread.iterrows()]

    bars = plt.bar(range(len(top20_spread)), top20_spread['spread_win_rate'],
                   color=colors, alpha=0.8, edgecolor='black', linewidth=1)

    plt.axhline(y=0.5, color='red', linestyle='--', linewidth=2, alpha=0.7, label='随机基准线(50%)')
    plt.axhline(y=baseline_spread, color='blue', linestyle='--', linewidth=2, alpha=0.7,
                label=f'基准走阔概率({baseline_spread:.3f})')

    plt.xlabel('因子排名', fontsize=14, fontweight='bold')
    plt.ylabel('预测胜率', fontsize=14, fontweight='bold')
    plt.title('TOP20单因子-利差预测胜率排名\n🔴 工程因子  🔵 原始因子', fontsize=16, fontweight='bold', pad=20)

    factor_labels = []
    for i, factor in enumerate(top20_spread['factor']):
        if len(factor) > 12:
            factor = factor[:10] + '..'
        factor_labels.append(f"{i+1}.\n{factor}")

    plt.xticks(range(len(top20_spread)), factor_labels, rotation=45, ha='right', fontsize=10)

    for i, (bar, rate) in enumerate(zip(bars, top20_spread['spread_win_rate'])):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005,
                f'{rate:.3f}', ha='center', va='bottom', fontweight='bold', fontsize=9)

    plt.legend(loc='upper right', fontsize=12)
    plt.grid(True, alpha=0.3, axis='y')
    plt.tight_layout()
    plt.savefig(f'{output_dir}/单因子-利差预测胜率排名.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✓ 单因子-利差预测胜率排名.png")

    # 2. 因子组合策略-利差预测胜率排名
    plt.figure(figsize=(14, 8))
    spread_strategies_sorted = spread_strategies.sort_values('accuracy', ascending=False)

    strategy_colors = ['#2ECC71', '#3498DB', '#E74C3C', '#F39C12']
    colors = strategy_colors[:len(spread_strategies_sorted)]

    bars = plt.bar(range(len(spread_strategies_sorted)), spread_strategies_sorted['accuracy'],
                   color=colors, alpha=0.8, edgecolor='black', linewidth=1.5)

    plt.axhline(y=0.5, color='red', linestyle='--', linewidth=2, alpha=0.7, label='随机基准线')
    plt.axhline(y=baseline_spread, color='blue', linestyle='--', linewidth=2, alpha=0.7,
                label=f'基准走阔概率({baseline_spread:.3f})')

    plt.xlabel('策略类型', fontsize=14, fontweight='bold')
    plt.ylabel('预测胜率', fontsize=14, fontweight='bold')
    plt.title('因子组合策略-利差预测胜率排名', fontsize=16, fontweight='bold', pad=20)

    strategy_labels = []
    for name in spread_strategies_sorted['strategy_name']:
        name = name.replace('策略', '')
        if len(name) > 8:
            name = name[:6] + '..'
        strategy_labels.append(name)

    plt.xticks(range(len(spread_strategies_sorted)), strategy_labels, rotation=45, ha='right', fontsize=11)

    for i, (bar, acc) in enumerate(zip(bars, spread_strategies_sorted['accuracy'])):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.002,
                f'{acc:.4f}', ha='center', va='bottom', fontweight='bold', fontsize=10)

    plt.legend(loc='upper right', fontsize=12)
    plt.grid(True, alpha=0.3, axis='y')
    plt.ylim(0.48, max(spread_strategies_sorted['accuracy']) + 0.02)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/因子组合策略-利差预测胜率排名.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✓ 因子组合策略-利差预测胜率排名.png")

    # 3. 单因子-30Y收益率预测胜率排名
    plt.figure(figsize=(16, 10))
    top20_yield = yield_ranking.head(20)

    colors = ['#FF6B6B' if row['factor_type'] == '工程因子' else '#4ECDC4' for _, row in top20_yield.iterrows()]

    bars = plt.bar(range(len(top20_yield)), top20_yield['yield_win_rate'],
                   color=colors, alpha=0.8, edgecolor='black', linewidth=1)

    plt.axhline(y=0.5, color='red', linestyle='--', linewidth=2, alpha=0.7, label='随机基准线(50%)')
    plt.axhline(y=baseline_yield, color='blue', linestyle='--', linewidth=2, alpha=0.7,
                label=f'基准上升概率({baseline_yield:.3f})')

    plt.xlabel('因子排名', fontsize=14, fontweight='bold')
    plt.ylabel('预测胜率', fontsize=14, fontweight='bold')
    plt.title('TOP20单因子-30Y收益率预测胜率排名\n🔴 工程因子  🔵 原始因子', fontsize=16, fontweight='bold', pad=20)

    factor_labels = []
    for i, factor in enumerate(top20_yield['factor']):
        if len(factor) > 12:
            factor = factor[:10] + '..'
        factor_labels.append(f"{i+1}.\n{factor}")

    plt.xticks(range(len(top20_yield)), factor_labels, rotation=45, ha='right', fontsize=10)

    for i, (bar, rate) in enumerate(zip(bars, top20_yield['yield_win_rate'])):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005,
                f'{rate:.3f}', ha='center', va='bottom', fontweight='bold', fontsize=9)

    plt.legend(loc='upper right', fontsize=12)
    plt.grid(True, alpha=0.3, axis='y')
    plt.tight_layout()
    plt.savefig(f'{output_dir}/单因子-30Y收益率预测胜率排名.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✓ 单因子-30Y收益率预测胜率排名.png")

    # 4. 因子组合策略-30Y收益率预测胜率排名
    plt.figure(figsize=(14, 8))
    yield_strategies_sorted = yield_strategies.sort_values('accuracy', ascending=False)

    strategy_colors = ['#2ECC71', '#3498DB', '#E74C3C', '#F39C12']
    colors = strategy_colors[:len(yield_strategies_sorted)]

    bars = plt.bar(range(len(yield_strategies_sorted)), yield_strategies_sorted['accuracy'],
                   color=colors, alpha=0.8, edgecolor='black', linewidth=1.5)

    plt.axhline(y=0.5, color='red', linestyle='--', linewidth=2, alpha=0.7, label='随机基准线')
    plt.axhline(y=baseline_yield, color='blue', linestyle='--', linewidth=2, alpha=0.7,
                label=f'基准上升概率({baseline_yield:.3f})')

    plt.xlabel('策略类型', fontsize=14, fontweight='bold')
    plt.ylabel('预测胜率', fontsize=14, fontweight='bold')
    plt.title('因子组合策略-30Y收益率预测胜率排名', fontsize=16, fontweight='bold', pad=20)

    strategy_labels = []
    for name in yield_strategies_sorted['strategy_name']:
        name = name.replace('策略', '')
        if len(name) > 8:
            name = name[:6] + '..'
        strategy_labels.append(name)

    plt.xticks(range(len(yield_strategies_sorted)), strategy_labels, rotation=45, ha='right', fontsize=11)

    for i, (bar, acc) in enumerate(zip(bars, yield_strategies_sorted['accuracy'])):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.002,
                f'{acc:.4f}', ha='center', va='bottom', fontweight='bold', fontsize=10)

    plt.legend(loc='upper right', fontsize=12)
    plt.grid(True, alpha=0.3, axis='y')
    plt.ylim(0.48, max(yield_strategies_sorted['accuracy']) + 0.02)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/因子组合策略-30Y收益率预测胜率排名.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✓ 因子组合策略-30Y收益率预测胜率排名.png")

def save_excel_files(all_factor_performance, spread_ranking, yield_ranking,
                    spread_strategies, yield_strategies, df_engineered, new_factors, output_dir):
    """保存Excel文件"""
    print("\n生成Excel文件...")

    # 1. 因子工程生成的因子具体数值
    with pd.ExcelWriter(f'{output_dir}/因子工程生成的因子具体数值.xlsx') as writer:

        # 工程因子数值
        factor_data = df_engineered[['日期'] + new_factors].copy()
        factor_data.to_excel(writer, sheet_name='工程因子数值', index=False)

        # 因子创建说明
        factor_creation_guide = pd.DataFrame([
            {
                '因子类别': '动量系列因子',
                '创建方法': '利差和收益率的N日变化率',
                '代表因子': '利差动量_3日, 利差动量_5日, 30y动量_3日',
                '计算公式': 'pct_change(N)',
                '投资逻辑': '捕捉利差和收益率的趋势性变化',
                '信号判断标准': '正相关时：因子值>中位数→预测走阔/上升；负相关时：因子值≤中位数→预测走阔/上升'
            },
            {
                '因子类别': '波动率系列因子',
                '创建方法': '利差和收益率的N日滚动标准差',
                '代表因子': '利差波动率_5日, 利差波动率_10日, 30y波动率_5日',
                '计算公式': 'rolling(N).std()',
                '投资逻辑': '衡量市场不确定性和风险偏好',
                '信号判断标准': '正相关时：因子值>中位数→预测走阔/上升；负相关时：因子值≤中位数→预测走阔/上升'
            },
            {
                '因子类别': '技术面增强因子',
                '创建方法': '偏离度的衍生指标',
                '代表因子': '偏离度动量, 偏离度绝对值, 偏离度平方, 偏离度标准化',
                '计算公式': 'diff(), abs(), **2, /std',
                '投资逻辑': '捕捉技术面变化的速度、强度和标准化程度',
                '信号判断标准': '正相关时：因子值>中位数→预测走阔/上升；负相关时：因子值≤中位数→预测走阔/上升'
            },
            {
                '因子类别': '复合基本面因子',
                '创建方法': '多个商品指数的综合',
                '代表因子': '商品综合指数, 商品动量_5日, 商品波动率_10日',
                '计算公式': 'mean(axis=1), pct_change(), rolling().std()',
                '投资逻辑': '综合反映商品市场对债券的影响',
                '信号判断标准': '正相关时：因子值>中位数→预测走阔/上升；负相关时：因子值≤中位数→预测走阔/上升'
            },
            {
                '因子类别': '资金面复合因子',
                '创建方法': 'R007和DR007的衍生指标',
                '代表因子': '资金面利差, 资金面利差动量, 资金面紧张度',
                '计算公式': 'R007-DR007, diff(), (x-mean)/std',
                '投资逻辑': '多角度分析资金面状况',
                '信号判断标准': '正相关时：因子值>中位数→预测走阔/上升；负相关时：因子值≤中位数→预测走阔/上升'
            },
            {
                '因子类别': '交互因子',
                '创建方法': '不同类型因子的乘积',
                '代表因子': '基本面技术面交互, 资金面成交交互',
                '计算公式': 'factor1 * factor2',
                '投资逻辑': '捕捉不同维度因子的协同效应',
                '信号判断标准': '正相关时：因子值>中位数→预测走阔/上升；负相关时：因子值≤中位数→预测走阔/上升'
            },
            {
                '因子类别': '机构行为因子',
                '创建方法': '机构净买入数据的综合',
                '代表因子': '机构净买入总和, 机构净买入动量, 机构行为一致性',
                '计算公式': 'sum(axis=1), diff(), (>0).sum()',
                '投资逻辑': '反映机构投资者的集体行为',
                '信号判断标准': '正相关时：因子值>中位数→预测走阔/上升；负相关时：因子值≤中位数→预测走阔/上升'
            },
            {
                '因子类别': '市场状态因子',
                '创建方法': '基于分位数的二元状态变量',
                '代表因子': '高波动状态, 极端偏离状态',
                '计算公式': '(factor > quantile(0.7/0.8)).astype(int)',
                '投资逻辑': '识别特殊市场环境，调整策略敏感度',
                '信号判断标准': '正相关时：因子值>中位数→预测走阔/上升；负相关时：因子值≤中位数→预测走阔/上升'
            }
        ])
        factor_creation_guide.to_excel(writer, sheet_name='因子创建说明', index=False)

    print("✓ 因子工程生成的因子具体数值.xlsx")

    # 2. 因子组合计算与影响分析
    with pd.ExcelWriter(f'{output_dir}/因子组合计算与影响分析.xlsx') as writer:

        # 全因子表现分析（包含信号规则）
        factor_analysis = all_factor_performance[['factor', 'factor_type', 'spread_win_rate', 'yield_win_rate',
                                                 'spread_correlation', 'yield_correlation', 'spread_significant',
                                                 'yield_significant', 'spread_signal_rule', 'yield_signal_rule',
                                                 'factor_median']].copy()
        factor_analysis.to_excel(writer, sheet_name='全因子表现分析', index=False)

        # 利差预测因子排名
        spread_ranking_detail = spread_ranking[['factor', 'factor_type', 'spread_win_rate', 'spread_correlation',
                                               'spread_significant', 'spread_signal_rule', 'factor_median']].copy()
        spread_ranking_detail.to_excel(writer, sheet_name='利差预测因子排名', index=False)

        # 30y收益率预测因子排名
        yield_ranking_detail = yield_ranking[['factor', 'factor_type', 'yield_win_rate', 'yield_correlation',
                                             'yield_significant', 'yield_signal_rule', 'factor_median']].copy()
        yield_ranking_detail.to_excel(writer, sheet_name='30y收益率预测因子排名', index=False)

        # 利差预测策略结果
        spread_strategies.to_excel(writer, sheet_name='利差预测策略结果', index=False)

        # 30y收益率预测策略结果
        yield_strategies.to_excel(writer, sheet_name='30y收益率预测策略结果', index=False)

        # 策略构建详细说明
        strategy_construction = pd.DataFrame([
            {
                '策略类型': '分层权重策略',
                '权重分配': '交互因子权重5，技术面权重4，机构行为权重3，成交量/资金面权重2，基本面权重1',
                '计算方法': '各类因子分别计算得分，按权重汇总',
                '决策规则': '总得分 > 最大得分/2 → 预测走阔/上升',
                '因子信号生成': '基于因子与目标变量的相关性：正相关时因子值>中位数为看涨信号，负相关时因子值≤中位数为看涨信号',
                '适用场景': '推荐作为主策略，适合大部分市场环境'
            },
            {
                '策略类型': '胜率加权策略',
                '权重分配': '权重 = 各因子历史胜率',
                '计算方法': '因子信号 × 因子胜率，求和后与总权重/2比较',
                '决策规则': '加权得分 > 总权重/2 → 预测走阔/上升',
                '因子信号生成': '基于因子与目标变量的相关性：正相关时因子值>中位数为看涨信号，负相关时因子值≤中位数为看涨信号',
                '适用场景': '因子表现差异明显时使用'
            },
            {
                '策略类型': '动态阈值策略',
                '权重分配': '基于分层权重策略',
                '计算方法': '根据市场波动性动态调整决策阈值',
                '决策规则': '高波动期阈值65%，低波动期阈值45%',
                '因子信号生成': '基于因子与目标变量的相关性：正相关时因子值>中位数为看涨信号，负相关时因子值≤中位数为看涨信号',
                '适用场景': '市场波动较大时期'
            },
            {
                '策略类型': '逻辑回归策略',
                '权重分配': '机器学习自动优化',
                '计算方法': '使用TOP10因子训练逻辑回归模型',
                '决策规则': '模型输出概率 > 0.5 → 预测走阔/上升',
                '因子信号生成': '机器学习模型自动学习因子与目标变量的关系',
                '适用场景': '因子关系复杂时使用'
            }
        ])
        strategy_construction.to_excel(writer, sheet_name='策略构建详细说明', index=False)

    print("✓ 因子组合计算与影响分析.xlsx")

# 生成图表和Excel文件
create_charts(spread_ranking, yield_ranking, spread_strategies, yield_strategies, output_dir)
save_excel_files(all_factor_performance, spread_ranking, yield_ranking,
                 spread_strategies, yield_strategies, df_engineered, new_factors, output_dir)

print(f"\n{'='*100}")
print(f"🎉 所有文件已成功生成到文件夹: {output_dir}")
print(f"{'='*100}")
print(f"📊 图表文件 (4张):")
print(f"  1. 单因子-利差预测胜率排名.png")
print(f"  2. 因子组合策略-利差预测胜率排名.png")
print(f"  3. 单因子-30Y收益率预测胜率排名.png")
print(f"  4. 因子组合策略-30Y收益率预测胜率排名.png")
print(f"📋 Excel文件 (2个):")
print(f"  1. 因子工程生成的因子具体数值.xlsx")
print(f"  2. 因子组合计算与影响分析.xlsx")
print(f"{'='*100}")
