"""
Tencent PCG Metric Optimization System
--------------------------------------
Models:
1. Social Fission Effect: Measures how sharing drives new user acquisition
2. Content Consumption: Analyzes viewing duration and completion rates
"""

import pandas as pd
import numpy as np
from sklearn.linear_model import LinearRegression
from sklearn.ensemble import RandomForestRegressor
from sklearn.model_selection import train_test_split
from sklearn.metrics import r2_score, mean_absolute_error

def calculate_social_fission(user_activity):
    """
    Calculate social fission effect - how sharing drives new user acquisition
    
    Parameters:
    user_activity (DataFrame): Daily user activity data
    
    Returns:
    dict: Social fission metrics and model results
    """
    # Prepare data for modeling
    fission_data = user_activity.groupby('user_id').agg(
        share_count=('share_count', 'sum'),
        new_users_generated=('new_user_flag', 'sum')
    ).reset_index()
    
    # Build social fission model
    X = fission_data[['share_count']]
    y = fission_data['new_users_generated']
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Train model
    model = LinearRegression()
    model.fit(X_train, y_train)
    
    # Evaluate model
    y_pred = model.predict(X_test)
    r2 = r2_score(y_test, y_pred)
    mae = mean_absolute_error(y_test, y_pred)
    
    # Calculate conversion rate
    total_shares = fission_data['share_count'].sum()
    total_new_users = fission_data['new_users_generated'].sum()
    conversion_rate = total_new_users / total_shares if total_shares > 0 else 0
    
    return {
        "conversion_rate": conversion_rate,
        "model_performance": {"r2": r2, "mae": mae},
        "coefficient": model.coef_[0],
        "recommendations": optimize_sharing_features(conversion_rate, model.coef_[0])
    }

def calculate_content_consumption(user_activity):
    """
    Calculate content consumption metrics - viewing duration and completion rates
    
    Parameters:
    user_activity (DataFrame): Daily user activity data
    
    Returns:
    dict: Content consumption metrics and model results
    """
    # Calculate key metrics
    avg_view_duration = user_activity['view_duration'].mean()
    completion_rate = (user_activity['view_duration'] / user_activity['content_duration']).mean()
    
    # Prepare data for modeling completion rate
    content_data = user_activity.groupby('content_id').agg(
        view_duration=('view_duration', 'mean'),
        content_duration=('content_duration', 'mean'),
        likes=('like_count', 'sum'),
        comments=('comment_count', 'sum'),
        shares=('share_count', 'sum')
    ).reset_index()
    
    content_data['completion_rate'] = content_data['view_duration'] / content_data['content_duration']
    
    # Build content consumption model
    X = content_data[['content_duration', 'likes', 'comments', 'shares']]
    y = content_data['completion_rate']
    
    # Split data
    X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=0.2, random_state=42)
    
    # Train model
    model = RandomForestRegressor(n_estimators=100, random_state=42)
    model.fit(X_train, y_train)
    
    # Evaluate model
    y_pred = model.predict(X_test)
    r2 = r2_score(y_test, y_pred)
    mae = mean_absolute_error(y_test, y_pred)
    
    # Feature importance
    feature_importance = dict(zip(X.columns, model.feature_importances_))
    
    return {
        "avg_view_duration": avg_view_duration,
        "completion_rate": completion_rate,
        "model_performance": {"r2": r2, "mae": mae},
        "feature_importance": feature_importance,
        "recommendations": optimize_content_engagement(completion_rate, feature_importance)
    }

def optimize_sharing_features(conversion_rate, coefficient):
    """Generate recommendations to improve sharing conversion"""
    recommendations = []
    
    if conversion_rate < 0.1:
        recommendations.append("Implement share reward program to incentivize users")
    
    if coefficient < 0.5:
        recommendations.append("Optimize share UI to make sharing more prominent")
        recommendations.append("Add social proof indicators showing how many others shared")
    
    return recommendations

def optimize_content_engagement(completion_rate, feature_importance):
    """Generate recommendations to improve content engagement"""
    recommendations = []
    
    if completion_rate < 0.6:
        recommendations.append("Analyze content types with low completion rates for improvement")
    
    if feature_importance.get('content_duration', 0) > 0.3:
        recommendations.append("Experiment with shorter content formats for better completion")
    
    if feature_importance.get('likes', 0) > 0.2:
        recommendations.append("Encourage more likes through UI enhancements and prompts")
    
    return recommendations
