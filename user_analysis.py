"""
Tencent PCG User Behavior Analysis
---------------------------------
Modules:
1. Viewing path analysis
2. User segmentation by demographics
"""

import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt
from collections import defaultdict
from sklearn.cluster import KMeans

def analyze_viewing_paths(user_activity):
    """
    Analyze user viewing paths and identify drop-off points
    
    Parameters:
    user_activity (DataFrame): User activity data with session sequences
    
    Returns:
    dict: Path analysis report with drop-off rates and recommendations
    """
    # Extract sequence of actions per session
    sessions = user_activity.groupby('session_id')['action'].agg(list).reset_index()
    
    # Define expected path: Search → Play → Next → Engagement → Exit
    expected_path = ['search', 'play', 'next', 'engagement', 'exit']
    
    # Calculate path completion metrics
    path_completion = []
    drop_off_points = defaultdict(int)
    
    for actions in sessions['action']:
        # Find position where user deviated from expected path
        for i, action in enumerate(actions):
            if i >= len(expected_path) or action != expected_path[i]:
                drop_off_points[expected_path[i] if i < len(expected_path) else 'after_engagement'] += 1
                path_completion.append(i / len(expected_path))
                break
        else:
            path_completion.append(1.0)  # Completed entire path
    
    # Calculate drop-off rates
    total_sessions = len(sessions)
    drop_off_rates = {step: count/total_sessions for step, count in drop_off_points.items()}
    
    # Identify critical drop-off points
    critical_drop_offs = [step for step, rate in drop_off_rates.items() if rate > 0.2]
    
    return {
        "avg_path_completion": np.mean(path_completion),
        "drop_off_rates": drop_off_rates,
        "critical_drop_offs": critical_drop_offs,
        "recommendations": generate_path_recommendations(critical_drop_offs)
    }

def segment_behavior(user_activity, user_profiles):
    """
    Analyze behavior differences by user segments
    
    Parameters:
    user_activity (DataFrame): User activity data
    user_profiles (DataFrame): User demographic data
    
    Returns:
    dict: Segmentation analysis report with cluster insights
    """
    # Merge activity with profile data
    merged = pd.merge(user_activity, user_profiles, on='user_id')
    
    # Behavior metrics by segment
    segment_metrics = merged.groupby(['age_group', 'region', 'interests']).agg(
        avg_session_duration=('session_duration', 'mean'),
        avg_actions=('action_count', 'mean'),
        completion_rate=('completion_rate', 'mean'),
        like_rate=('like_count', 'mean'),
        comment_rate=('comment_count', 'mean'),
        share_rate=('share_count', 'mean')
    ).reset_index()
    
    # Gen Z specific analysis (age 18-25)
    genz = merged[merged['age_group'] == '18-25']
    genz_metrics = genz.groupby('interests').agg(
        danmaku_usage=('danmaku_interaction', 'mean'),
        social_features=('social_feature_usage', 'mean')
    )
    
    # Cluster analysis for behavior patterns
    cluster_features = merged.groupby('user_id').agg({
        'session_duration': 'mean',
        'content_count': 'sum',
        'like_count': 'sum',
        'comment_count': 'sum',
        'share_count': 'sum',
        'completion_rate': 'mean'
    }).reset_index()
    
    # Normalize features
    from sklearn.preprocessing import StandardScaler
    scaler = StandardScaler()
    X = scaler.fit_transform(cluster_features.drop('user_id', axis=1))
    
    # Apply K-means clustering
    kmeans = KMeans(n_clusters=4, random_state=42)
    cluster_features['segment'] = kmeans.fit_predict(X)
    
    # Profile clusters
    cluster_profiles = cluster_features.groupby('segment').mean()
    
    return {
        "segment_metrics": segment_metrics.to_dict(orient='records'),
        "genz_analysis": genz_metrics.to_dict(),
        "behavior_clusters": cluster_profiles.to_dict(orient='index'),
        "recommendations": generate_segment_recommendations(cluster_profiles)
    }

def generate_path_recommendations(critical_drop_offs):
    """Generate recommendations based on drop-off points"""
    recommendations = []
    
    if 'search' in critical_drop_offs:
        recommendations.append("Optimize search relevance and result presentation")
    
    if 'play' in critical_drop_offs:
        recommendations.append("Improve video loading speed and initial playback experience")
        recommendations.append("Test different video quality options")
    
    if 'next' in critical_drop_offs:
        recommendations.append("Enhance recommendation algorithm for 'next' suggestions")
        recommendations.append("Reduce friction in moving to next content")
    
    if 'engagement' in critical_drop_offs:
        recommendations.append("Add more engaging interactive elements during playback")
        recommendations.append("Implement social features like co-watching")
    
    return recommendations

def generate_segment_recommendations(cluster_profiles):
    """Generate personalized recommendations based on user segments"""
    recommendations = []
    
    # Identify casual viewers cluster (low engagement)
    casual_cluster = cluster_profiles[cluster_profiles['like_count'] < 0.5].index
    if len(casual_cluster) > 0:
        recommendations.append(
            f"For casual viewers (cluster {casual_cluster[0]}): " 
            "Implement re-engagement campaigns and personalized content recommendations"
        )
    
    # Identify highly social cluster (high sharing/commenting)
    social_cluster = cluster_profiles[cluster_profiles['share_count'] > 1.5].index
    if len(social_cluster) > 0:
        recommendations.append(
            f"For social engagers (cluster {social_cluster[0]}): "
            "Enhance social features and implement referral programs"
        )
    
    # Identify Gen Z preferences
    if 'danmaku_usage' in cluster_profiles.columns and cluster_profiles['danmaku_usage'].max() > 0.7:
        recommendations.append(
            "For Gen Z users: Expand danmaku features and integrate with social media"
        )
    
    return recommendations
