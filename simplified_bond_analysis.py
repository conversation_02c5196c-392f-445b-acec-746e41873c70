import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
import os
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['savefig.dpi'] = 300

# 创建输出文件夹
output_dir = '/Users/<USER>/Desktop/量化择时'
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

print("=== 30-10y利差量化预测策略分析 ===")
print("作为固收量化专家，我将深度分析所有因子并构建预测模型")

# 加载数据
file_path = '/Users/<USER>/Desktop/量化择时0526.xlsx'
try:
    print("正在加载数据...")
    df = pd.read_excel(file_path)
    print(f"\n✓ 数据加载成功")
    print(f"数据形状: {df.shape}")
    print(f"数据时间范围: 第1行 到 第{len(df)}行")
    print("数据加载完成，开始分析...")
except Exception as e:
    print(f"✗ 数据加载失败: {e}")
    import traceback
    traceback.print_exc()
    exit()

# 显示列名
print(f"\n数据列名: {list(df.columns)}")

# 定义因子分类
factor_categories = {
    '基本面因子': ['水泥价格指数', '建材综合指数', '南华金属指数', '南华能化指数', '南华工业品指数'],
    '技术面因子': ['偏离度（偏离均线）', '偏离度（偏离MA+std）'],
    '资金面因子': ['R007', 'DR007', '成交量:DR007', '成交量:R007'],
    '权益市场因子': ['1/沪深300PE-10y'],
    '成交因子': ['30Y成交量']
}

# 所有预测因子
all_factors = []
for factors in factor_categories.values():
    all_factors.extend(factors)

# 目标变量
target_var = '30-10y'

print(f"\n因子分类:")
for category, factors in factor_categories.items():
    available_factors = [f for f in factors if f in df.columns]
    missing_factors = [f for f in factors if f not in df.columns]
    print(f"{category}: ")
    print(f"  可用因子: {available_factors}")
    if missing_factors:
        print(f"  缺失因子: {missing_factors}")

def preprocess_data(df):
    """数据预处理"""
    print("\n=== 数据预处理 ===")

    # 检查缺失值
    missing_stats = df.isnull().sum()
    print(f"缺失值统计:")
    for col, missing in missing_stats.items():
        if missing > 0:
            print(f"  {col}: {missing} ({missing/len(df)*100:.1f}%)")

    # 前向填充缺失值
    df_processed = df.fillna(method='ffill')

    # 计算目标变量的变化方向
    df_processed['30-10y_next'] = df_processed[target_var].shift(-1)  # 下一日利差
    df_processed['30-10y_change'] = df_processed['30-10y_next'] - df_processed[target_var]  # 利差变化
    df_processed['30-10y_direction'] = (df_processed['30-10y_change'] > 0).astype(int)  # 1=走阔, 0=收窄

    # 删除最后一行（因为shift(-1)后为NaN）
    df_processed = df_processed[:-1]

    print(f"预处理后数据形状: {df_processed.shape}")
    print(f"利差走阔天数: {df_processed['30-10y_direction'].sum()}")
    print(f"利差收窄天数: {(1-df_processed['30-10y_direction']).sum()}")
    print(f"走阔概率: {df_processed['30-10y_direction'].mean():.3f}")

    return df_processed

df_processed = preprocess_data(df)

def analyze_single_factor_effectiveness(df, factors, target_direction='30-10y_direction', target_level='30-10y'):
    """分析单因子有效性"""
    print(f"\n=== 单因子有效性分析 ===")

    results = []

    for factor in factors:
        if factor not in df.columns:
            print(f"跳过缺失因子: {factor}")
            continue

        print(f"分析因子: {factor}")

        # 计算相关性
        try:
            corr_level = df[factor].corr(df[target_level])
            corr_change = df[factor].corr(df[target_direction])
        except:
            print(f"  无法计算相关性，跳过")
            continue

        # 计算统计显著性
        try:
            _, p_value_level = stats.pearsonr(df[factor].dropna(), df[target_level].dropna())
            _, p_value_change = stats.pearsonr(df[factor].dropna(), df[target_direction].dropna())
        except:
            p_value_level = 1.0
            p_value_change = 1.0

        # 计算预测胜率（基于因子方向）
        factor_median = df[factor].median()
        high_factor = df[factor] > factor_median
        low_factor = df[factor] <= factor_median

        # 高因子值时的走阔概率
        high_prob = df[high_factor][target_direction].mean() if high_factor.sum() > 0 else 0.5
        low_prob = df[low_factor][target_direction].mean() if low_factor.sum() > 0 else 0.5

        # 简单预测策略：如果因子与目标正相关，高因子值预测走阔
        if corr_change > 0:
            predicted_high = 1  # 预测走阔
            predicted_low = 0   # 预测收窄
        else:
            predicted_high = 0  # 预测收窄
            predicted_low = 1   # 预测走阔

        # 计算预测准确率
        high_accuracy = (df[high_factor][target_direction] == predicted_high).mean() if high_factor.sum() > 0 else 0.5
        low_accuracy = (df[low_factor][target_direction] == predicted_low).mean() if low_factor.sum() > 0 else 0.5
        overall_accuracy = (high_accuracy * high_factor.sum() + low_accuracy * low_factor.sum()) / len(df)

        results.append({
            'factor': factor,
            'corr_level': corr_level,
            'corr_direction': corr_change,
            'p_value_level': p_value_level,
            'p_value_direction': p_value_change,
            'significant_level': p_value_level < 0.05,
            'significant_direction': p_value_change < 0.05,
            'high_prob': high_prob,
            'low_prob': low_prob,
            'prediction_accuracy': overall_accuracy,
            'factor_strength': abs(corr_change)
        })

        print(f"  与利差水平相关性: {corr_level:.4f} (p={p_value_level:.4f})")
        print(f"  与利差方向相关性: {corr_change:.4f} (p={p_value_change:.4f})")
        print(f"  预测准确率: {overall_accuracy:.3f}")
        print(f"  高因子值走阔概率: {high_prob:.3f}")
        print(f"  低因子值走阔概率: {low_prob:.3f}")

    return pd.DataFrame(results)

# 分析所有可用因子
available_factors = [f for f in all_factors if f in df_processed.columns]
print(f"\n可用因子: {available_factors}")

factor_analysis = analyze_single_factor_effectiveness(df_processed, available_factors)

# 保存单因子分析结果
factor_analysis.to_excel(f'{output_dir}/单因子有效性分析.xlsx', index=False)
print(f"\n✓ 单因子分析结果已保存到: {output_dir}/单因子有效性分析.xlsx")

def plot_factor_effectiveness(factor_analysis, output_dir):
    """可视化因子有效性"""
    print(f"\n=== 因子有效性可视化 ===")

    if len(factor_analysis) == 0:
        print("没有有效因子数据，跳过可视化")
        return

    # 按预测准确率排序
    factor_analysis_sorted = factor_analysis.sort_values('prediction_accuracy', ascending=False)

    # 1. 因子预测准确率图
    plt.figure(figsize=(14, 8))
    bars = plt.bar(range(len(factor_analysis_sorted)), factor_analysis_sorted['prediction_accuracy'])
    plt.axhline(y=0.5, color='red', linestyle='--', label='随机预测基准线(50%)')
    plt.xlabel('因子')
    plt.ylabel('预测准确率')
    plt.title('各因子预测30-10y利差方向的准确率')
    plt.xticks(range(len(factor_analysis_sorted)), factor_analysis_sorted['factor'], rotation=45, ha='right')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 标注准确率数值
    for i, (bar, acc) in enumerate(zip(bars, factor_analysis_sorted['prediction_accuracy'])):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005,
                f'{acc:.3f}', ha='center', va='bottom', fontsize=9)

    plt.tight_layout()
    plt.savefig(f'{output_dir}/因子预测准确率分析.png')
    plt.show()

    # 2. 因子相关性热力图
    plt.figure(figsize=(12, 8))
    corr_data = factor_analysis.set_index('factor')[['corr_level', 'corr_direction']]
    sns.heatmap(corr_data.T, annot=True, cmap='RdBu_r', center=0, fmt='.3f')
    plt.title('因子与30-10y利差的相关性')
    plt.ylabel('相关性类型')
    plt.xlabel('因子')
    plt.tight_layout()
    plt.savefig(f'{output_dir}/因子相关性热力图.png')
    plt.show()

    # 3. 因子强度vs准确率散点图
    plt.figure(figsize=(10, 8))
    scatter = plt.scatter(factor_analysis['factor_strength'], factor_analysis['prediction_accuracy'],
                         c=factor_analysis['significant_direction'], cmap='RdYlGn', s=100)
    plt.xlabel('因子强度 (|相关系数|)')
    plt.ylabel('预测准确率')
    plt.title('因子强度与预测准确率关系')
    plt.axhline(y=0.5, color='red', linestyle='--', alpha=0.5)
    plt.colorbar(scatter, label='统计显著性')

    # 添加因子名称标注
    for i, row in factor_analysis.iterrows():
        plt.annotate(row['factor'], (row['factor_strength'], row['prediction_accuracy']),
                    xytext=(5, 5), textcoords='offset points', fontsize=8, alpha=0.7)

    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/因子强度与准确率关系.png')
    plt.show()

plot_factor_effectiveness(factor_analysis, output_dir)

def analyze_factor_categories(df, factor_categories, factor_analysis, output_dir):
    """按因子类别分析有效性"""
    print(f"\n=== 因子类别分析 ===")

    category_results = []

    for category, factors in factor_categories.items():
        available_factors = [f for f in factors if f in df.columns]
        if not available_factors:
            continue

        category_analysis = factor_analysis[factor_analysis['factor'].isin(available_factors)]

        if len(category_analysis) == 0:
            continue

        avg_accuracy = category_analysis['prediction_accuracy'].mean()
        max_accuracy = category_analysis['prediction_accuracy'].max()
        significant_count = category_analysis['significant_direction'].sum()
        effective_count = (category_analysis['prediction_accuracy'] > 0.5).sum()

        category_results.append({
            'category': category,
            'factor_count': len(available_factors),
            'avg_accuracy': avg_accuracy,
            'max_accuracy': max_accuracy,
            'significant_count': significant_count,
            'effective_count': effective_count,
            'effectiveness_ratio': effective_count / len(available_factors) if available_factors else 0
        })

        print(f"{category}:")
        print(f"  因子数量: {len(available_factors)}")
        print(f"  平均准确率: {avg_accuracy:.3f}")
        print(f"  最高准确率: {max_accuracy:.3f}")
        print(f"  显著因子数: {significant_count}")
        print(f"  有效因子数: {effective_count}")
        print(f"  有效率: {effective_count/len(available_factors):.1%}")

    if not category_results:
        print("没有可分析的因子类别")
        return pd.DataFrame()

    category_df = pd.DataFrame(category_results)
    category_df.to_excel(f'{output_dir}/因子类别分析.xlsx', index=False)

    # 可视化因子类别效果
    plt.figure(figsize=(12, 8))
    x = range(len(category_df))
    width = 0.35

    plt.bar([i - width/2 for i in x], category_df['avg_accuracy'], width,
            label='平均准确率', alpha=0.8)
    plt.bar([i + width/2 for i in x], category_df['effectiveness_ratio'], width,
            label='有效率', alpha=0.8)

    plt.axhline(y=0.5, color='red', linestyle='--', alpha=0.5, label='基准线')
    plt.xlabel('因子类别')
    plt.ylabel('比率')
    plt.title('各类别因子的预测效果')
    plt.xticks(x, category_df['category'], rotation=45, ha='right')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/因子类别效果对比.png')
    plt.show()

    return category_df

category_analysis = analyze_factor_categories(df_processed, factor_categories, factor_analysis, output_dir)

def generate_final_strategy_report(factor_analysis, category_analysis, output_dir):
    """生成最终策略报告"""
    print(f"\n=== 最终策略报告 ===")

    if len(factor_analysis) == 0:
        print("没有因子分析数据，无法生成报告")
        return

    # 找出最佳因子
    best_factors = factor_analysis.nlargest(5, 'prediction_accuracy')

    # 找出有效因子（准确率>50%）
    effective_factors = factor_analysis[factor_analysis['prediction_accuracy'] > 0.5]

    # 生成报告
    report = f"""
30-10y利差量化预测策略分析报告
=====================================

一、数据概况
- 样本数量: {len(df_processed)}
- 因子总数: {len(factor_analysis)}
- 有效因子数: {len(effective_factors)}
- 基准走阔概率: {df_processed['30-10y_direction'].mean():.3f}

二、单因子分析结果
最佳5个因子:
"""

    for i, (_, factor) in enumerate(best_factors.iterrows(), 1):
        report += f"{i}. {factor['factor']}: 准确率 {factor['prediction_accuracy']:.3f}\n"

    if len(category_analysis) > 0:
        best_category = category_analysis.loc[category_analysis['avg_accuracy'].idxmax()]
        report += f"""
三、因子类别分析
最有效的因子类别: {best_category['category']}
平均准确率: {best_category['avg_accuracy']:.3f}
有效率: {best_category['effectiveness_ratio']:.1%}
"""

    report += f"""
四、有效因子详细分析
"""

    for _, factor in effective_factors.iterrows():
        report += f"""
{factor['factor']}:
- 预测准确率: {factor['prediction_accuracy']:.3f}
- 与利差水平相关性: {factor['corr_level']:.4f}
- 与利差方向相关性: {factor['corr_direction']:.4f}
- 统计显著性: {'是' if factor['significant_direction'] else '否'}
- 高因子值走阔概率: {factor['high_prob']:.3f}
- 低因子值走阔概率: {factor['low_prob']:.3f}
"""

    report += f"""
五、投资建议
基于分析结果，建议重点关注以下因子进行30-10y利差方向预测:

1. 最有效因子组合:
"""

    top_3_factors = effective_factors.nlargest(3, 'prediction_accuracy') if len(effective_factors) >= 3 else effective_factors
    for i, (_, factor) in enumerate(top_3_factors.iterrows(), 1):
        direction_advice = "高因子值预测走阔" if factor['corr_direction'] > 0 else "高因子值预测收窄"
        report += f"   {i}. {factor['factor']} (准确率: {factor['prediction_accuracy']:.3f}, {direction_advice})\n"

    report += f"""
2. 交易策略建议:
   - 当多个有效因子同时发出信号时，可提高预测置信度
   - 建议结合技术面、基本面、资金面因子进行综合判断
   - 重点关注统计显著且预测准确率>55%的因子

六、风险提示
- 模型基于历史数据，未来表现可能有差异
- 建议结合基本面分析和市场环境判断
- 定期重新训练模型以适应市场变化
- 单因子预测存在局限性，建议多因子组合使用
"""

    # 保存报告
    with open(f'{output_dir}/策略分析报告.txt', 'w', encoding='utf-8') as f:
        f.write(report)

    print(report)
    print(f"\n✓ 完整报告已保存到: {output_dir}/策略分析报告.txt")

    # 创建汇总Excel
    with pd.ExcelWriter(f'{output_dir}/量化择时策略汇总.xlsx') as writer:
        factor_analysis.to_excel(writer, sheet_name='单因子分析', index=False)
        if len(category_analysis) > 0:
            category_analysis.to_excel(writer, sheet_name='因子类别分析', index=False)

        # 添加最佳因子汇总
        best_factors.to_excel(writer, sheet_name='最佳因子TOP5', index=False)

        # 添加有效因子汇总
        effective_factors.to_excel(writer, sheet_name='有效因子', index=False)

    print(f"✓ 汇总Excel已保存到: {output_dir}/量化择时策略汇总.xlsx")

generate_final_strategy_report(factor_analysis, category_analysis, output_dir)

print(f"\n=== 分析完成 ===")
print(f"所有结果已保存到: {output_dir}")
print(f"主要输出文件:")
print(f"- 单因子有效性分析.xlsx")
print(f"- 因子类别分析.xlsx")
print(f"- 量化择时策略汇总.xlsx")
print(f"- 策略分析报告.txt")
print(f"- 各类分析图表.png")

print(f"\n=== 核心发现总结 ===")
if len(factor_analysis) > 0:
    best_factor = factor_analysis.loc[factor_analysis['prediction_accuracy'].idxmax()]
    print(f"最佳预测因子: {best_factor['factor']}")
    print(f"最高预测准确率: {best_factor['prediction_accuracy']:.3f}")

    effective_count = (factor_analysis['prediction_accuracy'] > 0.5).sum()
    print(f"有效因子数量: {effective_count}/{len(factor_analysis)}")
    print(f"有效因子比例: {effective_count/len(factor_analysis):.1%}")
else:
    print("未找到有效的预测因子")
