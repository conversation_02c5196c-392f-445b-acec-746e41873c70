import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.optimize import minimize
import warnings
import os
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression, <PERSON>, Lasso
from sklearn.metrics import mean_squared_error, r2_score, mean_absolute_error
from sklearn.preprocessing import StandardScaler
from statsmodels.tsa.stattools import adfuller, grangercausalitytests
from statsmodels.stats.diagnostic import acorr_ljungbox
import statsmodels.api as sm
warnings.filterwarnings('ignore')

# 修复中文字体显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.figsize'] = (16, 10)
plt.rcParams['savefig.dpi'] = 300

# 创建输出文件夹
output_dir = '/Users/<USER>/Desktop/利率指标结果'
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

print("=" * 120)
print("🏦 调集所有算力 - 利率预测指标构建系统")
print("构建周度频率领先利率预测指标")
print("=" * 120)

class InterestRatePredictionSystem:
    """利率预测指标构建系统"""
    
    def __init__(self, file_path):
        """
        初始化利率预测系统
        """
        self.file_path = file_path
        self.daily_factors = None
        self.bond_yields = None
        self.gdp_data = None
        self.weekly_data = None
        self.leading_indicator = None
        self.prediction_results = {}
        
        # 定义因子列表
        self.factor_names = [
            '北京:地铁客运量',
            '中国:票房收入:电影', 
            '中国:30大中城市:成交面积:商品房',
            'R007',
            'DR007', 
            'R007-DR007',
            '中国:逆回购利率:7天',
            '南华工业品指数',
            '期货持仓量(活跃合约):国债期货:10年期',
            '期货成交量:国债期货:10年期'
        ]
        
        print(f"初始化利率预测系统")
        print(f"目标因子数量: {len(self.factor_names)}")
        
    def load_and_clean_data(self):
        """加载和清洗数据"""
        print(f"\n{'='*80}")
        print("📊 加载和清洗数据")
        print(f"{'='*80}")
        
        try:
            # 读取三个sheet
            self.daily_factors = pd.read_excel(self.file_path, sheet_name='日频数据')
            self.bond_yields = pd.read_excel(self.file_path, sheet_name=1)  # 第二个sheet
            self.gdp_data = pd.read_excel(self.file_path, sheet_name=2)     # 第三个sheet
            
            print(f"✓ 日频因子数据形状: {self.daily_factors.shape}")
            print(f"✓ 债券收益率数据形状: {self.bond_yields.shape}")
            print(f"✓ GDP数据形状: {self.gdp_data.shape}")
            
        except Exception as e:
            print(f"✗ 数据加载失败: {e}")
            return False
        
        # 处理日频因子数据
        self._clean_daily_factors()
        
        # 处理债券收益率数据
        self._clean_bond_yields()
        
        # 处理GDP数据
        self._clean_gdp_data()
        
        return True
    
    def _clean_daily_factors(self):
        """清洗日频因子数据"""
        print("清洗日频因子数据...")
        
        df = self.daily_factors.copy()
        
        # 确保日期列为datetime格式
        date_col = df.columns[0]  # 假设第一列是日期
        df[date_col] = pd.to_datetime(df[date_col])
        df = df.rename(columns={date_col: '日期'})
        df = df.sort_values('日期').reset_index(drop=True)
        
        # 检查因子列是否存在
        available_factors = []
        for factor in self.factor_names:
            if factor in df.columns:
                available_factors.append(factor)
            else:
                print(f"⚠️ 因子 '{factor}' 不存在于数据中")
        
        self.factor_names = available_factors
        print(f"可用因子数量: {len(self.factor_names)}")
        
        # 识别交易日因子和全日期因子
        trading_day_factors = ['R007', 'DR007', 'R007-DR007', '南华工业品指数', 
                              '期货持仓量(活跃合约):国债期货:10年期', '期货成交量:国债期货:10年期']
        
        # 删除所有因子都为空的行
        factor_cols = ['日期'] + self.factor_names
        df = df[factor_cols].copy()
        
        # 标记交易日（基于R007是否有值）
        if 'R007' in df.columns:
            df['is_trading_day'] = df['R007'].notna()
        else:
            # 如果没有R007，基于其他交易日因子判断
            trading_factors_available = [f for f in trading_day_factors if f in df.columns]
            if trading_factors_available:
                df['is_trading_day'] = df[trading_factors_available[0]].notna()
            else:
                df['is_trading_day'] = True  # 如果没有交易日因子，假设都是交易日
        
        # 只保留交易日数据
        df_trading = df[df['is_trading_day']].copy()
        
        # 对于交易日因子，直接使用
        # 对于全日期因子，进行前向填充
        for factor in self.factor_names:
            if factor in trading_day_factors:
                # 交易日因子保持原样
                pass
            else:
                # 全日期因子进行前向填充
                df_trading[factor] = df_trading[factor].fillna(method='ffill')
        
        # 删除仍有缺失值的行
        df_trading = df_trading.dropna(subset=self.factor_names)
        
        self.daily_factors = df_trading
        print(f"清洗后日频数据形状: {self.daily_factors.shape}")
        print(f"数据时间范围: {self.daily_factors['日期'].min()} 到 {self.daily_factors['日期'].max()}")
    
    def _clean_bond_yields(self):
        """清洗债券收益率数据"""
        print("清洗债券收益率数据...")
        
        df = self.bond_yields.copy()
        
        # 确保日期列为datetime格式
        date_col = df.columns[0]
        df[date_col] = pd.to_datetime(df[date_col])
        df = df.rename(columns={date_col: '日期'})
        df = df.sort_values('日期').reset_index(drop=True)
        
        # 查找10y、1y收益率列和利差列
        yield_cols = []
        for col in df.columns:
            if '10' in col and ('年' in col or 'y' in col.lower()) and '收益率' in col:
                yield_cols.append(col)
                df = df.rename(columns={col: '10y收益率'})
            elif '1' in col and ('年' in col or 'y' in col.lower()) and '收益率' in col:
                yield_cols.append(col)
                df = df.rename(columns={col: '1y收益率'})
            elif '10-1' in col or ('利差' in col and ('10' in col or '1' in col)):
                yield_cols.append(col)
                df = df.rename(columns={col: '10-1y利差'})
        
        # 如果没有利差列，计算利差
        if '10-1y利差' not in df.columns and '10y收益率' in df.columns and '1y收益率' in df.columns:
            df['10-1y利差'] = df['10y收益率'] - df['1y收益率']
        
        # 只保留需要的列
        keep_cols = ['日期']
        for col in ['10y收益率', '1y收益率', '10-1y利差']:
            if col in df.columns:
                keep_cols.append(col)
        
        df = df[keep_cols].copy()
        df = df.dropna()
        
        self.bond_yields = df
        print(f"清洗后债券收益率数据形状: {self.bond_yields.shape}")
        print(f"可用收益率指标: {[col for col in df.columns if col != '日期']}")
    
    def _clean_gdp_data(self):
        """清洗GDP数据"""
        print("清洗GDP数据...")
        
        df = self.gdp_data.copy()
        
        # 确保日期列为datetime格式
        date_col = df.columns[0]
        df[date_col] = pd.to_datetime(df[date_col])
        df = df.rename(columns={date_col: '日期'})
        df = df.sort_values('日期').reset_index(drop=True)
        
        # 查找GDP同比增速列
        gdp_col = None
        for col in df.columns:
            if 'GDP' in col.upper() and ('同比' in col or '增速' in col):
                gdp_col = col
                break
        
        if gdp_col:
            df = df.rename(columns={gdp_col: 'GDP同比增速'})
            df = df[['日期', 'GDP同比增速']].copy()
            df = df.dropna()
        else:
            print("⚠️ 未找到GDP同比增速列")
            df = pd.DataFrame(columns=['日期', 'GDP同比增速'])
        
        self.gdp_data = df
        print(f"清洗后GDP数据形状: {self.gdp_data.shape}")
    
    def create_weekly_data(self):
        """创建周度数据"""
        print(f"\n{'='*80}")
        print("📅 创建周度数据")
        print(f"{'='*80}")
        
        # 设置日期为索引
        df_daily = self.daily_factors.set_index('日期')
        
        # 添加周标识
        df_daily['week'] = df_daily.index.to_series().dt.to_period('W')
        
        # 定义聚合方法
        agg_methods = {}
        for factor in self.factor_names:
            if factor in ['北京:地铁客运量', '中国:票房收入:电影', '中国:30大中城市:成交面积:商品房']:
                agg_methods[factor] = 'sum'  # 累计指标
            elif factor in ['期货持仓量(活跃合约):国债期货:10年期', '期货成交量:国债期货:10年期']:
                agg_methods[factor] = 'mean'  # 平均值
            else:
                agg_methods[factor] = 'last'  # 期末值
        
        # 按周聚合
        df_weekly = df_daily.groupby('week').agg(agg_methods)
        df_weekly.reset_index(inplace=True)
        df_weekly['日期'] = df_weekly['week'].dt.end_time
        df_weekly = df_weekly.drop('week', axis=1)
        
        # 删除包含NaN的行
        df_weekly = df_weekly.dropna()
        
        self.weekly_data = df_weekly
        print(f"周度数据形状: {self.weekly_data.shape}")
        print(f"周度数据时间范围: {self.weekly_data['日期'].min()} 到 {self.weekly_data['日期'].max()}")
    
    def optimize_factor_weights(self):
        """优化因子权重"""
        print(f"\n{'='*80}")
        print("⚖️ 优化因子权重")
        print(f"{'='*80}")
        
        # 合并周度数据和债券收益率数据
        merged_data = pd.merge(self.weekly_data, self.bond_yields, on='日期', how='inner')
        
        if len(merged_data) < 20:
            print("⚠️ 合并后数据量不足，使用等权重")
            weights = np.ones(len(self.factor_names)) / len(self.factor_names)
            self.factor_weights = dict(zip(self.factor_names, weights))
            return
        
        print(f"合并后数据量: {len(merged_data)}")
        
        # 标准化因子
        scaler = StandardScaler()
        X = merged_data[self.factor_names].values
        X_scaled = scaler.fit_transform(X)
        
        # 目标变量
        target_cols = [col for col in ['10y收益率', '1y收益率', '10-1y利差'] if col in merged_data.columns]
        
        if not target_cols:
            print("⚠️ 未找到目标变量，使用等权重")
            weights = np.ones(len(self.factor_names)) / len(self.factor_names)
            self.factor_weights = dict(zip(self.factor_names, weights))
            return
        
        # 使用主成分分析和相关性优化权重
        best_weights = self._optimize_weights_pca_correlation(X_scaled, merged_data[target_cols].values)
        
        self.factor_weights = dict(zip(self.factor_names, best_weights))
        
        print("优化后的因子权重:")
        for factor, weight in self.factor_weights.items():
            print(f"  {factor}: {weight:.4f}")
    
    def _optimize_weights_pca_correlation(self, X, y):
        """基于PCA和相关性优化权重"""
        from sklearn.decomposition import PCA
        
        # 方法1: 基于与目标变量的相关性
        correlations = []
        for i in range(X.shape[1]):
            corr_sum = 0
            for j in range(y.shape[1]):
                corr = np.corrcoef(X[:, i], y[:, j])[0, 1]
                if not np.isnan(corr):
                    corr_sum += abs(corr)
            correlations.append(corr_sum)
        
        correlations = np.array(correlations)
        corr_weights = correlations / correlations.sum()
        
        # 方法2: 基于PCA的权重
        pca = PCA(n_components=min(3, X.shape[1]))
        pca.fit(X)
        
        # 使用前几个主成分的权重
        pca_weights = np.abs(pca.components_[0])  # 使用第一主成分
        pca_weights = pca_weights / pca_weights.sum()
        
        # 方法3: 基于随机森林的特征重要性
        rf = RandomForestRegressor(n_estimators=100, random_state=42)
        rf.fit(X, y.mean(axis=1))  # 使用目标变量的平均值
        rf_weights = rf.feature_importances_
        
        # 综合权重
        final_weights = 0.4 * corr_weights + 0.3 * pca_weights + 0.3 * rf_weights
        final_weights = final_weights / final_weights.sum()
        
        return final_weights

    def construct_leading_indicator(self):
        """构建领先指标"""
        print(f"\n{'='*80}")
        print("🔮 构建领先指标")
        print(f"{'='*80}")

        # 标准化因子
        scaler = StandardScaler()
        factor_data = self.weekly_data[self.factor_names].values
        factor_data_scaled = scaler.fit_transform(factor_data)

        # 计算加权领先指标
        weights = np.array([self.factor_weights[factor] for factor in self.factor_names])
        leading_indicator_values = np.dot(factor_data_scaled, weights)

        # 创建领先指标数据框
        self.leading_indicator = pd.DataFrame({
            '日期': self.weekly_data['日期'],
            '领先指标': leading_indicator_values
        })

        print(f"领先指标构建完成，数据量: {len(self.leading_indicator)}")
        print(f"领先指标统计: 均值={self.leading_indicator['领先指标'].mean():.4f}, 标准差={self.leading_indicator['领先指标'].std():.4f}")

    def find_optimal_lead_time(self):
        """寻找最优领先时间"""
        print(f"\n{'='*80}")
        print("⏰ 寻找最优领先时间")
        print(f"{'='*80}")

        # 合并领先指标和债券收益率数据
        merged_data = pd.merge(self.leading_indicator, self.bond_yields, on='日期', how='inner')

        if len(merged_data) < 20:
            print("⚠️ 合并后数据量不足")
            return

        target_cols = [col for col in ['10y收益率', '1y收益率', '10-1y利差'] if col in merged_data.columns]

        # 测试不同的领先时间（1-12周）
        lead_times = range(1, 13)
        optimal_leads = {}

        for target in target_cols:
            print(f"\n分析 {target} 的最优领先时间:")

            best_corr = 0
            best_lead = 1
            correlations = []

            for lead in lead_times:
                # 创建领先数据
                indicator_lead = merged_data['领先指标'].shift(lead)
                target_current = merged_data[target]

                # 计算相关性
                valid_mask = indicator_lead.notna() & target_current.notna()
                if valid_mask.sum() > 10:
                    corr = np.corrcoef(indicator_lead[valid_mask], target_current[valid_mask])[0, 1]
                    if not np.isnan(corr):
                        correlations.append(abs(corr))
                        if abs(corr) > best_corr:
                            best_corr = abs(corr)
                            best_lead = lead
                    else:
                        correlations.append(0)
                else:
                    correlations.append(0)

            optimal_leads[target] = {
                'best_lead': best_lead,
                'best_corr': best_corr,
                'all_correlations': correlations
            }

            print(f"  最优领先时间: {best_lead}周")
            print(f"  最高相关性: {best_corr:.4f}")

        self.optimal_leads = optimal_leads
        return optimal_leads

    def perform_prediction_analysis(self):
        """执行预测分析"""
        print(f"\n{'='*80}")
        print("📈 执行预测分析")
        print(f"{'='*80}")

        # 合并数据
        merged_data = pd.merge(self.leading_indicator, self.bond_yields, on='日期', how='inner')
        target_cols = [col for col in ['10y收益率', '1y收益率', '10-1y利差'] if col in merged_data.columns]

        prediction_results = {}

        for target in target_cols:
            print(f"\n预测分析: {target}")

            lead_time = self.optimal_leads[target]['best_lead']

            # 准备数据
            X = merged_data['领先指标'].shift(lead_time).dropna()
            y = merged_data[target].loc[X.index]

            # 分割训练和测试数据
            train_size = int(len(X) * 0.8)
            X_train, X_test = X[:train_size], X[train_size:]
            y_train, y_test = y[:train_size], y[train_size:]

            # 多种预测模型
            models = {
                'Linear Regression': LinearRegression(),
                'Ridge Regression': Ridge(alpha=1.0),
                'Random Forest': RandomForestRegressor(n_estimators=100, random_state=42)
            }

            model_results = {}

            for model_name, model in models.items():
                # 训练模型
                if model_name in ['Linear Regression', 'Ridge Regression']:
                    model.fit(X_train.values.reshape(-1, 1), y_train)
                    y_pred = model.predict(X_test.values.reshape(-1, 1))
                else:
                    model.fit(X_train.values.reshape(-1, 1), y_train)
                    y_pred = model.predict(X_test.values.reshape(-1, 1))

                # 计算评估指标
                mse = mean_squared_error(y_test, y_pred)
                rmse = np.sqrt(mse)
                mae = mean_absolute_error(y_test, y_pred)
                r2 = r2_score(y_test, y_pred)

                model_results[model_name] = {
                    'mse': mse,
                    'rmse': rmse,
                    'mae': mae,
                    'r2': r2,
                    'predictions': y_pred,
                    'actual': y_test.values
                }

                print(f"  {model_name}:")
                print(f"    RMSE: {rmse:.4f}")
                print(f"    MAE: {mae:.4f}")
                print(f"    R²: {r2:.4f}")

            prediction_results[target] = {
                'lead_time': lead_time,
                'models': model_results,
                'X_test': X_test,
                'y_test': y_test
            }

        self.prediction_results = prediction_results
        return prediction_results

    def create_visualizations(self):
        """创建可视化图表"""
        print(f"\n{'='*80}")
        print("📊 创建可视化图表")
        print(f"{'='*80}")

        # 1. 因子权重图
        self._plot_factor_weights()

        # 2. 领先指标时间序列图
        self._plot_leading_indicator()

        # 3. 最优领先时间图
        self._plot_optimal_lead_times()

        # 4. 预测效果图
        self._plot_prediction_results()

        # 5. 相关性热力图
        self._plot_correlation_heatmap()

    def _plot_factor_weights(self):
        """绘制因子权重图"""
        plt.figure(figsize=(14, 8))

        factors = list(self.factor_weights.keys())
        weights = list(self.factor_weights.values())

        # 创建颜色映射
        colors = plt.cm.viridis(np.linspace(0, 1, len(factors)))

        bars = plt.bar(range(len(factors)), weights, color=colors)
        plt.xlabel('因子', fontsize=14, fontweight='bold')
        plt.ylabel('权重', fontsize=14, fontweight='bold')
        plt.title('利率预测因子权重分布', fontsize=16, fontweight='bold')
        plt.xticks(range(len(factors)), factors, rotation=45, ha='right')
        plt.grid(True, alpha=0.3)

        # 添加数值标签
        for i, (bar, weight) in enumerate(zip(bars, weights)):
            plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
                    f'{weight:.3f}', ha='center', va='bottom', fontsize=10)

        plt.tight_layout()
        plt.savefig(f'{output_dir}/因子权重分布图.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✓ 因子权重分布图.png")

    def _plot_leading_indicator(self):
        """绘制领先指标时间序列图"""
        plt.figure(figsize=(16, 10))

        # 合并数据用于绘图
        merged_data = pd.merge(self.leading_indicator, self.bond_yields, on='日期', how='inner')

        # 创建子图
        _, axes = plt.subplots(2, 1, figsize=(16, 12))

        # 上图：领先指标
        axes[0].plot(merged_data['日期'], merged_data['领先指标'],
                    linewidth=2, color='blue', label='领先指标')
        axes[0].set_ylabel('领先指标值', fontsize=12, fontweight='bold')
        axes[0].set_title('周度利率领先指标时间序列', fontsize=14, fontweight='bold')
        axes[0].grid(True, alpha=0.3)
        axes[0].legend()

        # 下图：债券收益率
        target_cols = [col for col in ['10y收益率', '1y收益率', '10-1y利差'] if col in merged_data.columns]
        colors = ['red', 'green', 'orange']

        for i, target in enumerate(target_cols):
            axes[1].plot(merged_data['日期'], merged_data[target],
                        linewidth=2, color=colors[i], label=target)

        axes[1].set_xlabel('日期', fontsize=12, fontweight='bold')
        axes[1].set_ylabel('收益率 (%)', fontsize=12, fontweight='bold')
        axes[1].set_title('债券收益率时间序列', fontsize=14, fontweight='bold')
        axes[1].grid(True, alpha=0.3)
        axes[1].legend()

        plt.tight_layout()
        plt.savefig(f'{output_dir}/领先指标时间序列图.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✓ 领先指标时间序列图.png")

    def _plot_optimal_lead_times(self):
        """绘制最优领先时间图"""
        plt.figure(figsize=(15, 10))

        target_cols = list(self.optimal_leads.keys())
        lead_times = range(1, 13)

        for i, target in enumerate(target_cols):
            correlations = self.optimal_leads[target]['all_correlations']
            best_lead = self.optimal_leads[target]['best_lead']

            plt.subplot(2, 2, i+1)
            plt.plot(lead_times, correlations, 'o-', linewidth=2, markersize=6)
            plt.axvline(x=best_lead, color='red', linestyle='--', alpha=0.7,
                       label=f'最优领先时间: {best_lead}周')
            plt.xlabel('领先时间 (周)', fontsize=12)
            plt.ylabel('相关系数 (绝对值)', fontsize=12)
            plt.title(f'{target} - 领先时间分析', fontsize=13, fontweight='bold')
            plt.grid(True, alpha=0.3)
            plt.legend()

        plt.tight_layout()
        plt.savefig(f'{output_dir}/最优领先时间分析图.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✓ 最优领先时间分析图.png")

    def _plot_prediction_results(self):
        """绘制预测结果图"""
        target_cols = list(self.prediction_results.keys())

        for target in target_cols:
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle(f'{target} 预测结果分析', fontsize=16, fontweight='bold')

            models = self.prediction_results[target]['models']
            y_test = self.prediction_results[target]['y_test']

            model_names = list(models.keys())

            for i, model_name in enumerate(model_names):
                if i >= 3:  # 最多显示3个模型
                    break

                row = i // 2
                col = i % 2

                y_pred = models[model_name]['predictions']
                r2 = models[model_name]['r2']

                # 预测vs实际散点图
                axes[row, col].scatter(y_test, y_pred, alpha=0.6)
                axes[row, col].plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()],
                                   'r--', linewidth=2)
                axes[row, col].set_xlabel('实际值', fontsize=12)
                axes[row, col].set_ylabel('预测值', fontsize=12)
                axes[row, col].set_title(f'{model_name} (R² = {r2:.3f})', fontsize=13)
                axes[row, col].grid(True, alpha=0.3)

            # 时间序列对比图
            if len(model_names) >= 3:
                axes[1, 1].plot(range(len(y_test)), y_test.values, 'b-', linewidth=2, label='实际值')
                for model_name in model_names[:2]:  # 显示前两个模型的预测
                    y_pred = models[model_name]['predictions']
                    axes[1, 1].plot(range(len(y_pred)), y_pred, '--', linewidth=2, label=f'{model_name}预测')
                axes[1, 1].set_xlabel('时间', fontsize=12)
                axes[1, 1].set_ylabel(target, fontsize=12)
                axes[1, 1].set_title('预测vs实际时间序列', fontsize=13)
                axes[1, 1].legend()
                axes[1, 1].grid(True, alpha=0.3)

            plt.tight_layout()
            plt.savefig(f'{output_dir}/{target}_预测结果图.png', dpi=300, bbox_inches='tight')
            plt.close()
            print(f"✓ {target}_预测结果图.png")

    def _plot_correlation_heatmap(self):
        """绘制相关性热力图"""
        # 合并所有数据
        merged_data = pd.merge(self.weekly_data, self.bond_yields, on='日期', how='inner')
        merged_data = pd.merge(merged_data, self.leading_indicator, on='日期', how='inner')

        # 选择关键列进行相关性分析
        corr_cols = self.factor_names + ['领先指标']
        target_cols = [col for col in ['10y收益率', '1y收益率', '10-1y利差'] if col in merged_data.columns]
        corr_cols.extend(target_cols)

        corr_matrix = merged_data[corr_cols].corr()

        plt.figure(figsize=(14, 12))
        mask = np.triu(np.ones_like(corr_matrix, dtype=bool))
        sns.heatmap(corr_matrix, mask=mask, annot=True, cmap='coolwarm', center=0,
                   square=True, linewidths=0.5, cbar_kws={"shrink": .8}, fmt='.2f')
        plt.title('因子与收益率相关性热力图', fontsize=16, fontweight='bold')
        plt.tight_layout()
        plt.savefig(f'{output_dir}/相关性热力图.png', dpi=300, bbox_inches='tight')
        plt.close()
        print("✓ 相关性热力图.png")

    def save_results(self):
        """保存结果到Excel"""
        print(f"\n{'='*80}")
        print("💾 保存结果到Excel")
        print(f"{'='*80}")

        with pd.ExcelWriter(f'{output_dir}/利率预测分析结果.xlsx') as writer:

            # 1. 清洗后的数据
            self.daily_factors.to_excel(writer, sheet_name='清洗后日频数据', index=False)
            self.weekly_data.to_excel(writer, sheet_name='周度数据', index=False)
            self.bond_yields.to_excel(writer, sheet_name='债券收益率数据', index=False)

            # 2. 因子权重
            weights_df = pd.DataFrame(list(self.factor_weights.items()),
                                    columns=['因子名称', '权重'])
            weights_df.to_excel(writer, sheet_name='因子权重', index=False)

            # 3. 领先指标
            self.leading_indicator.to_excel(writer, sheet_name='领先指标', index=False)

            # 4. 最优领先时间
            lead_times_data = []
            for target, info in self.optimal_leads.items():
                lead_times_data.append({
                    '目标变量': target,
                    '最优领先时间(周)': info['best_lead'],
                    '最高相关性': info['best_corr']
                })
            lead_times_df = pd.DataFrame(lead_times_data)
            lead_times_df.to_excel(writer, sheet_name='最优领先时间', index=False)

            # 5. 预测结果汇总
            prediction_summary = []
            for target, results in self.prediction_results.items():
                for model_name, metrics in results['models'].items():
                    prediction_summary.append({
                        '目标变量': target,
                        '模型': model_name,
                        '领先时间(周)': results['lead_time'],
                        'RMSE': metrics['rmse'],
                        'MAE': metrics['mae'],
                        'R²': metrics['r2']
                    })

            prediction_df = pd.DataFrame(prediction_summary)
            prediction_df.to_excel(writer, sheet_name='预测结果汇总', index=False)

        print("✓ 利率预测分析结果.xlsx")

    def run_complete_analysis(self):
        """运行完整分析"""
        print("🚀 开始完整分析流程...")

        # 1. 加载和清洗数据
        if not self.load_and_clean_data():
            return False

        # 2. 创建周度数据
        self.create_weekly_data()

        # 3. 优化因子权重
        self.optimize_factor_weights()

        # 4. 构建领先指标
        self.construct_leading_indicator()

        # 5. 寻找最优领先时间
        self.find_optimal_lead_time()

        # 6. 执行预测分析
        self.perform_prediction_analysis()

        # 7. 创建可视化
        self.create_visualizations()

        # 8. 保存结果
        self.save_results()

        print(f"\n{'='*120}")
        print("🎉 完整分析流程完成！")
        print(f"{'='*120}")

        return True

# 运行分析
if __name__ == "__main__":
    # 初始化系统
    file_path = '/Users/<USER>/Desktop/利率指标底稿-0707.xlsx'
    system = InterestRatePredictionSystem(file_path)

    # 运行完整分析
    success = system.run_complete_analysis()

    if success:
        print(f"\n📁 所有结果已保存到: {output_dir}")
        print("📊 生成的文件:")
        print("  - 利率预测分析结果.xlsx (数据和统计结果)")
        print("  - 因子权重分布图.png")
        print("  - 领先指标时间序列图.png")
        print("  - 最优领先时间分析图.png")
        print("  - 各目标变量预测结果图.png")
        print("  - 相关性热力图.png")
    else:
        print("❌ 分析过程中出现错误")
