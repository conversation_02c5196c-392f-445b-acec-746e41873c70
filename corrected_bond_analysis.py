import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
import os
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['savefig.dpi'] = 300

# 创建输出文件夹
output_dir = '/Users/<USER>/Desktop/量化择时'
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

print("=== 30-10y利差量化预测策略分析（修正版）===")
print("分析期间：2024/1/2 到 2025/5/23")

# 加载数据
file_path = '/Users/<USER>/Desktop/量化择时0526.xlsx'
try:
    print("正在加载数据...")
    df = pd.read_excel(file_path)
    print(f"\n✓ 数据加载成功")
    print(f"原始数据形状: {df.shape}")
except Exception as e:
    print(f"✗ 数据加载失败: {e}")
    exit()

# 检查数据并筛选正确的时间范围
print(f"\n原始数据列名: {list(df.columns)}")

# 假设第一列是日期，如果不是需要调整
if 'Unnamed: 0' in df.columns:
    df = df.drop('Unnamed: 0', axis=1)

# 重新检查数据
print(f"处理后数据形状: {df.shape}")
print(f"实际样本数量: {len(df)}")

# 定义因子分类（根据您的要求）
factor_categories = {
    '基本面因子': ['水泥价格指数', '建材综合指数', '南华金属指数', '南华能化指数', '南华工业品指数'],
    '技术面因子': ['偏离度（偏离均线）', '偏离度（偏离MA+std）'],
    '资金面因子': ['R007', 'DR007', '成交量:DR007', '成交量:R007'],
    '权益市场因子': ['1/沪深300PE-10y'],
    '成交因子': ['30Y成交量']
}

# 所有预测因子
all_factors = []
for factors in factor_categories.values():
    all_factors.extend(factors)

# 目标变量
target_var = '30-10y'

print(f"\n因子分类检查:")
for category, factors in factor_categories.items():
    available_factors = [f for f in factors if f in df.columns]
    missing_factors = [f for f in factors if f not in df.columns]
    print(f"{category}: ")
    print(f"  可用因子: {available_factors}")
    if missing_factors:
        print(f"  缺失因子: {missing_factors}")

def preprocess_data_correctly(df):
    """正确的数据预处理"""
    print("\n=== 数据预处理（修正版）===")

    # 检查缺失值
    missing_stats = df.isnull().sum()
    print(f"缺失值统计:")
    missing_cols = missing_stats[missing_stats > 0]
    if len(missing_cols) > 0:
        for col, missing in missing_cols.items():
            print(f"  {col}: {missing} ({missing/len(df)*100:.1f}%)")
    else:
        print("  无缺失值")

    # 前向填充缺失值
    df_processed = df.fillna(method='ffill').copy()

    # 正确计算利差变化方向
    # 当前日的因子 -> 预测下一日的利差变化
    df_processed['30-10y_next'] = df_processed[target_var].shift(-1)  # 下一日利差
    df_processed['30-10y_change'] = df_processed['30-10y_next'] - df_processed[target_var]  # 利差变化

    # 1=走阔(下一日利差>当日利差), 0=收窄(下一日利差<=当日利差)
    df_processed['30-10y_direction'] = (df_processed['30-10y_change'] > 0).astype(int)

    # 删除最后一行（因为shift(-1)后为NaN）和第一行（如果有NaN）
    df_processed = df_processed.dropna()

    # 验证数据范围
    actual_samples = len(df_processed)
    expected_samples = 346  # 2024/1/2到2025/5/23的交易日

    print(f"预处理后数据形状: {df_processed.shape}")
    print(f"实际样本数量: {actual_samples}")
    print(f"期望样本数量: {expected_samples}")

    if actual_samples != expected_samples:
        print(f"⚠️  样本数量与期望不符，请检查数据范围")

    # 统计利差变化情况
    spread_widen_days = df_processed['30-10y_direction'].sum()
    spread_narrow_days = len(df_processed) - spread_widen_days
    widen_probability = df_processed['30-10y_direction'].mean()

    print(f"\n利差变化统计:")
    print(f"利差走阔天数: {spread_widen_days}")
    print(f"利差收窄天数: {spread_narrow_days}")
    print(f"走阔概率: {widen_probability:.3f}")
    print(f"收窄概率: {1-widen_probability:.3f}")

    # 显示利差的基本统计
    print(f"\n30-10y利差基本统计:")
    print(f"均值: {df_processed[target_var].mean():.2f}bp")
    print(f"标准差: {df_processed[target_var].std():.2f}bp")
    print(f"最小值: {df_processed[target_var].min():.2f}bp")
    print(f"最大值: {df_processed[target_var].max():.2f}bp")

    return df_processed

df_processed = preprocess_data_correctly(df)

def analyze_factor_predictive_power(df, factors, target_direction='30-10y_direction', target_level='30-10y'):
    """分析因子的预测能力"""
    print(f"\n=== 因子预测能力分析 ===")
    print("分析逻辑：使用当日因子水平预测下一日利差变化方向")

    results = []

    for factor in factors:
        if factor not in df.columns:
            print(f"跳过缺失因子: {factor}")
            continue

        print(f"\n分析因子: {factor}")

        # 计算相关性
        try:
            # 当日因子与下一日利差方向的相关性
            corr_direction = df[factor].corr(df[target_direction])
            # 当日因子与当日利差水平的相关性（参考）
            corr_level = df[factor].corr(df[target_level])
        except:
            print(f"  无法计算相关性，跳过")
            continue

        # 计算统计显著性
        try:
            _, p_value_direction = stats.pearsonr(df[factor].dropna(), df[target_direction].dropna())
            _, p_value_level = stats.pearsonr(df[factor].dropna(), df[target_level].dropna())
        except:
            p_value_direction = 1.0
            p_value_level = 1.0

        # 基于因子分位数的预测策略
        # 将因子分为高、中、低三组
        factor_q33 = df[factor].quantile(0.33)
        factor_q67 = df[factor].quantile(0.67)

        low_factor = df[factor] <= factor_q33
        mid_factor = (df[factor] > factor_q33) & (df[factor] <= factor_q67)
        high_factor = df[factor] > factor_q67

        # 各组的走阔概率
        low_prob = df[low_factor][target_direction].mean() if low_factor.sum() > 0 else 0.5
        mid_prob = df[mid_factor][target_direction].mean() if mid_factor.sum() > 0 else 0.5
        high_prob = df[high_factor][target_direction].mean() if high_factor.sum() > 0 else 0.5

        # 预测策略：基于相关性方向
        if corr_direction > 0:
            # 正相关：高因子值预测走阔，低因子值预测收窄
            high_prediction = 1  # 预测走阔
            low_prediction = 0   # 预测收窄
        else:
            # 负相关：高因子值预测收窄，低因子值预测走阔
            high_prediction = 0  # 预测收窄
            low_prediction = 1   # 预测走阔

        # 计算预测准确率
        high_accuracy = (df[high_factor][target_direction] == high_prediction).mean() if high_factor.sum() > 0 else 0.5
        low_accuracy = (df[low_factor][target_direction] == low_prediction).mean() if low_factor.sum() > 0 else 0.5

        # 整体预测准确率（加权平均）
        overall_accuracy = (high_accuracy * high_factor.sum() +
                          0.5 * mid_factor.sum() +
                          low_accuracy * low_factor.sum()) / len(df)

        # 计算信息比率（相关性/标准误差的近似）
        information_ratio = abs(corr_direction) / (1/np.sqrt(len(df))) if len(df) > 0 else 0

        results.append({
            'factor': factor,
            'corr_level': corr_level,
            'corr_direction': corr_direction,
            'p_value_level': p_value_level,
            'p_value_direction': p_value_direction,
            'significant_level': p_value_level < 0.05,
            'significant_direction': p_value_direction < 0.05,
            'low_group_prob': low_prob,
            'mid_group_prob': mid_prob,
            'high_group_prob': high_prob,
            'prediction_accuracy': overall_accuracy,
            'factor_strength': abs(corr_direction),
            'information_ratio': information_ratio,
            'sample_size': len(df[~df[factor].isna()])
        })

        print(f"  与当日利差水平相关性: {corr_level:.4f} (p={p_value_level:.4f})")
        print(f"  与下日利差方向相关性: {corr_direction:.4f} (p={p_value_direction:.4f})")
        print(f"  预测准确率: {overall_accuracy:.3f}")
        print(f"  低分位组走阔概率: {low_prob:.3f}")
        print(f"  中分位组走阔概率: {mid_prob:.3f}")
        print(f"  高分位组走阔概率: {high_prob:.3f}")
        print(f"  统计显著性: {'是' if p_value_direction < 0.05 else '否'}")

    return pd.DataFrame(results)

# 分析所有可用因子
available_factors = [f for f in all_factors if f in df_processed.columns]
print(f"\n可用因子列表: {available_factors}")
print(f"可用因子数量: {len(available_factors)}")

factor_analysis = analyze_factor_predictive_power(df_processed, available_factors)

# 保存分析结果
factor_analysis.to_excel(f'{output_dir}/修正版_单因子预测能力分析.xlsx', index=False)
print(f"\n✓ 因子分析结果已保存到: {output_dir}/修正版_单因子预测能力分析.xlsx")

def create_factor_effectiveness_visualization(factor_analysis, output_dir):
    """创建因子有效性可视化"""
    print(f"\n=== 因子有效性可视化 ===")

    if len(factor_analysis) == 0:
        print("没有有效因子数据，跳过可视化")
        return

    # 按预测准确率排序
    factor_sorted = factor_analysis.sort_values('prediction_accuracy', ascending=False)

    # 1. 因子预测准确率排名图
    plt.figure(figsize=(14, 8))
    colors = ['green' if acc > 0.5 else 'red' for acc in factor_sorted['prediction_accuracy']]
    bars = plt.bar(range(len(factor_sorted)), factor_sorted['prediction_accuracy'], color=colors, alpha=0.7)

    plt.axhline(y=0.5, color='black', linestyle='--', label='随机预测基准线(50%)', linewidth=2)
    plt.xlabel('因子', fontsize=12)
    plt.ylabel('预测准确率', fontsize=12)
    plt.title('各因子预测30-10y利差方向的准确率排名', fontsize=14)
    plt.xticks(range(len(factor_sorted)), factor_sorted['factor'], rotation=45, ha='right')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 标注准确率数值
    for i, (bar, acc) in enumerate(zip(bars, factor_sorted['prediction_accuracy'])):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{acc:.3f}', ha='center', va='bottom', fontsize=9)

    plt.tight_layout()
    plt.savefig(f'{output_dir}/因子预测准确率排名.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 2. 因子相关性与显著性分析
    plt.figure(figsize=(12, 8))

    # 创建散点图：x轴为相关性强度，y轴为预测准确率，颜色表示显著性
    scatter = plt.scatter(factor_analysis['factor_strength'],
                         factor_analysis['prediction_accuracy'],
                         c=factor_analysis['significant_direction'],
                         s=100, cmap='RdYlGn', alpha=0.7)

    plt.axhline(y=0.5, color='red', linestyle='--', alpha=0.5, label='随机预测基准')
    plt.xlabel('因子强度 (|相关系数|)', fontsize=12)
    plt.ylabel('预测准确率', fontsize=12)
    plt.title('因子强度与预测准确率关系', fontsize=14)
    plt.colorbar(scatter, label='统计显著性 (1=显著, 0=不显著)')

    # 添加因子名称标注
    for i, row in factor_analysis.iterrows():
        plt.annotate(row['factor'],
                    (row['factor_strength'], row['prediction_accuracy']),
                    xytext=(5, 5), textcoords='offset points',
                    fontsize=8, alpha=0.8)

    plt.grid(True, alpha=0.3)
    plt.legend()
    plt.tight_layout()
    plt.savefig(f'{output_dir}/因子强度与准确率关系.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 3. 因子分组走阔概率对比
    fig, ax = plt.subplots(figsize=(14, 10))

    x = np.arange(len(factor_analysis))
    width = 0.25

    bars1 = ax.bar(x - width, factor_analysis['low_group_prob'], width,
                   label='低分位组', alpha=0.8, color='lightcoral')
    bars2 = ax.bar(x, factor_analysis['mid_group_prob'], width,
                   label='中分位组', alpha=0.8, color='lightblue')
    bars3 = ax.bar(x + width, factor_analysis['high_group_prob'], width,
                   label='高分位组', alpha=0.8, color='lightgreen')

    ax.axhline(y=df_processed['30-10y_direction'].mean(), color='red',
               linestyle='--', label=f'整体走阔概率({df_processed["30-10y_direction"].mean():.3f})')

    ax.set_xlabel('因子')
    ax.set_ylabel('走阔概率')
    ax.set_title('各因子分组的利差走阔概率对比')
    ax.set_xticks(x)
    ax.set_xticklabels(factor_analysis['factor'], rotation=45, ha='right')
    ax.legend()
    ax.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(f'{output_dir}/因子分组走阔概率对比.png', dpi=300, bbox_inches='tight')
    plt.show()

create_factor_effectiveness_visualization(factor_analysis, output_dir)

def analyze_factor_categories_performance(df, factor_categories, factor_analysis, output_dir):
    """分析各类别因子的表现"""
    print(f"\n=== 因子类别表现分析 ===")

    category_results = []

    for category, factors in factor_categories.items():
        available_factors = [f for f in factors if f in df.columns]
        if not available_factors:
            continue

        category_data = factor_analysis[factor_analysis['factor'].isin(available_factors)]

        if len(category_data) == 0:
            continue

        # 计算类别统计
        avg_accuracy = category_data['prediction_accuracy'].mean()
        max_accuracy = category_data['prediction_accuracy'].max()
        min_accuracy = category_data['prediction_accuracy'].min()
        significant_count = category_data['significant_direction'].sum()
        effective_count = (category_data['prediction_accuracy'] > 0.5).sum()
        avg_correlation = category_data['factor_strength'].mean()

        category_results.append({
            'category': category,
            'factor_count': len(available_factors),
            'avg_accuracy': avg_accuracy,
            'max_accuracy': max_accuracy,
            'min_accuracy': min_accuracy,
            'significant_count': significant_count,
            'effective_count': effective_count,
            'effectiveness_ratio': effective_count / len(available_factors) if available_factors else 0,
            'avg_correlation': avg_correlation
        })

        print(f"\n{category}:")
        print(f"  因子数量: {len(available_factors)}")
        print(f"  平均准确率: {avg_accuracy:.3f}")
        print(f"  最高准确率: {max_accuracy:.3f}")
        print(f"  最低准确率: {min_accuracy:.3f}")
        print(f"  显著因子数: {significant_count}")
        print(f"  有效因子数: {effective_count}")
        print(f"  有效率: {effective_count/len(available_factors):.1%}")
        print(f"  平均相关性强度: {avg_correlation:.3f}")

    if not category_results:
        print("没有可分析的因子类别")
        return pd.DataFrame()

    category_df = pd.DataFrame(category_results)
    category_df.to_excel(f'{output_dir}/因子类别表现分析.xlsx', index=False)

    # 可视化因子类别表现
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

    # 1. 平均准确率对比
    bars1 = ax1.bar(category_df['category'], category_df['avg_accuracy'], alpha=0.7)
    ax1.axhline(y=0.5, color='red', linestyle='--', alpha=0.5)
    ax1.set_title('各类别因子平均预测准确率')
    ax1.set_ylabel('平均准确率')
    ax1.tick_params(axis='x', rotation=45)
    for bar, acc in zip(bars1, category_df['avg_accuracy']):
        ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{acc:.3f}', ha='center', va='bottom')

    # 2. 有效率对比
    bars2 = ax2.bar(category_df['category'], category_df['effectiveness_ratio'], alpha=0.7, color='orange')
    ax2.set_title('各类别因子有效率')
    ax2.set_ylabel('有效率')
    ax2.tick_params(axis='x', rotation=45)
    for bar, ratio in zip(bars2, category_df['effectiveness_ratio']):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.02,
                f'{ratio:.1%}', ha='center', va='bottom')

    # 3. 显著因子数量
    bars3 = ax3.bar(category_df['category'], category_df['significant_count'], alpha=0.7, color='green')
    ax3.set_title('各类别显著因子数量')
    ax3.set_ylabel('显著因子数')
    ax3.tick_params(axis='x', rotation=45)
    for bar, count in zip(bars3, category_df['significant_count']):
        ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                f'{int(count)}', ha='center', va='bottom')

    # 4. 平均相关性强度
    bars4 = ax4.bar(category_df['category'], category_df['avg_correlation'], alpha=0.7, color='purple')
    ax4.set_title('各类别因子平均相关性强度')
    ax4.set_ylabel('平均相关性强度')
    ax4.tick_params(axis='x', rotation=45)
    for bar, corr in zip(bars4, category_df['avg_correlation']):
        ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005,
                f'{corr:.3f}', ha='center', va='bottom')

    plt.tight_layout()
    plt.savefig(f'{output_dir}/因子类别综合表现.png', dpi=300, bbox_inches='tight')
    plt.show()

    return category_df

category_analysis = analyze_factor_categories_performance(df_processed, factor_categories, factor_analysis, output_dir)

def build_multi_factor_strategy(factor_analysis, df_processed, output_dir):
    """构建多因子组合策略"""
    print(f"\n=== 多因子组合策略构建 ===")

    # 选择有效因子（准确率>50%且统计显著）
    effective_factors = factor_analysis[
        (factor_analysis['prediction_accuracy'] > 0.5) &
        (factor_analysis['significant_direction'] == True)
    ].sort_values('prediction_accuracy', ascending=False)

    print(f"有效因子数量: {len(effective_factors)}")
    if len(effective_factors) > 0:
        print("有效因子列表:")
        for i, (_, factor) in enumerate(effective_factors.iterrows(), 1):
            print(f"  {i}. {factor['factor']}: 准确率 {factor['prediction_accuracy']:.3f}")

    # 构建简单的多因子策略
    if len(effective_factors) >= 2:
        # 选择前3个最有效的因子
        top_factors = effective_factors.head(3)['factor'].tolist()

        print(f"\n构建基于前3个因子的组合策略: {top_factors}")

        # 为每个因子生成信号
        signals = pd.DataFrame(index=df_processed.index)

        for factor in top_factors:
            factor_data = factor_analysis[factor_analysis['factor'] == factor].iloc[0]

            # 基于相关性方向生成信号
            if factor_data['corr_direction'] > 0:
                # 正相关：高于中位数预测走阔
                signals[f'{factor}_signal'] = (df_processed[factor] > df_processed[factor].median()).astype(int)
            else:
                # 负相关：低于中位数预测走阔
                signals[f'{factor}_signal'] = (df_processed[factor] < df_processed[factor].median()).astype(int)

        # 组合信号：多数投票
        signals['combined_signal'] = signals.sum(axis=1)
        signals['prediction'] = (signals['combined_signal'] >= len(top_factors)/2).astype(int)

        # 计算组合策略准确率
        actual = df_processed['30-10y_direction']
        combined_accuracy = (signals['prediction'] == actual).mean()

        print(f"多因子组合策略准确率: {combined_accuracy:.3f}")

        # 保存策略结果
        strategy_results = pd.DataFrame({
            'date_index': df_processed.index,
            'actual_direction': actual,
            'predicted_direction': signals['prediction'],
            'combined_signal_strength': signals['combined_signal'],
            'correct_prediction': (signals['prediction'] == actual)
        })

        strategy_results.to_excel(f'{output_dir}/多因子组合策略结果.xlsx', index=False)

        return strategy_results, top_factors, combined_accuracy
    else:
        print("有效因子不足，无法构建多因子策略")
        return None, [], 0

strategy_results, top_factors, combined_accuracy = build_multi_factor_strategy(factor_analysis, df_processed, output_dir)

def generate_comprehensive_report(factor_analysis, category_analysis, strategy_results, top_factors, combined_accuracy, df_processed, output_dir):
    """生成综合分析报告"""
    print(f"\n=== 生成综合分析报告 ===")

    # 基本统计
    total_samples = len(df_processed)
    widen_days = df_processed['30-10y_direction'].sum()
    narrow_days = total_samples - widen_days
    widen_prob = df_processed['30-10y_direction'].mean()

    # 最佳因子
    if len(factor_analysis) > 0:
        best_factor = factor_analysis.loc[factor_analysis['prediction_accuracy'].idxmax()]
        effective_factors = factor_analysis[factor_analysis['prediction_accuracy'] > 0.5]
    else:
        best_factor = None
        effective_factors = pd.DataFrame()

    # 生成报告
    report = f"""
30-10y利差量化预测策略深度分析报告
=====================================
分析期间：2024年1月2日 - 2025年5月23日

一、数据概况
- 总样本数量: {total_samples}个交易日
- 利差走阔天数: {widen_days}天
- 利差收窄天数: {narrow_days}天
- 基准走阔概率: {widen_prob:.3f} ({widen_prob*100:.1f}%)
- 基准收窄概率: {1-widen_prob:.3f} ({(1-widen_prob)*100:.1f}%)

二、利差基本特征
- 利差均值: {df_processed['30-10y'].mean():.2f}bp
- 利差标准差: {df_processed['30-10y'].std():.2f}bp
- 利差最小值: {df_processed['30-10y'].min():.2f}bp
- 利差最大值: {df_processed['30-10y'].max():.2f}bp

三、单因子分析结果
- 分析因子总数: {len(factor_analysis)}
- 有效因子数量: {len(effective_factors)} (准确率>50%)
- 有效因子比例: {len(effective_factors)/len(factor_analysis)*100:.1f}%
"""

    if best_factor is not None:
        report += f"""
最佳预测因子: {best_factor['factor']}
- 预测准确率: {best_factor['prediction_accuracy']:.3f}
- 相关性强度: {best_factor['factor_strength']:.4f}
- 统计显著性: {'是' if best_factor['significant_direction'] else '否'}
"""

    if len(effective_factors) > 0:
        report += f"""
TOP5有效因子:
"""
        for i, (_, factor) in enumerate(effective_factors.head(5).iterrows(), 1):
            report += f"{i}. {factor['factor']}: 准确率 {factor['prediction_accuracy']:.3f}\n"

    if len(category_analysis) > 0:
        best_category = category_analysis.loc[category_analysis['avg_accuracy'].idxmax()]
        report += f"""
四、因子类别分析
最有效因子类别: {best_category['category']}
- 平均准确率: {best_category['avg_accuracy']:.3f}
- 有效率: {best_category['effectiveness_ratio']:.1%}
- 显著因子数: {int(best_category['significant_count'])}
"""

    if strategy_results is not None:
        report += f"""
五、多因子组合策略
组合因子: {', '.join(top_factors)}
组合策略准确率: {combined_accuracy:.3f}
相比最佳单因子提升: {combined_accuracy - best_factor['prediction_accuracy']:.3f}
"""

    report += f"""
六、投资策略建议

1. 核心交易策略:
   基于分析结果，建议采用以下量化择时策略:

   走阔信号 (买入30年国债，卖出10年国债):
"""

    if len(effective_factors) > 0:
        for _, factor in effective_factors.head(3).iterrows():
            if factor['corr_direction'] > 0:
                condition = f"{factor['factor']} > 历史中位数"
            else:
                condition = f"{factor['factor']} < 历史中位数"
            report += f"   - {condition}\n"

    report += f"""
   收窄信号 (买入10年国债，卖出30年国债):
   - 与走阔信号相反的条件

2. 风险管理建议:
   - 利差走阔是低概率事件({widen_prob*100:.1f}%)，需要谨慎判断
   - 建议多因子确认，避免单一因子误判
   - 设置合理的止损位和仓位管理
   - 定期重新评估因子有效性

3. 策略优化方向:
   - 加入更多宏观经济指标
   - 考虑政策事件对利差的影响
   - 引入机器学习模型提升预测精度
   - 建立动态因子权重调整机制

七、重要发现与结论

1. 利差变化的不对称性:
   收窄是常态({(1-widen_prob)*100:.1f}%)，走阔是异常事件({widen_prob*100:.1f}%)

2. 因子有效性分化明显:
   部分因子具有较强的预测能力，但整体有效因子比例有限

3. 多因子组合的价值:
   适当的因子组合可以提升预测稳定性

4. 实用性建议:
   该策略适合作为债券配置的辅助工具，需结合基本面分析使用
"""

    # 保存报告
    with open(f'{output_dir}/30-10y利差量化预测策略报告.txt', 'w', encoding='utf-8') as f:
        f.write(report)

    print(report)
    print(f"\n✓ 完整报告已保存到: {output_dir}/30-10y利差量化预测策略报告.txt")

    # 创建汇总Excel
    with pd.ExcelWriter(f'{output_dir}/量化择时策略完整分析.xlsx') as writer:
        factor_analysis.to_excel(writer, sheet_name='单因子分析', index=False)
        if len(category_analysis) > 0:
            category_analysis.to_excel(writer, sheet_name='因子类别分析', index=False)
        if len(effective_factors) > 0:
            effective_factors.to_excel(writer, sheet_name='有效因子', index=False)
        if strategy_results is not None:
            strategy_results.to_excel(writer, sheet_name='多因子策略结果', index=False)

    print(f"✓ 汇总Excel已保存到: {output_dir}/量化择时策略完整分析.xlsx")

generate_comprehensive_report(factor_analysis, category_analysis, strategy_results, top_factors, combined_accuracy, df_processed, output_dir)

print(f"\n" + "="*60)
print(f"30-10y利差量化预测策略分析完成")
print(f"="*60)
print(f"分析期间: 2024年1月2日 - 2025年5月23日")
print(f"样本数量: {len(df_processed)}个交易日")
print(f"分析因子: {len(factor_analysis)}个")
print(f"有效因子: {len(factor_analysis[factor_analysis['prediction_accuracy'] > 0.5])}个")
print(f"输出目录: {output_dir}")
print(f"="*60)

if len(factor_analysis) > 0:
    best_factor = factor_analysis.loc[factor_analysis['prediction_accuracy'].idxmax()]
    print(f"🏆 最佳因子: {best_factor['factor']}")
    print(f"📊 预测准确率: {best_factor['prediction_accuracy']:.3f}")
    print(f"📈 基准走阔概率: {df_processed['30-10y_direction'].mean():.3f}")

    if strategy_results is not None:
        print(f"🔗 多因子策略准确率: {combined_accuracy:.3f}")

print(f"="*60)
