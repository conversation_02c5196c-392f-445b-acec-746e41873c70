import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.model_selection import train_test_split, TimeSeriesSplit
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score, classification_report, confusion_matrix
from sklearn.preprocessing import StandardScaler
from scipy import stats
import warnings
import os
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.figsize'] = (12, 8)
plt.rcParams['savefig.dpi'] = 300

# 创建输出文件夹
output_dir = '/Users/<USER>/Desktop/量化择时'
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

print("=== 30-10y利差量化预测策略分析 ===")
print("作为固收量化专家，我将深度分析所有因子并构建预测模型")

# 加载数据
file_path = '/Users/<USER>/Desktop/量化择时0526.xlsx'
try:
    print("正在加载数据...")
    df = pd.read_excel(file_path)
    print(f"\n✓ 数据加载成功")
    print(f"数据形状: {df.shape}")
    print(f"数据时间范围: 第1行 到 第{len(df)}行")
    print("数据加载完成，开始分析...")
except Exception as e:
    print(f"✗ 数据加载失败: {e}")
    import traceback
    traceback.print_exc()
    exit()

# 显示列名
print(f"\n数据列名: {list(df.columns)}")

# 定义因子分类（根据您的最新分类）
factor_categories = {
    '基本面因子': ['水泥价格指数', '建材综合指数', '南华金属指数', '南华能化指数', '南华工业品指数'],
    '技术面因子': ['偏离度（偏离均线）', '偏离度（偏离MA+std）'],
    '资金面因子': ['R007', 'DR007', '成交量:DR007', '成交量:R007'],
    '权益市场因子': ['1/沪深300PE-10y'],
    '成交因子': ['30Y成交量']
}

# 所有预测因子
all_factors = []
for factors in factor_categories.values():
    all_factors.extend(factors)

# 目标变量
target_var = '30-10y'
target_30y = '30y'

print(f"\n因子分类:")
for category, factors in factor_categories.items():
    available_factors = [f for f in factors if f in df.columns]
    missing_factors = [f for f in factors if f not in df.columns]
    print(f"{category}: ")
    print(f"  可用因子: {available_factors}")
    if missing_factors:
        print(f"  缺失因子: {missing_factors}")

def preprocess_data(df):
    """数据预处理"""
    print("\n=== 数据预处理 ===")

    # 检查缺失值
    missing_stats = df.isnull().sum()
    print(f"缺失值统计:")
    for col, missing in missing_stats.items():
        if missing > 0:
            print(f"  {col}: {missing} ({missing/len(df)*100:.1f}%)")

    # 前向填充缺失值
    df_processed = df.fillna(method='ffill')

    # 计算目标变量的变化方向
    df_processed['30-10y_next'] = df_processed[target_var].shift(-1)  # 下一日利差
    df_processed['30-10y_change'] = df_processed['30-10y_next'] - df_processed[target_var]  # 利差变化
    df_processed['30-10y_direction'] = (df_processed['30-10y_change'] > 0).astype(int)  # 1=走阔, 0=收窄

    # 计算30y收益率变化
    df_processed['30y_next'] = df_processed[target_30y].shift(-1)
    df_processed['30y_change'] = df_processed['30y_next'] - df_processed[target_30y]
    df_processed['30y_direction'] = (df_processed['30y_change'] > 0).astype(int)

    # 删除最后一行（因为shift(-1)后为NaN）
    df_processed = df_processed[:-1]

    print(f"预处理后数据形状: {df_processed.shape}")
    print(f"利差走阔天数: {df_processed['30-10y_direction'].sum()}")
    print(f"利差收窄天数: {(1-df_processed['30-10y_direction']).sum()}")
    print(f"走阔概率: {df_processed['30-10y_direction'].mean():.3f}")

    return df_processed

df_processed = preprocess_data(df)

def analyze_single_factor_effectiveness(df, factors, target_direction='30-10y_direction', target_level='30-10y'):
    """分析单因子有效性"""
    print(f"\n=== 单因子有效性分析 ===")

    results = []

    for factor in factors:
        if factor not in df.columns:
            continue

        # 计算相关性
        corr_level = df[factor].corr(df[target_level])
        corr_change = df[factor].corr(df[target_direction])

        # 计算统计显著性
        try:
            _, p_value_level = stats.pearsonr(df[factor].dropna(), df[target_level].dropna())
            _, p_value_change = stats.pearsonr(df[factor].dropna(), df[target_direction].dropna())
        except:
            p_value_level = 1.0
            p_value_change = 1.0

        # 计算预测胜率（基于因子方向）
        # 将因子分为高低两组，看预测准确率
        factor_median = df[factor].median()
        high_factor = df[factor] > factor_median
        low_factor = df[factor] <= factor_median

        # 高因子值时的走阔概率
        high_prob = df[high_factor][target_direction].mean() if high_factor.sum() > 0 else 0.5
        low_prob = df[low_factor][target_direction].mean() if low_factor.sum() > 0 else 0.5

        # 简单预测策略：如果因子与目标正相关，高因子值预测走阔
        if corr_change > 0:
            predicted_high = 1  # 预测走阔
            predicted_low = 0   # 预测收窄
        else:
            predicted_high = 0  # 预测收窄
            predicted_low = 1   # 预测走阔

        # 计算预测准确率
        high_accuracy = (df[high_factor][target_direction] == predicted_high).mean() if high_factor.sum() > 0 else 0.5
        low_accuracy = (df[low_factor][target_direction] == predicted_low).mean() if low_factor.sum() > 0 else 0.5
        overall_accuracy = (high_accuracy * high_factor.sum() + low_accuracy * low_factor.sum()) / len(df)

        results.append({
            'factor': factor,
            'corr_level': corr_level,
            'corr_direction': corr_change,
            'p_value_level': p_value_level,
            'p_value_direction': p_value_change,
            'significant_level': p_value_level < 0.05,
            'significant_direction': p_value_change < 0.05,
            'high_prob': high_prob,
            'low_prob': low_prob,
            'prediction_accuracy': overall_accuracy,
            'factor_strength': abs(corr_change)
        })

        print(f"{factor}:")
        print(f"  与利差水平相关性: {corr_level:.4f} (p={p_value_level:.4f})")
        print(f"  与利差方向相关性: {corr_change:.4f} (p={p_value_change:.4f})")
        print(f"  预测准确率: {overall_accuracy:.3f}")
        print(f"  高因子值走阔概率: {high_prob:.3f}")
        print(f"  低因子值走阔概率: {low_prob:.3f}")

    return pd.DataFrame(results)

# 分析所有可用因子
available_factors = [f for f in all_factors if f in df_processed.columns]
factor_analysis = analyze_single_factor_effectiveness(df_processed, available_factors)

# 保存单因子分析结果
factor_analysis.to_excel(f'{output_dir}/单因子有效性分析.xlsx', index=False)
print(f"\n✓ 单因子分析结果已保存到: {output_dir}/单因子有效性分析.xlsx")

def plot_factor_effectiveness(factor_analysis, output_dir):
    """可视化因子有效性"""
    print(f"\n=== 因子有效性可视化 ===")

    # 按预测准确率排序
    factor_analysis_sorted = factor_analysis.sort_values('prediction_accuracy', ascending=False)

    # 1. 因子预测准确率图
    plt.figure(figsize=(14, 8))
    bars = plt.bar(range(len(factor_analysis_sorted)), factor_analysis_sorted['prediction_accuracy'])
    plt.axhline(y=0.5, color='red', linestyle='--', label='随机预测基准线(50%)')
    plt.xlabel('因子')
    plt.ylabel('预测准确率')
    plt.title('各因子预测30-10y利差方向的准确率')
    plt.xticks(range(len(factor_analysis_sorted)), factor_analysis_sorted['factor'], rotation=45, ha='right')
    plt.legend()
    plt.grid(True, alpha=0.3)

    # 标注准确率数值
    for i, (bar, acc) in enumerate(zip(bars, factor_analysis_sorted['prediction_accuracy'])):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005,
                f'{acc:.3f}', ha='center', va='bottom', fontsize=9)

    plt.tight_layout()
    plt.savefig(f'{output_dir}/因子预测准确率分析.png')
    plt.show()

    # 2. 因子相关性热力图
    plt.figure(figsize=(12, 8))
    corr_data = factor_analysis.set_index('factor')[['corr_level', 'corr_direction']]
    sns.heatmap(corr_data.T, annot=True, cmap='RdBu_r', center=0, fmt='.3f')
    plt.title('因子与30-10y利差的相关性')
    plt.ylabel('相关性类型')
    plt.xlabel('因子')
    plt.tight_layout()
    plt.savefig(f'{output_dir}/因子相关性热力图.png')
    plt.show()

    # 3. 因子强度vs准确率散点图
    plt.figure(figsize=(10, 8))
    scatter = plt.scatter(factor_analysis['factor_strength'], factor_analysis['prediction_accuracy'],
                         c=factor_analysis['significant_direction'], cmap='RdYlGn', s=100)
    plt.xlabel('因子强度 (|相关系数|)')
    plt.ylabel('预测准确率')
    plt.title('因子强度与预测准确率关系')
    plt.axhline(y=0.5, color='red', linestyle='--', alpha=0.5)
    plt.colorbar(scatter, label='统计显著性')

    # 添加因子名称标注
    for i, row in factor_analysis.iterrows():
        plt.annotate(row['factor'], (row['factor_strength'], row['prediction_accuracy']),
                    xytext=(5, 5), textcoords='offset points', fontsize=8, alpha=0.7)

    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/因子强度与准确率关系.png')
    plt.show()

plot_factor_effectiveness(factor_analysis, output_dir)

def build_factor_combination_models(df, factor_analysis, output_dir):
    """构建因子组合模型"""
    print(f"\n=== 因子组合模型构建 ===")

    # 选择有效因子（预测准确率>50%且统计显著）
    effective_factors = factor_analysis[
        (factor_analysis['prediction_accuracy'] > 0.5) &
        (factor_analysis['significant_direction'] == True)
    ]['factor'].tolist()

    print(f"有效因子数量: {len(effective_factors)}")
    print(f"有效因子: {effective_factors}")

    if len(effective_factors) == 0:
        print("警告: 没有找到有效因子，使用所有因子")
        effective_factors = factor_analysis['factor'].tolist()

    # 准备数据
    X = df[effective_factors].fillna(method='ffill')
    y = df['30-10y_direction']

    # 删除包含NaN的行
    valid_idx = ~(X.isnull().any(axis=1) | y.isnull())
    X = X[valid_idx]
    y = y[valid_idx]

    print(f"有效样本数量: {len(X)}")

    # 时间序列分割（避免未来信息泄露）
    tscv = TimeSeriesSplit(n_splits=5)

    # 定义模型
    models = {
        'Logistic Regression': LogisticRegression(random_state=42),
        'Random Forest': RandomForestClassifier(n_estimators=100, random_state=42),
        'Gradient Boosting': GradientBoostingClassifier(random_state=42)
    }

    # 模型评估结果
    model_results = []

    for model_name, model in models.items():
        print(f"\n训练模型: {model_name}")

        accuracies = []

        for train_idx, test_idx in tscv.split(X):
            X_train, X_test = X.iloc[train_idx], X.iloc[test_idx]
            y_train, y_test = y.iloc[train_idx], y.iloc[test_idx]

            # 标准化特征
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)

            # 训练模型
            model.fit(X_train_scaled, y_train)

            # 预测
            y_pred = model.predict(X_test_scaled)

            # 计算准确率
            accuracy = accuracy_score(y_test, y_pred)
            accuracies.append(accuracy)

        mean_accuracy = np.mean(accuracies)
        std_accuracy = np.std(accuracies)

        model_results.append({
            'model': model_name,
            'mean_accuracy': mean_accuracy,
            'std_accuracy': std_accuracy,
            'factor_count': len(effective_factors)
        })

        print(f"  平均准确率: {mean_accuracy:.3f} ± {std_accuracy:.3f}")

    # 保存模型结果
    model_results_df = pd.DataFrame(model_results)
    model_results_df.to_excel(f'{output_dir}/因子组合模型结果.xlsx', index=False)

    return model_results_df, effective_factors

model_results, effective_factors = build_factor_combination_models(df_processed, factor_analysis, output_dir)

def analyze_factor_categories(df, factor_categories, factor_analysis, output_dir):
    """按因子类别分析有效性"""
    print(f"\n=== 因子类别分析 ===")

    category_results = []

    for category, factors in factor_categories.items():
        available_factors = [f for f in factors if f in df.columns]
        if not available_factors:
            continue

        category_analysis = factor_analysis[factor_analysis['factor'].isin(available_factors)]

        avg_accuracy = category_analysis['prediction_accuracy'].mean()
        max_accuracy = category_analysis['prediction_accuracy'].max()
        significant_count = category_analysis['significant_direction'].sum()
        effective_count = (category_analysis['prediction_accuracy'] > 0.5).sum()

        category_results.append({
            'category': category,
            'factor_count': len(available_factors),
            'avg_accuracy': avg_accuracy,
            'max_accuracy': max_accuracy,
            'significant_count': significant_count,
            'effective_count': effective_count,
            'effectiveness_ratio': effective_count / len(available_factors) if available_factors else 0
        })

        print(f"{category}:")
        print(f"  因子数量: {len(available_factors)}")
        print(f"  平均准确率: {avg_accuracy:.3f}")
        print(f"  最高准确率: {max_accuracy:.3f}")
        print(f"  显著因子数: {significant_count}")
        print(f"  有效因子数: {effective_count}")
        print(f"  有效率: {effective_count/len(available_factors):.1%}")

    category_df = pd.DataFrame(category_results)
    category_df.to_excel(f'{output_dir}/因子类别分析.xlsx', index=False)

    # 可视化因子类别效果
    plt.figure(figsize=(12, 8))
    x = range(len(category_df))
    width = 0.35

    plt.bar([i - width/2 for i in x], category_df['avg_accuracy'], width,
            label='平均准确率', alpha=0.8)
    plt.bar([i + width/2 for i in x], category_df['effectiveness_ratio'], width,
            label='有效率', alpha=0.8)

    plt.axhline(y=0.5, color='red', linestyle='--', alpha=0.5, label='基准线')
    plt.xlabel('因子类别')
    plt.ylabel('比率')
    plt.title('各类别因子的预测效果')
    plt.xticks(x, category_df['category'], rotation=45, ha='right')
    plt.legend()
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/因子类别效果对比.png')
    plt.show()

    return category_df

category_analysis = analyze_factor_categories(df_processed, factor_categories, factor_analysis, output_dir)

def generate_final_strategy_report(factor_analysis, model_results, category_analysis, effective_factors, output_dir):
    """生成最终策略报告"""
    print(f"\n=== 最终策略报告 ===")

    # 找出最佳因子
    best_factors = factor_analysis.nlargest(5, 'prediction_accuracy')

    # 找出最佳模型
    best_model = model_results.loc[model_results['mean_accuracy'].idxmax()]

    # 生成报告
    report = f"""
30-10y利差量化预测策略分析报告
=====================================

一、数据概况
- 样本数量: {len(df_processed)}
- 因子总数: {len(factor_analysis)}
- 有效因子数: {len(effective_factors)}

二、单因子分析结果
最佳5个因子:
"""

    for i, (_, factor) in enumerate(best_factors.iterrows(), 1):
        report += f"{i}. {factor['factor']}: 准确率 {factor['prediction_accuracy']:.3f}\n"

    report += f"""
三、因子类别分析
最有效的因子类别: {category_analysis.loc[category_analysis['avg_accuracy'].idxmax(), 'category']}
平均准确率: {category_analysis['avg_accuracy'].max():.3f}

四、模型组合结果
最佳模型: {best_model['model']}
预测准确率: {best_model['mean_accuracy']:.3f} ± {best_model['std_accuracy']:.3f}

五、投资建议
基于分析结果，建议重点关注以下因子进行30-10y利差方向预测:
"""

    for factor in effective_factors[:3]:  # 显示前3个有效因子
        factor_info = factor_analysis[factor_analysis['factor'] == factor].iloc[0]
        report += f"- {factor}: 准确率 {factor_info['prediction_accuracy']:.3f}\n"

    report += f"""
六、风险提示
- 模型基于历史数据，未来表现可能有差异
- 建议结合基本面分析和市场环境判断
- 定期重新训练模型以适应市场变化
"""

    # 保存报告
    with open(f'{output_dir}/策略分析报告.txt', 'w', encoding='utf-8') as f:
        f.write(report)

    print(report)
    print(f"\n✓ 完整报告已保存到: {output_dir}/策略分析报告.txt")

    # 创建汇总Excel
    with pd.ExcelWriter(f'{output_dir}/量化择时策略汇总.xlsx') as writer:
        factor_analysis.to_excel(writer, sheet_name='单因子分析', index=False)
        model_results.to_excel(writer, sheet_name='模型结果', index=False)
        category_analysis.to_excel(writer, sheet_name='因子类别分析', index=False)

        # 添加最佳因子汇总
        best_factors.to_excel(writer, sheet_name='最佳因子TOP5', index=False)

    print(f"✓ 汇总Excel已保存到: {output_dir}/量化择时策略汇总.xlsx")

generate_final_strategy_report(factor_analysis, model_results, category_analysis, effective_factors, output_dir)

print(f"\n=== 分析完成 ===")
print(f"所有结果已保存到: {output_dir}")
print(f"主要输出文件:")
print(f"- 单因子有效性分析.xlsx")
print(f"- 因子组合模型结果.xlsx")
print(f"- 因子类别分析.xlsx")
print(f"- 量化择时策略汇总.xlsx")
print(f"- 策略分析报告.txt")
print(f"- 各类分析图表.png")
