"""
Tencent PCG Sample Data Generator
---------------------------------
Creates sample datasets for testing the analysis system
"""

import pandas as pd
import numpy as np
from datetime import date, timedelta

def generate_user_activity(num_users=1000, days=30):
    """Generate user activity data"""
    user_ids = [f"user_{i}" for i in range(1, num_users+1)]
    dates = [date.today() - timedelta(days=i) for i in range(days)]
    
    records = []
    for d in dates:
        daily_users = np.random.choice(user_ids, size=np.random.randint(800, 1000), replace=False)
        for user in daily_users:
            records.append({
                "date": d.isoformat(),
                "user_id": user,
                "session_id": f"{user}_{d.isoformat()}",
                "session_duration": np.random.randint(60, 1800),
                "action": np.random.choice(['search', 'play', 'next', 'engagement', 'exit'], p=[0.2, 0.3, 0.2, 0.2, 0.1]),
                "like_count": np.random.poisson(0.5),
                "comment_count": np.random.poisson(0.3),
                "share_count": np.random.poisson(0.2),
                "new_user_flag": 1 if np.random.random() < 0.05 else 0,
                "view_duration": np.random.randint(10, 600),
                "content_duration": np.random.randint(30, 1200),
                "content_id": f"content_{np.random.randint(1, 100)}",
                "action_count": np.random.randint(1, 20)
            })
    
    return pd.DataFrame(records)

def generate_user_profiles(num_users=1000):
    """Generate user profile data"""
    user_ids = [f"user_{i}" for i in range(1, num_users+1)]
    
    profiles = []
    for user in user_ids:
        profiles.append({
            "user_id": user,
            "age_group": np.random.choice(['18-25', '26-35', '36-45', '46+'], p=[0.3, 0.4, 0.2, 0.1]),
            "region": np.random.choice(['North', 'South', 'East', 'West'], p=[0.3, 0.3, 0.2, 0.2]),
            "interests": np.random.choice(['sports', 'entertainment', 'news', 'education'], p=[0.4, 0.3, 0.2, 0.1]),
            "danmaku_interaction": np.random.randint(0, 10),
            "social_feature_usage": np.random.randint(0, 10)
        })
    
    return pd.DataFrame(profiles)

if __name__ == "__main__":
    # Create sample data
    user_activity = generate_user_activity()
    user_profiles = generate_user_profiles()
    
    # Save to data directory
    today = date.today().isoformat()
    user_activity.to_csv(f"data/raw/user_activity_{today}.csv", index=False)
    user_profiles.to_csv("data/reference/user_profiles.csv", index=False)
    
    print(f"Generated sample data with {len(user_activity)} activity records")
    print(f"and {len(user_profiles)} user profiles")
