import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
from scipy.optimize import minimize
import warnings
import os
from itertools import combinations, product
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score
from sklearn.preprocessing import StandardScaler
warnings.filterwarnings('ignore')

# 设置中文字体和样式
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.figsize'] = (14, 10)
plt.rcParams['savefig.dpi'] = 300
sns.set_style("whitegrid")
sns.set_palette("husl")

# 创建输出文件夹
output_dir = '/Users/<USER>/Desktop/量化择时胜率'
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

print("=" * 100)
print("🚀 30-10y利差终极因子工程与策略优化")
print("调集所有算力进行深度分析")
print("=" * 100)

# 加载数据
file_path = '/Users/<USER>/Desktop/择时胜率.xlsx'
df = pd.read_excel(file_path)

# 数据预处理
df_processed = df.fillna(method='ffill').fillna(method='bfill').copy()
if 'Unnamed: 0' in df_processed.columns:
    df_processed = df_processed.rename(columns={'Unnamed: 0': '日期'})

# 构建目标变量
df_processed['30-10y_next'] = df_processed['30-10y'].shift(-1)
df_processed['30-10y_change'] = df_processed['30-10y_next'] - df_processed['30-10y']
df_processed['direction_next'] = (df_processed['30-10y_change'] > 0).astype(int)
df_processed = df_processed[:-1].copy()

# 原始因子
original_factors = {
    '基本面因子': ['水泥价格指数', '建材综合指数', '南华金属指数', '南华能化指数', '南华工业品指数'],
    '技术面因子': ['偏离度（偏离均线）', '偏离度（偏离MA+std）'],
    '资金面因子': ['R007', 'DR007', '成交量:DR007', '成交量:R007'],
    '权益市场因子': ['1/沪深300PE-10y'],
    '成交因子': ['30Y成交量']
}

all_original_factors = []
for factors in original_factors.values():
    all_original_factors.extend(factors)

available_original_factors = [f for f in all_original_factors if f in df_processed.columns]

print(f"原始因子数量: {len(available_original_factors)}")
print(f"数据列名: {list(df_processed.columns)}")
print(f"基准走阔概率: {df_processed['direction_next'].mean():.3f}")

def create_engineered_factors(df):
    """创造新的工程因子"""
    print(f"\n{'='*60}")
    print("🔬 创造新的工程因子")
    print(f"{'='*60}")

    # 1. 动量因子
    print("创建动量因子...")
    df['利差动量_5日'] = df['30-10y'].pct_change(5)
    df['利差动量_10日'] = df['30-10y'].pct_change(10)
    df['利差动量_20日'] = df['30-10y'].pct_change(20)

    # 2. 波动率因子
    print("创建波动率因子...")
    df['利差波动率_5日'] = df['30-10y'].rolling(5).std()
    df['利差波动率_10日'] = df['30-10y'].rolling(10).std()
    df['利差波动率_20日'] = df['30-10y'].rolling(20).std()

    # 3. 相对强弱因子
    print("创建相对强弱因子...")
    if '30y' in df.columns and '10y' in df.columns:
        df['30y相对强弱'] = df['30y'] / df['10y']
        df['30y相对强弱_MA5'] = df['30y相对强弱'].rolling(5).mean()
        df['30y相对强弱偏离'] = df['30y相对强弱'] - df['30y相对强弱_MA5']
    elif '30y' in df.columns:
        # 如果没有10y，使用30y的相对变化
        df['30y相对变化'] = df['30y'].pct_change(5)
        df['30y相对强弱偏离'] = df['30y相对变化'] - df['30y相对变化'].rolling(10).mean()

    # 4. 复合基本面因子
    print("创建复合基本面因子...")
    commodity_factors = ['南华金属指数', '南华能化指数', '南华工业品指数']
    available_commodity = [f for f in commodity_factors if f in df.columns]
    if len(available_commodity) >= 2:
        df['商品综合指数'] = df[available_commodity].mean(axis=1)
        df['商品动量'] = df['商品综合指数'].pct_change(5)

    # 5. 资金面复合因子
    print("创建资金面复合因子...")
    if 'R007' in df.columns and 'DR007' in df.columns:
        df['资金面利差'] = df['R007'] - df['DR007']
        df['资金面利差_MA5'] = df['资金面利差'].rolling(5).mean()
        df['资金面利差偏离'] = df['资金面利差'] - df['资金面利差_MA5']

    # 6. 成交量相关因子
    print("创建成交量因子...")
    volume_factors = ['成交量:DR007', '成交量:R007', '30Y成交量']
    available_volume = [f for f in volume_factors if f in df.columns]
    if len(available_volume) >= 2:
        df['成交量综合'] = df[available_volume].mean(axis=1)
        df['成交量动量'] = df['成交量综合'].pct_change(5)

    # 7. 技术面增强因子
    print("创建技术面增强因子...")
    if '偏离度（偏离均线）' in df.columns:
        df['偏离度动量'] = df['偏离度（偏离均线）'].diff(1)
        df['偏离度加速度'] = df['偏离度动量'].diff(1)
        df['偏离度绝对值'] = abs(df['偏离度（偏离均线）'])

    # 8. 趋势强度因子
    print("创建趋势强度因子...")
    df['利差趋势强度_5日'] = df['30-10y'].rolling(5).apply(lambda x: 1 if x.iloc[-1] > x.iloc[0] else 0)
    df['利差趋势强度_10日'] = df['30-10y'].rolling(10).apply(lambda x: 1 if x.iloc[-1] > x.iloc[0] else 0)

    # 9. 市场状态因子
    print("创建市场状态因子...")
    df['高波动状态'] = (df['利差波动率_10日'] > df['利差波动率_10日'].rolling(20).mean()).astype(int)
    df['极端偏离状态'] = (abs(df['偏离度（偏离均线）']) > df['偏离度绝对值'].rolling(20).quantile(0.8)).astype(int)

    # 10. 交互因子
    print("创建交互因子...")
    if '水泥价格指数' in df.columns and '偏离度（偏离均线）' in df.columns:
        df['基本面技术面交互'] = df['水泥价格指数'] * df['偏离度（偏离均线）']

    if 'R007' in df.columns and '30Y成交量' in df.columns:
        df['资金面成交交互'] = df['R007'] * df['30Y成交量']

    # 删除包含NaN的行
    df = df.dropna()

    # 统计新创建的因子
    new_factors = [col for col in df.columns if col not in available_original_factors +
                   ['30-10y', '10y', '30y', '日期', '30-10y_next', '30-10y_change', 'direction_next']]

    print(f"✅ 成功创建 {len(new_factors)} 个新因子:")
    for i, factor in enumerate(new_factors, 1):
        print(f"  {i}. {factor}")

    return df, new_factors

df_engineered, new_factors = create_engineered_factors(df_processed)

def analyze_all_factors_performance(df, original_factors, new_factors):
    """分析所有因子的表现"""
    print(f"\n{'='*60}")
    print("📊 全因子表现分析")
    print(f"{'='*60}")

    all_factors = original_factors + new_factors
    factor_performance = []

    for factor in all_factors:
        if factor not in df.columns:
            continue

        # 计算基本统计
        factor_data = df[factor]
        target_data = df['direction_next']

        # 相关性分析
        correlation = factor_data.corr(target_data)
        _, p_value = stats.pearsonr(factor_data, target_data)

        # 分位数分析
        q25 = factor_data.quantile(0.25)
        q50 = factor_data.quantile(0.50)
        q75 = factor_data.quantile(0.75)

        # 各分位数组的走阔概率
        low_group = df[factor_data <= q25]['direction_next'].mean()
        mid_low_group = df[(factor_data > q25) & (factor_data <= q50)]['direction_next'].mean()
        mid_high_group = df[(factor_data > q50) & (factor_data <= q75)]['direction_next'].mean()
        high_group = df[factor_data > q75]['direction_next'].mean()

        # 计算预测能力
        groups = [low_group, mid_low_group, mid_high_group, high_group]
        max_prob = max(groups)
        min_prob = min(groups)
        spread_range = max_prob - min_prob

        # 计算单因子胜率
        if correlation > 0:
            prediction = (factor_data > q50).astype(int)
        else:
            prediction = (factor_data <= q50).astype(int)

        win_rate = (prediction == target_data).mean()

        # 因子类型
        if factor in original_factors:
            factor_type = '原始因子'
        else:
            factor_type = '工程因子'

        factor_performance.append({
            'factor': factor,
            'factor_type': factor_type,
            'correlation': correlation,
            'p_value': p_value,
            'significant': p_value < 0.05,
            'win_rate': win_rate,
            'spread_range': spread_range,
            'low_group_prob': low_group,
            'mid_low_prob': mid_low_group,
            'mid_high_prob': mid_high_group,
            'high_group_prob': high_group,
            'factor_strength': abs(correlation),
            'information_ratio': abs(correlation) / (p_value + 0.001)  # 信息比率
        })

    factor_df = pd.DataFrame(factor_performance)

    # 按胜率排序
    factor_df = factor_df.sort_values('win_rate', ascending=False)

    print(f"总因子数量: {len(factor_df)}")
    print(f"原始因子数量: {len(factor_df[factor_df['factor_type'] == '原始因子'])}")
    print(f"工程因子数量: {len(factor_df[factor_df['factor_type'] == '工程因子'])}")

    # TOP10因子
    print(f"\nTOP10因子表现:")
    top10 = factor_df.head(10)
    for i, (_, row) in enumerate(top10.iterrows(), 1):
        print(f"  {i}. {row['factor']} ({row['factor_type']}): 胜率 {row['win_rate']:.3f}")

    return factor_df

all_factor_performance = analyze_all_factors_performance(df_engineered, available_original_factors, new_factors)

def optimize_factor_weights(df, factor_list, target='direction_next', method='genetic'):
    """优化因子权重"""
    print(f"\n{'='*60}")
    print("🎯 因子权重优化")
    print(f"{'='*60}")

    # 准备数据
    X = df[factor_list].fillna(method='ffill')
    y = df[target]

    # 标准化
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X)

    def objective_function(weights):
        """目标函数：最大化预测准确率"""
        weights = np.array(weights)
        weights = weights / np.sum(weights)  # 归一化权重

        # 计算加权信号
        signals = np.zeros(len(X_scaled))
        for i, factor in enumerate(factor_list):
            factor_median = df[factor].median()
            factor_corr = df[factor].corr(y)

            if factor_corr > 0:
                factor_signal = (df[factor] > factor_median).astype(int)
            else:
                factor_signal = (df[factor] <= factor_median).astype(int)

            signals += factor_signal * weights[i]

        # 预测
        predictions = (signals > 0.5).astype(int)
        accuracy = (predictions == y).mean()

        return -accuracy  # 最小化负准确率

    # 权重约束
    n_factors = len(factor_list)
    bounds = [(0.01, 1.0) for _ in range(n_factors)]  # 每个权重在0.01到1之间
    constraints = {'type': 'eq', 'fun': lambda x: np.sum(x) - 1}  # 权重和为1

    # 初始权重
    initial_weights = np.ones(n_factors) / n_factors

    # 优化
    print(f"正在优化 {n_factors} 个因子的权重...")
    result = minimize(objective_function, initial_weights, method='SLSQP',
                     bounds=bounds, constraints=constraints)

    optimal_weights = result.x / np.sum(result.x)  # 确保归一化
    optimal_accuracy = -result.fun

    print(f"优化完成！最优准确率: {optimal_accuracy:.4f}")

    # 返回结果
    weight_results = []
    for i, factor in enumerate(factor_list):
        weight_results.append({
            'factor': factor,
            'optimal_weight': optimal_weights[i],
            'weight_rank': i + 1
        })

    return pd.DataFrame(weight_results), optimal_accuracy

def build_advanced_strategies(df, factor_performance, output_dir):
    """构建高级策略"""
    print(f"\n{'='*60}")
    print("🚀 构建高级因子组合策略")
    print(f"{'='*60}")

    # 选择TOP因子
    top_factors = factor_performance.head(15)['factor'].tolist()
    print(f"选择TOP15因子进行策略构建: {len(top_factors)}个")

    strategies_results = []

    # 策略1: 等权重投票策略
    print(f"\n策略1: 等权重投票策略")
    equal_weight_signals = pd.DataFrame(index=df.index)

    for factor in top_factors[:10]:  # 使用TOP10
        if factor not in df.columns:
            continue
        factor_corr = df[factor].corr(df['direction_next'])
        factor_median = df[factor].median()

        if factor_corr > 0:
            equal_weight_signals[f'{factor}_signal'] = (df[factor] > factor_median).astype(int)
        else:
            equal_weight_signals[f'{factor}_signal'] = (df[factor] <= factor_median).astype(int)

    equal_weight_signals['vote_sum'] = equal_weight_signals.sum(axis=1)
    equal_weight_signals['prediction'] = (equal_weight_signals['vote_sum'] > len(top_factors[:10]) / 2).astype(int)
    equal_weight_accuracy = (equal_weight_signals['prediction'] == df['direction_next']).mean()

    strategies_results.append({
        'strategy_name': '等权重投票策略',
        'factors_used': ', '.join(top_factors[:10]),
        'accuracy': equal_weight_accuracy,
        'factor_count': 10,
        'strategy_type': '传统策略'
    })

    print(f"  等权重投票策略准确率: {equal_weight_accuracy:.4f}")

    # 策略2: 胜率加权策略
    print(f"\n策略2: 胜率加权策略")
    weighted_signals = pd.DataFrame(index=df.index)

    total_weight = 0
    for factor in top_factors[:10]:
        if factor not in df.columns:
            continue
        factor_stats = factor_performance[factor_performance['factor'] == factor].iloc[0]
        factor_corr = factor_stats['correlation']
        factor_median = df[factor].median()
        weight = factor_stats['win_rate']

        if factor_corr > 0:
            signal = (df[factor] > factor_median).astype(int)
        else:
            signal = (df[factor] <= factor_median).astype(int)

        weighted_signals[f'{factor}_weighted'] = signal * weight
        total_weight += weight

    weighted_signals['weighted_sum'] = weighted_signals.sum(axis=1)
    weighted_signals['prediction'] = (weighted_signals['weighted_sum'] > total_weight / 2).astype(int)
    weighted_accuracy = (weighted_signals['prediction'] == df['direction_next']).mean()

    strategies_results.append({
        'strategy_name': '胜率加权策略',
        'factors_used': ', '.join(top_factors[:10]),
        'accuracy': weighted_accuracy,
        'factor_count': 10,
        'strategy_type': '加权策略'
    })

    print(f"  胜率加权策略准确率: {weighted_accuracy:.4f}")

    # 策略3: 优化权重策略
    print(f"\n策略3: 优化权重策略")
    try:
        weight_results, optimized_accuracy = optimize_factor_weights(df, top_factors[:8])

        strategies_results.append({
            'strategy_name': '优化权重策略',
            'factors_used': ', '.join(top_factors[:8]),
            'accuracy': optimized_accuracy,
            'factor_count': 8,
            'strategy_type': '优化策略'
        })

        print(f"  优化权重策略准确率: {optimized_accuracy:.4f}")
    except Exception as e:
        print(f"  优化权重策略失败: {e}")
        weight_results = pd.DataFrame()
        optimized_accuracy = 0

    # 策略4: 分层权重策略（技术面主导）
    print(f"\n策略4: 分层权重策略")

    # 分类因子
    tech_factors = [f for f in top_factors if '偏离' in f or '动量' in f or '趋势' in f][:3]
    volume_factors = [f for f in top_factors if '成交量' in f or '30Y成交量' in f][:2]
    fundamental_factors = [f for f in top_factors if any(x in f for x in ['水泥', '建材', '南华', '商品'])][:3]
    other_factors = [f for f in top_factors if f not in tech_factors + volume_factors + fundamental_factors][:2]

    layered_signals = pd.DataFrame(index=df.index)

    # 技术面权重3
    tech_score = 0
    for factor in tech_factors:
        if factor in df.columns:
            factor_corr = df[factor].corr(df['direction_next'])
            factor_median = df[factor].median()

            if factor_corr > 0:
                signal = (df[factor] > factor_median).astype(int)
            else:
                signal = (df[factor] <= factor_median).astype(int)

            tech_score += signal * 3

    # 成交量权重2
    volume_score = 0
    for factor in volume_factors:
        if factor in df.columns:
            factor_corr = df[factor].corr(df['direction_next'])
            factor_median = df[factor].median()

            if factor_corr > 0:
                signal = (df[factor] > factor_median).astype(int)
            else:
                signal = (df[factor] <= factor_median).astype(int)

            volume_score += signal * 2

    # 基本面权重1
    fundamental_score = 0
    for factor in fundamental_factors:
        if factor in df.columns:
            factor_corr = df[factor].corr(df['direction_next'])
            factor_median = df[factor].median()

            if factor_corr > 0:
                signal = (df[factor] > factor_median).astype(int)
            else:
                signal = (df[factor] <= factor_median).astype(int)

            fundamental_score += signal * 1

    total_score = tech_score + volume_score + fundamental_score
    max_score = len(tech_factors) * 3 + len(volume_factors) * 2 + len(fundamental_factors) * 1

    layered_signals['prediction'] = (total_score > max_score / 2).astype(int)
    layered_accuracy = (layered_signals['prediction'] == df['direction_next']).mean()

    strategies_results.append({
        'strategy_name': '分层权重策略',
        'factors_used': f'技术面({len(tech_factors)}): {tech_factors}; 成交量({len(volume_factors)}): {volume_factors}; 基本面({len(fundamental_factors)}): {fundamental_factors}',
        'accuracy': layered_accuracy,
        'factor_count': len(tech_factors) + len(volume_factors) + len(fundamental_factors),
        'strategy_type': '分层策略'
    })

    print(f"  分层权重策略准确率: {layered_accuracy:.4f}")

    # 策略5: 动态阈值策略
    print(f"\n策略5: 动态阈值策略")

    # 基于市场波动性调整阈值
    volatility = df['30-10y'].rolling(10).std()
    high_vol_periods = volatility > volatility.median()

    dynamic_prediction = layered_signals['prediction'].copy()

    # 在高波动期，需要更强的信号确认
    high_vol_threshold = max_score * 0.6
    low_vol_threshold = max_score * 0.4

    dynamic_prediction[high_vol_periods] = (total_score[high_vol_periods] > high_vol_threshold).astype(int)
    dynamic_prediction[~high_vol_periods] = (total_score[~high_vol_periods] > low_vol_threshold).astype(int)

    dynamic_accuracy = (dynamic_prediction == df['direction_next']).mean()

    strategies_results.append({
        'strategy_name': '动态阈值策略',
        'factors_used': '基于分层权重策略，根据市场波动性动态调整阈值',
        'accuracy': dynamic_accuracy,
        'factor_count': len(tech_factors) + len(volume_factors) + len(fundamental_factors),
        'strategy_type': '动态策略'
    })

    print(f"  动态阈值策略准确率: {dynamic_accuracy:.4f}")

    # 策略6: 机器学习增强策略
    print(f"\n策略6: 机器学习增强策略")

    # 准备特征
    feature_factors = top_factors[:12]
    X = df[feature_factors].fillna(method='ffill')
    y = df['direction_next']

    # 时间序列分割
    train_size = int(len(X) * 0.7)
    X_train, X_test = X[:train_size], X[train_size:]
    y_train, y_test = y[:train_size], y[train_size:]

    # 随机森林
    rf_model = RandomForestClassifier(n_estimators=200, max_depth=10, random_state=42)
    rf_model.fit(X_train, y_train)
    rf_pred = rf_model.predict(X_test)
    rf_accuracy = accuracy_score(y_test, rf_pred)

    strategies_results.append({
        'strategy_name': '随机森林策略',
        'factors_used': ', '.join(feature_factors),
        'accuracy': rf_accuracy,
        'factor_count': len(feature_factors),
        'strategy_type': '机器学习'
    })

    print(f"  随机森林策略准确率: {rf_accuracy:.4f}")

    # 逻辑回归
    lr_model = LogisticRegression(random_state=42, max_iter=1000)
    lr_model.fit(X_train, y_train)
    lr_pred = lr_model.predict(X_test)
    lr_accuracy = accuracy_score(y_test, lr_pred)

    strategies_results.append({
        'strategy_name': '逻辑回归策略',
        'factors_used': ', '.join(feature_factors),
        'accuracy': lr_accuracy,
        'factor_count': len(feature_factors),
        'strategy_type': '机器学习'
    })

    print(f"  逻辑回归策略准确率: {lr_accuracy:.4f}")

    return pd.DataFrame(strategies_results), weight_results

strategies_df, weight_optimization = build_advanced_strategies(df_engineered, all_factor_performance, output_dir)

def create_beautiful_visualizations(all_factor_performance, strategies_df, df, output_dir):
    """创建美观的可视化图表"""
    print(f"\n{'='*60}")
    print("🎨 创建美观的可视化图表")
    print(f"{'='*60}")

    # 设置美观的颜色主题
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F']

    # 1. 因子胜率排名图（美化版）
    plt.figure(figsize=(16, 10))

    top20_factors = all_factor_performance.head(20)

    # 创建渐变色
    factor_colors = []
    for i, row in top20_factors.iterrows():
        if row['factor_type'] == '工程因子':
            factor_colors.append('#FF6B6B')  # 红色系 - 工程因子
        else:
            factor_colors.append('#4ECDC4')  # 蓝绿色系 - 原始因子

    bars = plt.bar(range(len(top20_factors)), top20_factors['win_rate'],
                   color=factor_colors, alpha=0.8, edgecolor='white', linewidth=1.5)

    # 添加基准线
    plt.axhline(y=0.5, color='#2C3E50', linestyle='--', linewidth=2, alpha=0.7, label='随机基准线(50%)')
    baseline = df['direction_next'].mean()
    plt.axhline(y=baseline, color='#E74C3C', linestyle='--', linewidth=2, alpha=0.7,
                label=f'基准走阔概率({baseline:.3f})')

    # 美化标签
    plt.xlabel('因子排名', fontsize=14, fontweight='bold')
    plt.ylabel('预测胜率', fontsize=14, fontweight='bold')
    plt.title('TOP20因子预测胜率排名\n🔴 工程因子  🔵 原始因子', fontsize=16, fontweight='bold', pad=20)

    # 设置x轴标签
    factor_labels = []
    for i, (_, row) in enumerate(top20_factors.iterrows()):
        label = row['factor']
        if len(label) > 15:
            label = label[:12] + '...'
        factor_labels.append(f"{i+1}.\n{label}")

    plt.xticks(range(len(top20_factors)), factor_labels, rotation=45, ha='right', fontsize=10)

    # 添加数值标签
    for i, (bar, rate) in enumerate(zip(bars, top20_factors['win_rate'])):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.005,
                f'{rate:.3f}', ha='center', va='bottom', fontweight='bold', fontsize=9)

    plt.legend(loc='upper right', fontsize=12)
    plt.grid(True, alpha=0.3, axis='y')
    plt.tight_layout()
    plt.savefig(f'{output_dir}/TOP20因子胜率排名_美化版.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 2. 策略胜率对比图（美化版）
    plt.figure(figsize=(14, 10))

    strategies_sorted = strategies_df.sort_values('accuracy', ascending=False)

    # 根据策略类型设置颜色
    strategy_colors = []
    for strategy_type in strategies_sorted['strategy_type']:
        if strategy_type == '传统策略':
            strategy_colors.append('#3498DB')
        elif strategy_type == '加权策略':
            strategy_colors.append('#E74C3C')
        elif strategy_type == '优化策略':
            strategy_colors.append('#2ECC71')
        elif strategy_type == '分层策略':
            strategy_colors.append('#F39C12')
        elif strategy_type == '动态策略':
            strategy_colors.append('#9B59B6')
        else:  # 机器学习
            strategy_colors.append('#1ABC9C')

    bars = plt.bar(range(len(strategies_sorted)), strategies_sorted['accuracy'],
                   color=strategy_colors, alpha=0.8, edgecolor='white', linewidth=2)

    # 添加基准线
    plt.axhline(y=0.5, color='#2C3E50', linestyle='--', linewidth=2, alpha=0.7, label='随机基准线')
    plt.axhline(y=baseline, color='#E74C3C', linestyle='--', linewidth=2, alpha=0.7,
                label=f'基准走阔概率({baseline:.3f})')

    # 美化标签
    plt.xlabel('策略类型', fontsize=14, fontweight='bold')
    plt.ylabel('预测胜率', fontsize=14, fontweight='bold')
    plt.title('因子组合策略胜率对比\n🔵传统 🔴加权 🟢优化 🟠分层 🟣动态 🟦机器学习',
              fontsize=16, fontweight='bold', pad=20)

    # 设置x轴标签
    strategy_labels = []
    for name in strategies_sorted['strategy_name']:
        if len(name) > 8:
            name = name.replace('策略', '')
        strategy_labels.append(name)

    plt.xticks(range(len(strategies_sorted)), strategy_labels, rotation=45, ha='right', fontsize=11)

    # 添加数值标签
    for i, (bar, acc) in enumerate(zip(bars, strategies_sorted['accuracy'])):
        plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.002,
                f'{acc:.4f}', ha='center', va='bottom', fontweight='bold', fontsize=10)

    plt.legend(loc='upper right', fontsize=12)
    plt.grid(True, alpha=0.3, axis='y')
    plt.ylim(0.48, max(strategies_sorted['accuracy']) + 0.02)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/策略胜率对比_美化版.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 3. 因子类型分布饼图
    plt.figure(figsize=(12, 8))

    factor_type_counts = all_factor_performance['factor_type'].value_counts()
    colors_pie = ['#FF6B6B', '#4ECDC4']

    plt.pie(factor_type_counts.values, labels=factor_type_counts.index, autopct='%1.1f%%',
            colors=colors_pie, startangle=90, textprops={'fontsize': 12, 'fontweight': 'bold'})
    plt.title('因子类型分布', fontsize=16, fontweight='bold', pad=20)
    plt.axis('equal')
    plt.savefig(f'{output_dir}/因子类型分布.png', dpi=300, bbox_inches='tight')
    plt.show()

    # 4. 因子胜率vs相关性散点图
    plt.figure(figsize=(12, 8))

    # 分别绘制原始因子和工程因子
    original_factors_data = all_factor_performance[all_factor_performance['factor_type'] == '原始因子']
    engineered_factors_data = all_factor_performance[all_factor_performance['factor_type'] == '工程因子']

    plt.scatter(original_factors_data['factor_strength'], original_factors_data['win_rate'],
                c='#4ECDC4', s=100, alpha=0.7, label='原始因子', edgecolors='white', linewidth=1)
    plt.scatter(engineered_factors_data['factor_strength'], engineered_factors_data['win_rate'],
                c='#FF6B6B', s=100, alpha=0.7, label='工程因子', edgecolors='white', linewidth=1)

    plt.axhline(y=0.5, color='#2C3E50', linestyle='--', alpha=0.5, label='随机基准')
    plt.axvline(x=0.05, color='#E74C3C', linestyle='--', alpha=0.5, label='显著性阈值')

    plt.xlabel('因子强度 (|相关系数|)', fontsize=12, fontweight='bold')
    plt.ylabel('预测胜率', fontsize=12, fontweight='bold')
    plt.title('因子强度与预测胜率关系', fontsize=14, fontweight='bold')
    plt.legend(fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.savefig(f'{output_dir}/因子强度与胜率关系.png', dpi=300, bbox_inches='tight')
    plt.show()

create_beautiful_visualizations(all_factor_performance, strategies_df, df_engineered, output_dir)

def save_comprehensive_excel_reports(all_factor_performance, strategies_df, weight_optimization, df, output_dir):
    """保存综合Excel报告"""
    print(f"\n{'='*60}")
    print("📊 保存综合Excel报告")
    print(f"{'='*60}")

    # 1. 主报告文件
    with pd.ExcelWriter(f'{output_dir}/终极因子组合策略分析报告.xlsx') as writer:

        # 因子表现分析
        all_factor_performance.to_excel(writer, sheet_name='全因子表现分析', index=False)

        # 策略对比
        strategies_df.to_excel(writer, sheet_name='策略胜率对比', index=False)

        # 权重优化结果
        if not weight_optimization.empty:
            weight_optimization.to_excel(writer, sheet_name='权重优化结果', index=False)

        # TOP因子详细分析
        top_factors = all_factor_performance.head(10)
        top_factors.to_excel(writer, sheet_name='TOP10因子详细分析', index=False)

        # 工程因子vs原始因子对比
        factor_comparison = all_factor_performance.groupby('factor_type').agg({
            'win_rate': ['mean', 'max', 'min', 'std'],
            'factor_strength': ['mean', 'max'],
            'significant': 'sum'
        }).round(4)
        factor_comparison.to_excel(writer, sheet_name='因子类型对比')

        # 策略构建详情
        strategy_details = pd.DataFrame([
            {
                'strategy_name': '等权重投票策略',
                'description': '对TOP10因子进行等权重投票，超过半数则预测走阔',
                'weight_scheme': '每个因子权重 = 1/10 = 0.1',
                'decision_rule': '投票数 > 5 → 预测走阔',
                '适用场景': '因子表现相近时使用'
            },
            {
                'strategy_name': '胜率加权策略',
                'description': '根据各因子历史胜率分配权重',
                'weight_scheme': '因子权重 = 该因子胜率',
                'decision_rule': '加权得分 > 总权重/2 → 预测走阔',
                '适用场景': '因子表现差异明显时使用'
            },
            {
                'strategy_name': '分层权重策略',
                'description': '技术面因子权重3，成交量因子权重2，基本面因子权重1',
                'weight_scheme': '技术面×3 + 成交量×2 + 基本面×1',
                'decision_rule': '加权得分 > 总权重/2 → 预测走阔',
                '适用场景': '强调技术面分析的策略'
            },
            {
                'strategy_name': '动态阈值策略',
                'description': '根据市场波动性动态调整决策阈值',
                'weight_scheme': '基于分层权重，高波动期阈值提高到60%',
                'decision_rule': '高波动期需要更强信号确认',
                '适用场景': '市场波动较大时期'
            }
        ])
        strategy_details.to_excel(writer, sheet_name='策略构建详情', index=False)

    print(f"✅ 主报告已保存: {output_dir}/终极因子组合策略分析报告.xlsx")

    # 2. 因子工程报告
    with pd.ExcelWriter(f'{output_dir}/因子工程分析报告.xlsx') as writer:

        # 原始因子表现
        original_factors = all_factor_performance[all_factor_performance['factor_type'] == '原始因子']
        original_factors.to_excel(writer, sheet_name='原始因子表现', index=False)

        # 工程因子表现
        engineered_factors = all_factor_performance[all_factor_performance['factor_type'] == '工程因子']
        engineered_factors.to_excel(writer, sheet_name='工程因子表现', index=False)

        # 因子创建说明
        factor_creation_guide = pd.DataFrame([
            {
                'factor_category': '动量因子',
                'factors': '利差动量_5日, 利差动量_10日, 利差动量_20日',
                'calculation': '利差的N日变化率',
                'logic': '捕捉利差的趋势性变化'
            },
            {
                'factor_category': '波动率因子',
                'factors': '利差波动率_5日, 利差波动率_10日, 利差波动率_20日',
                'calculation': '利差的N日滚动标准差',
                'logic': '衡量市场不确定性'
            },
            {
                'factor_category': '相对强弱因子',
                'factors': '30y相对强弱, 30y相对强弱偏离',
                'calculation': '30y/10y比值及其偏离',
                'logic': '反映长短端相对表现'
            },
            {
                'factor_category': '复合因子',
                'factors': '商品综合指数, 资金面利差',
                'calculation': '多个相关因子的平均值或差值',
                'logic': '综合反映某类信息'
            },
            {
                'factor_category': '技术面增强因子',
                'factors': '偏离度动量, 偏离度加速度',
                'calculation': '偏离度的一阶和二阶差分',
                'logic': '捕捉技术面变化的速度和加速度'
            },
            {
                'factor_category': '市场状态因子',
                'factors': '高波动状态, 极端偏离状态',
                'calculation': '基于分位数的二元状态变量',
                'logic': '识别特殊市场环境'
            },
            {
                'factor_category': '交互因子',
                'factors': '基本面技术面交互, 资金面成交交互',
                'calculation': '两个因子的乘积',
                'logic': '捕捉因子间的协同效应'
            }
        ])
        factor_creation_guide.to_excel(writer, sheet_name='因子创建指南', index=False)

    print(f"✅ 因子工程报告已保存: {output_dir}/因子工程分析报告.xlsx")

    # 3. 策略实施指南
    implementation_guide = pd.DataFrame([
        {
            'step': '步骤1: 数据准备',
            'description': '收集所有原始因子数据，确保数据质量',
            'details': '检查缺失值，进行前向填充处理',
            'frequency': '每日'
        },
        {
            'step': '步骤2: 因子工程',
            'description': '基于原始因子创建工程因子',
            'details': '计算动量、波动率、相对强弱等衍生因子',
            'frequency': '每日'
        },
        {
            'step': '步骤3: 因子评估',
            'description': '计算所有因子的预测胜率和统计显著性',
            'details': '滚动窗口重新评估因子有效性',
            'frequency': '每周'
        },
        {
            'step': '步骤4: 策略选择',
            'description': '根据市场环境选择最适合的策略',
            'details': '正常市场用分层权重，高波动期用动态阈值',
            'frequency': '每日'
        },
        {
            'step': '步骤5: 信号生成',
            'description': '基于选定策略生成交易信号',
            'details': '计算各因子信号，按权重汇总得出最终信号',
            'frequency': '每日'
        },
        {
            'step': '步骤6: 风险控制',
            'description': '设置止损和仓位管理',
            'details': '单次交易不超过15%仓位，2.5BP止损',
            'frequency': '每日'
        },
        {
            'step': '步骤7: 策略监控',
            'description': '监控策略表现，及时调整',
            'details': '跟踪胜率变化，必要时重新优化权重',
            'frequency': '每月'
        }
    ])

    with pd.ExcelWriter(f'{output_dir}/策略实施指南.xlsx') as writer:
        implementation_guide.to_excel(writer, sheet_name='实施步骤', index=False)

        # 风险管理指南
        risk_management = pd.DataFrame([
            {
                'risk_type': '模型风险',
                'description': '因子失效或策略过拟合',
                'mitigation': '定期重新评估，使用多策略组合',
                'monitoring': '月度胜率跟踪'
            },
            {
                'risk_type': '市场风险',
                'description': '极端市场条件下策略失效',
                'mitigation': '设置止损，降低仓位',
                'monitoring': '实时波动率监控'
            },
            {
                'risk_type': '流动性风险',
                'description': '债券市场流动性不足',
                'mitigation': '关注成交量指标，避免极端时期交易',
                'monitoring': '日度成交量跟踪'
            },
            {
                'risk_type': '操作风险',
                'description': '信号执行偏差或系统故障',
                'mitigation': '建立备用系统，人工复核',
                'monitoring': '交易执行质量检查'
            }
        ])
        risk_management.to_excel(writer, sheet_name='风险管理', index=False)

    print(f"✅ 实施指南已保存: {output_dir}/策略实施指南.xlsx")

save_comprehensive_excel_reports(all_factor_performance, strategies_df, weight_optimization, df_engineered, output_dir)

def generate_ultimate_strategy_report(all_factor_performance, strategies_df, df, output_dir):
    """生成终极策略报告"""
    print(f"\n{'='*60}")
    print("📋 生成终极策略分析报告")
    print(f"{'='*60}")

    # 找出最佳策略
    best_strategy = strategies_df.loc[strategies_df['accuracy'].idxmax()]
    baseline_accuracy = df['direction_next'].mean()

    # 统计信息
    total_factors = len(all_factor_performance)
    original_factors_count = len(all_factor_performance[all_factor_performance['factor_type'] == '原始因子'])
    engineered_factors_count = len(all_factor_performance[all_factor_performance['factor_type'] == '工程因子'])

    # 生成最终报告
    report = f"""
🚀 30-10y利差终极因子工程与策略优化报告
=====================================

📊 分析概况
- 分析期间: 346个交易日
- 基准走阔概率: {baseline_accuracy:.3f} ({baseline_accuracy*100:.1f}%)
- 总因子数量: {total_factors}个
- 原始因子: {original_factors_count}个
- 工程因子: {engineered_factors_count}个

🏆 最佳策略表现
策略名称: {best_strategy['strategy_name']}
预测胜率: {best_strategy['accuracy']:.4f} ({best_strategy['accuracy']*100:.2f}%)
相比基准提升: {(best_strategy['accuracy'] - baseline_accuracy)*100:.2f}个百分点
相比随机提升: {(best_strategy['accuracy'] - 0.5)*100:.2f}个百分点

📈 TOP10因子排名
"""

    top10_factors = all_factor_performance.head(10)
    for i, (_, factor) in enumerate(top10_factors.iterrows(), 1):
        report += f"{i:2d}. {factor['factor']} ({factor['factor_type']}): {factor['win_rate']:.4f}\n"

    report += f"""
🎯 策略构建方案

1. 分层权重策略 (推荐)
   - 技术面因子权重: 3
   - 成交量因子权重: 2
   - 基本面因子权重: 1
   - 决策规则: 加权得分 > 总权重/2 → 预测走阔

2. 动态阈值策略
   - 基于分层权重策略
   - 高波动期阈值: 60%
   - 低波动期阈值: 40%

3. 胜率加权策略
   - 权重 = 各因子历史胜率
   - 适用于因子表现差异明显时

💡 因子工程创新
成功创建 {engineered_factors_count} 个工程因子:
- 动量因子: 捕捉利差趋势变化
- 波动率因子: 衡量市场不确定性
- 相对强弱因子: 反映长短端相对表现
- 复合因子: 综合多维度信息
- 技术面增强因子: 捕捉变化速度和加速度
- 市场状态因子: 识别特殊市场环境
- 交互因子: 捕捉因子协同效应

🔧 实施建议
1. 数据准备: 每日收集并处理原始因子数据
2. 因子工程: 每日计算衍生因子
3. 策略选择: 根据市场波动性选择合适策略
4. 信号生成: 按权重汇总生成交易信号
5. 风险控制: 15%仓位上限，2.5BP止损
6. 策略监控: 月度重新评估因子有效性

💰 预期收益
基于 {best_strategy['accuracy']:.4f} 胜率:
- 假设每次盈利2BP，亏损1.5BP
- 年化交易约200次
- 预期年化收益: {(best_strategy['accuracy'] * 2 - (1-best_strategy['accuracy']) * 1.5) * 200:.1f}BP
- 预期年化收益率: 约 {(best_strategy['accuracy'] * 2 - (1-best_strategy['accuracy']) * 1.5) * 200 / 100:.2f}%

⚠️ 风险提示
1. 模型基于历史数据，未来表现可能有差异
2. 极端市场条件下因子可能失效
3. 建议与基本面分析相结合使用
4. 定期重新评估和优化策略参数

🎉 核心成果
1. 成功将预测胜率从基准 {baseline_accuracy:.3f} 提升至 {best_strategy['accuracy']:.4f}
2. 创建了 {engineered_factors_count} 个有效的工程因子
3. 构建了多种可选的组合策略
4. 提供了完整的实施和风险管理框架

该终极因子工程策略为30-10y利差交易提供了科学、系统、高胜率的决策工具！
"""

    # 保存最终报告
    with open(f'{output_dir}/终极策略分析报告.txt', 'w', encoding='utf-8') as f:
        f.write(report)

    print(report)
    print(f"\n✅ 终极报告已保存: {output_dir}/终极策略分析报告.txt")

generate_ultimate_strategy_report(all_factor_performance, strategies_df, df_engineered, output_dir)

print(f"\n" + "="*100)
print(f"🎉 30-10y利差终极因子工程与策略优化完成！")
print(f"="*100)

# 输出关键结果
if len(all_factor_performance) > 0:
    best_factor = all_factor_performance.iloc[0]
    print(f"🏆 最佳因子: {best_factor['factor']} ({best_factor['factor_type']})")
    print(f"📊 最高因子胜率: {best_factor['win_rate']:.4f} ({best_factor['win_rate']*100:.2f}%)")

if len(strategies_df) > 0:
    best_strategy = strategies_df.loc[strategies_df['accuracy'].idxmax()]
    print(f"🚀 最佳策略: {best_strategy['strategy_name']}")
    print(f"📈 最高策略胜率: {best_strategy['accuracy']:.4f} ({best_strategy['accuracy']*100:.2f}%)")

print(f"📁 输出目录: {output_dir}")
print(f"📊 总因子数量: {len(all_factor_performance)}")
print(f"🔬 工程因子数量: {len(all_factor_performance[all_factor_performance['factor_type'] == '工程因子'])}")
print(f"📋 策略数量: {len(strategies_df)}")

baseline = df_engineered['direction_next'].mean()
if len(strategies_df) > 0:
    improvement = (best_strategy['accuracy'] - baseline) * 100
    print(f"📈 相比基准提升: {improvement:.2f}个百分点")

print(f"="*100)
print(f"🎯 调集所有算力的深度分析已完成！")
print(f"="*100)
