"""
Tencent PCG Attribution Modeling
--------------------------------
Causal inference methods:
1. Propensity Score Matching (PSM)
2. Difference-in-Differences (DID)
3. Root cause analysis for metric anomalies
"""

import pandas as pd
import numpy as np
import statsmodels.api as sm
from sklearn.linear_model import LogisticRegression
from sklearn.neighbors import NearestNeighbors
from scipy import stats

def evaluate_feature_impact(feature_name, pre_period='2023-01-01', post_period='2023-02-01'):
    """
    Evaluate feature impact using PSM and DID methods
    
    Parameters:
    feature_name (str): Name of feature to evaluate
    pre_period (str): Start date of pre-feature period
    post_period (str): Start date of post-feature period
    
    Returns:
    dict: Analysis results with causal impact estimates
    """
    # Load data (placeholder - actual implementation would load from database)
    df = pd.DataFrame({
        'user_id': range(1000),
        'treatment': np.random.choice([0, 1], size=1000, p=[0.7, 0.3]),
        'age': np.random.randint(18, 60, size=1000),
        'activity_level': np.random.normal(50, 15, size=1000),
        'pre_metric': np.random.normal(100, 20, size=1000),
        'post_metric': np.random.normal(110, 25, size=1000)
    })
    
    # Propensity Score Matching (PSM)
    psm_results = run_psm_analysis(df, feature_name)
    
    # Difference-in-Differences (DID)
    did_results = run_did_analysis(df, pre_period, post_period, feature_name)
    
    return {
        "feature": feature_name,
        "psm_impact": psm_results,
        "did_impact": did_results,
        "recommendations": generate_feature_recommendations(psm_results, did_results)
    }

def analyze_metric_anomaly(metric_name, change_date, potential_causes):
    """
    Analyze root cause of metric anomaly using statistical methods
    
    Parameters:
    metric_name (str): Name of anomalous metric (e.g., session_duration)
    change_date (str): Date when anomaly started
    potential_causes (list): Possible causes to investigate
    
    Returns:
    dict: Root cause analysis report
    """
    # Load data (placeholder)
    df = pd.DataFrame({
        'date': pd.date_range(start='2023-01-01', periods=90, freq='D'),
        metric_name: np.concatenate([
            np.random.normal(100, 10, 60), 
            np.random.normal(85, 12, 30)
        ]),
        'content_quality': np.random.normal(0.7, 0.1, 90),
        'ui_version': ['old']*60 + ['new']*30
    })
    
    # Pre/post change comparison
    pre_change = df[df['date'] < change_date][metric_name]
    post_change = df[df['date'] >= change_date][metric_name]
    
    # T-test for significance
    t_stat, p_value = stats.ttest_ind(pre_change, post_change, equal_var=False)
    
    # Regression analysis for potential causes
    X = sm.add_constant(df[['content_quality', 'ui_version']])
    X['ui_version'] = X['ui_version'].map({'old': 0, 'new': 1})
    y = df[metric_name]
    
    model = sm.OLS(y, X).fit()
    
    return {
        "metric": metric_name,
        "change_date": change_date,
        "pre_mean": pre_change.mean(),
        "post_mean": post_change.mean(),
        "p_value": p_value,
        "regression_coefficients": model.params.to_dict(),
        "regression_pvalues": model.pvalues.to_dict(),
        "primary_cause": identify_primary_cause(model),
        "recommendations": generate_anomaly_recommendations(model, metric_name)
    }

def run_psm_analysis(df, feature_name):
    """Propensity Score Matching implementation"""
    # Calculate propensity scores
    X = df[['age', 'activity_level', 'pre_metric']]
    y = df['treatment']
    
    ps_model = LogisticRegression()
    ps_model.fit(X, y)
    df['propensity_score'] = ps_model.predict_proba(X)[:, 1]
    
    # Match treatment and control groups
    treated = df[df['treatment'] == 1]
    control = df[df['treatment'] == 0]
    
    nbrs = NearestNeighbors(n_neighbors=1).fit(control[['propensity_score']])
    distances, indices = nbrs.kneighbors(treated[['propensity_score']])
    
    matched_control = control.iloc[indices.flatten()]
    
    # Calculate treatment effect
    effect = treated['post_metric'].mean() - matched_control['post_metric'].mean()
    
    return {
        "treatment_effect": effect,
        "p_value": stats.ttest_ind(treated['post_metric'], matched_control['post_metric']).pvalue
    }

def run_did_analysis(df, pre_period, post_period, feature_name):
    """Difference-in-Differences implementation"""
    # Placeholder - actual implementation would use time-series data
    # DID formula: (Treatment_post - Treatment_pre) - (Control_post - Control_pre)
    treatment_pre = 100
    treatment_post = 120
    control_pre = 100
    control_post = 105
    
    did_effect = (treatment_post - treatment_pre) - (control_post - control_pre)
    
    return {
        "did_effect": did_effect,
        "relative_change": did_effect / treatment_pre
    }

def identify_primary_cause(model):
    """Identify primary cause from regression results"""
    # Exclude intercept
    pvalues = model.pvalues.drop('const')
    min_pvalue = pvalues.min()
    
    if min_pvalue < 0.05:
        return pvalues.idxmin()
    else:
        return "No significant factor identified"

def generate_feature_recommendations(psm, did):
    """Generate feature recommendations based on impact analysis"""
    recommendations = []
    
    if psm['treatment_effect'] > 0 and psm['p_value'] < 0.05:
        recommendations.append("Roll out feature to all users")
    elif did['did_effect'] > 0:
        recommendations.append("Continue monitoring feature performance")
    else:
        recommendations.append("Consider feature redesign or sunsetting")
    
    return recommendations

def generate_anomaly_recommendations(model, metric_name):
    """Generate recommendations based on root cause analysis"""
    recommendations = []
    coefficients = model.params
    pvalues = model.pvalues
    
    # UI change impact
    if 'ui_version' in coefficients and pvalues['ui_version'] < 0.05:
        if coefficients['ui_version'] < 0:
            recommendations.append(f"Revert UI changes for {metric_name} improvement")
        else:
            recommendations.append("Maintain current UI version")
    
    # Content quality impact
    if 'content_quality' in coefficients and pvalues['content_quality'] < 0.05:
        if coefficients['content_quality'] < 0:
            recommendations.append("Improve content quality standards")
        else:
            recommendations.append("Continue current content strategy")
    
    return recommendations
