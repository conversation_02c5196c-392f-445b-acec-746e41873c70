import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.decomposition import PCA
from sklearn.cross_decomposition import PLSRegression
from sklearn.preprocessing import StandardScaler
from statsmodels.tsa.stattools import grangercausalitytests
from scipy import stats
import seaborn as sns

def build_interest_rate_index():
    # 加载预处理后的数据
    data = pd.read_csv('processed_data.csv', index_col=0, parse_dates=True)
    
    # 标准化数据
    scaler = StandardScaler()
    scaled_data = scaler.fit_transform(data)
    
    # 使用PCA构建利率领先指数
    pca = PCA(n_components=1)
    pca_index = pca.fit_transform(scaled_data)
    
    # 将PCA指数转换为DataFrame
    index_df = pd.DataFrame(pca_index, 
                           index=data.index, 
                           columns=['PCA_Index'])
    
    # 反转PCA指数方向
    index_df['PCA_Index'] = -index_df['PCA_Index']
    
    return index_df

def build_pls_index(data, target_series):
    # 标准化数据
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(data)
    
    # 使用PLS回归构建指数（最大化与目标序列的相关性）
    pls = PLSRegression(n_components=1)
    pls.fit(X_scaled, target_series)
    
    # 获取PLS指数
    pls_index = pls.transform(X_scaled)
    
    # 转换为DataFrame
    pls_df = pd.DataFrame(pls_index, 
                         index=data.index, 
                         columns=['PLS_Index'])
    
    return pls_df

def load_bond_yields():
    # 加载债券收益率数据
    data_path = '/Users/<USER>/Desktop/利率领先指数底稿0703.xlsx'
    bond_df = pd.read_excel(data_path, sheet_name='中债收益率')
    
    # 设置时间索引
    bond_df.set_index(pd.to_datetime(bond_df.iloc[:, 0]), inplace=True)
    bond_df = bond_df.iloc[:, 1:]
    bond_df.columns = ['10Y_Yield', '1Y_Yield', '10-1Y_Spread']
    
    return bond_df

def analyze_relationships(index_df, bond_df):
    # 合并指数和债券数据
    combined = pd.merge(index_df, bond_df, left_index=True, right_index=True, how='inner')
    
    # 计算相关系数
    corr_matrix = combined.corr()
    print("Correlation Matrix:")
    print(corr_matrix)
    
    # 可视化指数与10年期收益率的关系
    plt.figure(figsize=(12, 6))
    plt.plot(combined.index, combined['PCA_Index'], label='PCA Index')
    plt.plot(combined.index, combined['PLS_Index'], label='PLS Index')
    plt.plot(combined.index, combined['10Y_Yield'], label='10Y Yield', alpha=0.7)
    plt.title('Interest Rate Leading Index vs 10Y Government Bond Yield')
    plt.xlabel('Date')
    plt.ylabel('Standardized Value')
    plt.legend()
    plt.grid(True)
    plt.savefig('index_yield_comparison.png')
    plt.close()
    
    # 计算PLS指数与10年收益率的时滞相关性
    max_lead = 6  # 最大领先月数
    pls_lead_corrs = []
    for lead in range(0, max_lead+1):
        shifted_yield = combined['10Y_Yield'].shift(-lead)
        corr = stats.pearsonr(combined['PLS_Index'].iloc[:-lead], shifted_yield.dropna())[0]
        pls_lead_corrs.append(corr)
    
    # 绘制领先相关性图
    plt.figure(figsize=(10, 6))
    plt.plot(range(0, max_lead+1), pls_lead_corrs, 'o-')
    plt.title('PLS Index Leading Correlation with 10Y Yield')
    plt.xlabel('Leading Months')
    plt.ylabel('Correlation Coefficient')
    plt.axhline(0, color='gray', linestyle='--')
    plt.grid(True)
    plt.savefig('pls_lead_correlation.png')
    plt.close()
    
    # 执行格兰杰因果检验
    granger_data = combined[['PLS_Index', '10Y_Yield']].dropna()
    granger_test = grangercausalitytests(granger_data, maxlag=3, verbose=False)
    
    print("\nGranger Causality Test Results (PLS Index causes 10Y Yield):")
    for lag in range(1, 4):
        p_value = granger_test[lag][0]['ssr_ftest'][1]
        print(f"Lag {lag}: p-value = {p_value:.4f}")
    
    return combined

if __name__ == "__main__":
    # 加载预处理数据
    processed_data = pd.read_csv('processed_data.csv', index_col=0, parse_dates=True)
    
    # 加载债券收益率数据
    bond_df = load_bond_yields()
    
    # 构建PCA指数
    pca_index = build_interest_rate_index()
    
    # 构建PLS指数（使用未来1个月的10年期收益率作为目标）
    # 创建目标序列：未来1个月的10年期收益率
    target = bond_df['10Y_Yield'].shift(-1).dropna()
    aligned_data = processed_data.loc[target.index]
    
    pls_index = build_pls_index(aligned_data, target)
    
    # 合并所有指数
    index_df = pd.merge(pca_index, pls_index, left_index=True, right_index=True, how='inner')
    
    # 分析指数与债券收益率的关系
    combined_data = analyze_relationships(index_df, bond_df)
    combined_data.to_csv('combined_index_yield_data.csv')
    
    print("Interest rate leading index built and analyzed")
