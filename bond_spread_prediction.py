import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体
try:
    plt.rcParams['font.sans-serif'] = ['Songti SC', 'STHeiti', 'Hiragino Sans GB']  # 系统自带中文字体
    plt.rcParams['font.family'] = 'sans-serif'
except:
    print("中文显示配置失败，请执行以下操作：")
    print("1. 打开字体册应用，确认已安装宋体、华文黑体或冬青黑体")
    print("2. 或执行命令安装字体：brew tap homebrew/cask-fonts && brew install --cask font-sarasa-gothic")
plt.rcParams['axes.unicode_minus'] = False  # 解决负号显示问题
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import TimeSeriesSplit
from sklearn.preprocessing import StandardScaler

class SpreadPredictor:
    def __init__(self):
        self.factors = [
            '互联网搜索指数:失业金领取条件',
            'R007',
            '30Y成交量',
            '1/沪深300PE-10y'
        ]
        self.model = LogisticRegression(class_weight='balanced', random_state=42)
        self.scaler = StandardScaler()
    
    def preprocess(self, data):
        """因子预处理"""
        # 列名统一处理（增强兼容性）
        print("原始数据列名:", data.columns.tolist())
        
        # 使用精确列名匹配
        column_mapping = {
            '30-10y': '利差',
            '30Y成交量': '30Y成交量',
            'R007': 'R007',
            '偏离度（偏离均线）': '偏离度_均线',
            '利差变动': '利差变动'  # 确保目标变量存在
        }
        data = data.rename(columns=column_mapping)
        
        # 确保关键列存在
        required_columns = ['利差', '利差变动', '30Y成交量', 'R007']
        missing_cols = [col for col in required_columns if col not in data.columns]
        if missing_cols:
            raise ValueError(f"缺失必要列: {missing_cols}")
            
        print("处理后的列名:", data.columns.tolist())
        
        # 动态标准化（优化实现）
        rolling_mean = data[self.factors].rolling(252, min_periods=20).mean()
        rolling_std = data[self.factors].rolling(252, min_periods=20).std()
        scaled = (data[self.factors] - rolling_mean) / rolling_std.replace(0, 1e-6)
        # 方向一致性调整
        scaled['互联网搜索指数:失业金领取条件'] *= -1  # 经济预期反向因子
        
        # 合并关键列到处理后的数据
        # 排除已标准化处理的列
        processed_data = pd.concat([
            scaled,
            data[['利差', '利差变动', 'DR007']]
        ], axis=1).dropna()
        
        print("预处理后的列名:", processed_data.columns.tolist())
        return processed_data
    
    def create_features(self, data):
        """特征工程"""
        print("创建特征时的可用列:", data.columns.tolist())
        features = data.copy()
        # 确保关键列存在
        if '利差' not in features.columns:
            raise ValueError("利差列缺失，请检查数据预处理步骤")
        # 量价背离特征
        features['量价背离'] = features['30Y成交量'] * features['利差'].diff(3).fillna(0)
        # 资金面压力指数
        features['资金压力'] = features['R007'].rolling(5).mean() * features['DR007'].diff(3).fillna(0)
        return features.dropna()
    
    def train_model(self, train_data):
        """模型训练"""
        X = self.scaler.fit_transform(train_data)
        y = (train_data['利差变动'].shift(-1) > 0).astype(int).dropna()
        X = X[:len(y)]  # 确保数据对齐
        self.model.fit(X, y)
    
    def predict_signal(self, latest_data):
        """信号预测"""
        X = self.scaler.transform(latest_data)
        proba = self.model.predict_proba(X)[:,1]
        return np.where(proba > 0.65, 1, np.where(proba < 0.35, -1, 0))
    
    def backtest(self, data):
        """滚动回测"""
        tscv = TimeSeriesSplit(n_splits=5)
        results = []
        
        for train_idx, test_idx in tscv.split(data):
            train = data.iloc[train_idx]
            test = data.iloc[test_idx]
            
            self.train_model(train)
            signals = self.predict_signal(test)
            
            # 计算收益（假设交易10BP变动）
            returns = signals * test['利差变动'].abs().mean()
            results.append(returns.mean())
        
        print(f"平均胜率: {np.mean(results)*100:.1f}%")
        
        # 可视化回测结果
        plt.figure(figsize=(12, 6))
        plt.plot(data.index, data['利差'], label='30-10y利差')
        plt.title('利差走势与交易信号')
        plt.legend()
        plt.savefig('trade_signals.png')
        
        return np.mean(results)

    def plot_factor_analysis(self, data):
        """因子分析可视化"""
        plt.figure(figsize=(14, 8))
        
        # 因子相关性热力图
        plt.subplot(2,2,1)
        corr_matrix = data[self.factors].corr()
        sns.heatmap(corr_matrix, annot=True, cmap='coolwarm')
        plt.title('因子相关性矩阵')
        
        # 因子有效性分析
        plt.subplot(2,2,2)
        effectiveness = []
        for factor in self.factors:
            corr = data[factor].shift(1).corr(data['利差变动'])
            effectiveness.append(abs(corr))
        plt.barh(self.factors, effectiveness)
        plt.title('因子预测有效性')
        
        plt.tight_layout()
        plt.savefig('factor_analysis.png')

if __name__ == "__main__":
    predictor = SpreadPredictor()
    # 从原始数据文件加载
    raw_data = pd.read_excel("/Users/<USER>/Desktop/量化择时0526.xlsx", 
                           sheet_name=0,
                           parse_dates=['Unnamed: 0'],
                           index_col='Unnamed: 0')
    # 使用预处理后的数据
    processed_data = predictor.preprocess(raw_data)
    features = predictor.create_features(processed_data)
    predictor.backtest(features)
    predictor.plot_factor_analysis(processed_data)
