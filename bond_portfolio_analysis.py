import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
import os
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score
warnings.filterwarnings('ignore')

# 修复中文字体显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.figsize'] = (14, 10)
plt.rcParams['savefig.dpi'] = 300

# 创建输出文件夹
output_dir = '/Users/<USER>/Desktop/择时累计收益率'
if not os.path.exists(output_dir):
    os.makedirs(output_dir)

print("=" * 100)
print("🏦 专业债券投资组合分析")
print("基于30Y国债收益率的择时策略回测")
print("=" * 100)

# 加载数据
file_path = '/Users/<USER>/Desktop/择时胜率0527.xlsx'
try:
    df = pd.read_excel(file_path)
    print(f"✓ 数据加载成功，数据形状: {df.shape}")
except Exception as e:
    print(f"✗ 数据加载失败: {e}")
    exit()

# 数据预处理
df_processed = df.fillna(method='ffill').fillna(method='bfill').copy()
if 'Unnamed: 0' in df_processed.columns:
    df_processed = df_processed.rename(columns={'Unnamed: 0': '日期'})

# 确保日期列为datetime格式
if '日期' in df_processed.columns:
    df_processed['日期'] = pd.to_datetime(df_processed['日期'])
    df_processed = df_processed.sort_values('日期').reset_index(drop=True)
else:
    df_processed['日期'] = pd.date_range(start='2024-01-02', periods=len(df_processed), freq='D')

print(f"数据时间范围: {df_processed['日期'].min()} 到 {df_processed['日期'].max()}")
print(f"30Y收益率范围: {df_processed['30y'].min():.3f}% 到 {df_processed['30y'].max():.3f}%")

class BondPortfolioAnalyzer:
    """专业债券投资组合分析器"""

    def __init__(self, data, duration_30y=18.5):
        """
        初始化债券分析器
        duration_30y: 30年国债的修正久期，通常在18-20年之间
        """
        self.data = data.copy()
        self.duration_30y = duration_30y  # 30年国债修正久期
        self.daily_data = None
        self.weekly_data = None
        self.signals = {}
        self.performance_metrics = {}

        print(f"使用30年国债修正久期: {self.duration_30y}年")

    def prepare_daily_data(self):
        """准备日频数据和因子"""
        print(f"\n{'='*60}")
        print("📊 准备日频数据和因子")
        print(f"{'='*60}")

        df = self.data.copy()

        # 计算30Y收益率变动（单位：%）
        df['30y_change'] = df['30y'].diff()

        # 计算债券价格收益率 = -久期 × 收益率变动
        # 收益率上升1%，债券价格下降约久期%
        df['bond_return'] = -self.duration_30y * df['30y_change'] / 100

        # 创建日频工程因子
        df['30y_momentum_3d'] = df['30y'].pct_change(3)
        df['30y_momentum_5d'] = df['30y'].pct_change(5)
        df['30y_volatility_5d'] = df['30y'].rolling(5).std()
        df['30y_volatility_10d'] = df['30y'].rolling(10).std()

        if '偏离度（偏离均线）' in df.columns:
            df['deviation_momentum'] = df['偏离度（偏离均线）'].diff(1)
            df['deviation_abs'] = abs(df['偏离度（偏离均线）'])

        # 资金面因子
        if 'R007' in df.columns and 'DR007' in df.columns:
            df['funding_spread'] = df['R007'] - df['DR007']
            df['funding_momentum'] = df['funding_spread'].diff(1)

        # 机构行为因子
        institutional_cols = [col for col in df.columns if '净买入' in col]
        if len(institutional_cols) >= 2:
            df['institutional_total'] = df[institutional_cols].sum(axis=1)
            df['institutional_momentum'] = df['institutional_total'].diff(1)

        # 交互因子
        if '水泥价格指数' in df.columns and '偏离度（偏离均线）' in df.columns:
            df['fundamental_technical_interaction'] = df['水泥价格指数'] * df['偏离度（偏离均线）']

        # 删除包含NaN的行
        df = df.dropna()

        self.daily_data = df
        print(f"日频数据样本数量: {len(df)}")
        print(f"债券收益率统计: 均值={df['bond_return'].mean()*100:.3f}%, 标准差={df['bond_return'].std()*100:.3f}%")

    def prepare_weekly_data(self):
        """准备周频数据和因子"""
        print(f"\n{'='*60}")
        print("📊 准备周频数据和因子")
        print(f"{'='*60}")

        # 设置日期为索引
        df_daily = self.data.set_index('日期')

        # 添加周标识
        df_daily['week'] = df_daily.index.to_series().dt.to_period('W')

        # 定义聚合方法
        agg_methods = {}
        price_factors = ['30y', '10y', '30-10y', 'R007', 'DR007', '偏离度（偏离均线）']
        volume_factors = ['30Y成交量', '成交量:DR007', '成交量:R007']
        institutional_factors = [col for col in df_daily.columns if '净买入' in col]

        for col in df_daily.columns:
            if col == 'week':
                continue
            elif col in price_factors:
                agg_methods[col] = 'last'  # 周末收盘价
            elif col in volume_factors + institutional_factors:
                agg_methods[col] = 'sum'   # 周度累计
            else:
                agg_methods[col] = 'last'

        # 按周聚合
        df_weekly = df_daily.groupby('week').agg(agg_methods)
        df_weekly.reset_index(inplace=True)
        df_weekly['日期'] = df_weekly['week'].dt.end_time
        df_weekly = df_weekly.drop('week', axis=1)

        # 计算周度30Y收益率变动
        df_weekly['30y_change'] = df_weekly['30y'].diff()

        # 计算周度债券价格收益率
        df_weekly['bond_return'] = -self.duration_30y * df_weekly['30y_change'] / 100

        # 创建周频工程因子
        df_weekly['30y_momentum_2w'] = df_weekly['30y'].pct_change(2)
        df_weekly['30y_momentum_4w'] = df_weekly['30y'].pct_change(4)
        df_weekly['30y_volatility_4w'] = df_weekly['30y'].rolling(4).std()
        df_weekly['30y_volatility_8w'] = df_weekly['30y'].rolling(8).std()

        if '偏离度（偏离均线）' in df_weekly.columns:
            df_weekly['deviation_momentum_w'] = df_weekly['偏离度（偏离均线）'].diff(1)
            df_weekly['deviation_abs_w'] = abs(df_weekly['偏离度（偏离均线）'])

        # 资金面因子
        if 'R007' in df_weekly.columns and 'DR007' in df_weekly.columns:
            df_weekly['funding_spread_w'] = df_weekly['R007'] - df_weekly['DR007']
            df_weekly['funding_momentum_w'] = df_weekly['funding_spread_w'].diff(1)

        # 机构行为因子
        if len(institutional_factors) >= 2:
            df_weekly['institutional_total_w'] = df_weekly[institutional_factors].sum(axis=1)
            df_weekly['institutional_momentum_w'] = df_weekly['institutional_total_w'].diff(1)

        # 删除包含NaN的行
        df_weekly = df_weekly.dropna()

        self.weekly_data = df_weekly
        print(f"周频数据样本数量: {len(df_weekly)}")
        print(f"周度债券收益率统计: 均值={df_weekly['bond_return'].mean()*100:.3f}%, 标准差={df_weekly['bond_return'].std()*100:.3f}%")

    def generate_trading_signals(self):
        """生成交易信号"""
        print(f"\n{'='*60}")
        print("📈 生成交易信号")
        print(f"{'='*60}")

        # 日频信号
        daily_signals = self._generate_daily_signals()

        # 周频信号
        weekly_signals = self._generate_weekly_signals()

        self.signals = {
            'daily': daily_signals,
            'weekly': weekly_signals
        }

        return self.signals

    def _generate_daily_signals(self):
        """生成日频交易信号"""
        df = self.daily_data.copy()

        # 策略1: 技术面主导策略
        tech_score = 0
        if '偏离度（偏离均线）' in df.columns:
            deviation_median = df['偏离度（偏离均线）'].median()
            # 偏离度为正且较大时，预期利差收窄，债券价格上涨
            tech_score += (df['偏离度（偏离均线）'] > deviation_median).astype(int) * 3

        if 'deviation_momentum' in df.columns:
            momentum_median = df['deviation_momentum'].median()
            # 偏离度动量为正时，预期继续偏离，债券价格可能下跌
            tech_score += (df['deviation_momentum'] < momentum_median).astype(int) * 2

        # 策略2: 资金面主导策略
        funding_score = 0
        if 'funding_spread' in df.columns:
            funding_median = df['funding_spread'].median()
            # 资金面利差小时（紧张），债券需求增加，价格上涨
            funding_score += (df['funding_spread'] < funding_median).astype(int) * 3

        if 'R007' in df.columns:
            r007_median = df['R007'].median()
            # R007较低时，流动性充裕，债券价格上涨
            funding_score += (df['R007'] < r007_median).astype(int) * 2

        # 策略3: 机构行为策略
        institutional_score = 0
        if 'institutional_total' in df.columns:
            inst_median = df['institutional_total'].median()
            # 机构净买入多时，债券价格上涨
            institutional_score += (df['institutional_total'] > inst_median).astype(int) * 3

        if 'institutional_momentum' in df.columns:
            inst_mom_median = df['institutional_momentum'].median()
            # 机构净买入动量为正时，债券价格继续上涨
            institutional_score += (df['institutional_momentum'] > inst_mom_median).astype(int) * 2

        # 策略4: 综合策略
        total_score = tech_score + funding_score + institutional_score
        max_score = 10  # 3+2+3+2

        # 生成信号 (1=看涨债券, 0=看跌债券)
        signals = {
            'technical_signal': (tech_score > 2.5).astype(int),
            'funding_signal': (funding_score > 2.5).astype(int),
            'institutional_signal': (institutional_score > 2.5).astype(int),
            'comprehensive_signal': (total_score > max_score/2).astype(int),
            'date': df['日期'],
            '30y': df['30y'],
            'bond_return': df['bond_return']
        }

        return pd.DataFrame(signals)

    def _generate_weekly_signals(self):
        """生成周频交易信号"""
        df = self.weekly_data.copy()

        # 策略1: 周度技术面策略
        tech_score = 0
        if '偏离度（偏离均线）' in df.columns:
            deviation_median = df['偏离度（偏离均线）'].median()
            tech_score += (df['偏离度（偏离均线）'] > deviation_median).astype(int) * 4

        if 'deviation_momentum_w' in df.columns:
            momentum_median = df['deviation_momentum_w'].median()
            tech_score += (df['deviation_momentum_w'] < momentum_median).astype(int) * 3

        # 策略2: 周度资金面策略
        funding_score = 0
        if 'funding_spread_w' in df.columns:
            funding_median = df['funding_spread_w'].median()
            funding_score += (df['funding_spread_w'] < funding_median).astype(int) * 4

        if 'R007' in df.columns:
            r007_median = df['R007'].median()
            funding_score += (df['R007'] < r007_median).astype(int) * 3

        # 策略3: 周度机构行为策略
        institutional_score = 0
        if 'institutional_total_w' in df.columns:
            inst_median = df['institutional_total_w'].median()
            institutional_score += (df['institutional_total_w'] > inst_median).astype(int) * 4

        if 'institutional_momentum_w' in df.columns:
            inst_mom_median = df['institutional_momentum_w'].median()
            institutional_score += (df['institutional_momentum_w'] > inst_mom_median).astype(int) * 3

        # 策略4: 周度综合策略
        total_score = tech_score + funding_score + institutional_score
        max_score = 14  # 4+3+4+3

        # 生成信号
        signals = {
            'technical_signal': (tech_score > 3.5).astype(int),
            'funding_signal': (funding_score > 3.5).astype(int),
            'institutional_signal': (institutional_score > 3.5).astype(int),
            'comprehensive_signal': (total_score > max_score/2).astype(int),
            'date': df['日期'],
            '30y': df['30y'],
            'bond_return': df['bond_return']
        }

        return pd.DataFrame(signals)

# 初始化债券分析器
bond_analyzer = BondPortfolioAnalyzer(df_processed, duration_30y=18.5)

# 准备数据
bond_analyzer.prepare_daily_data()
bond_analyzer.prepare_weekly_data()

# 生成交易信号
signals = bond_analyzer.generate_trading_signals()

print(f"\n日频信号数据形状: {signals['daily'].shape}")
print(f"周频信号数据形状: {signals['weekly'].shape}")

# 显示信号统计
daily_signals = signals['daily']
weekly_signals = signals['weekly']

print(f"\n日频信号统计:")
for signal_type in ['technical_signal', 'funding_signal', 'institutional_signal', 'comprehensive_signal']:
    if signal_type in daily_signals.columns:
        signal_rate = daily_signals[signal_type].mean()
        print(f"  {signal_type}: {signal_rate:.3f} ({signal_rate*100:.1f}%)")

print(f"\n周频信号统计:")
for signal_type in ['technical_signal', 'funding_signal', 'institutional_signal', 'comprehensive_signal']:
    if signal_type in weekly_signals.columns:
        signal_rate = weekly_signals[signal_type].mean()
        print(f"  {signal_type}: {signal_rate:.3f} ({signal_rate*100:.1f}%)")

def calculate_bond_portfolio_returns(signals_df, frequency='daily'):
    """计算债券投资组合收益率"""
    print(f"\n{'='*60}")
    print(f"💰 计算{frequency}债券投资组合收益率")
    print(f"{'='*60}")

    df = signals_df.copy()

    # 基准收益率：买入持有30年国债
    df['benchmark_return'] = df['bond_return']

    # 策略收益率计算
    strategies = ['technical_signal', 'funding_signal', 'institutional_signal', 'comprehensive_signal']

    for strategy in strategies:
        if strategy in df.columns:
            # 信号为1时做多债券，信号为0时空仓（获得无风险收益率，这里假设为0）
            df[f'{strategy}_return'] = np.where(
                df[strategy] == 1,
                df['bond_return'],  # 做多债券
                0.0                 # 空仓
            )

    # 计算累计收益率（复利）
    df['benchmark_cumret'] = (1 + df['benchmark_return']).cumprod() - 1

    for strategy in strategies:
        if f'{strategy}_return' in df.columns:
            df[f'{strategy}_cumret'] = (1 + df[f'{strategy}_return']).cumprod() - 1

    return df

def calculate_bond_performance_metrics(returns_df, frequency='daily'):
    """计算债券投资组合绩效指标"""
    print(f"\n{'='*60}")
    print(f"📊 计算{frequency}债券绩效指标")
    print(f"{'='*60}")

    strategies = ['technical_signal', 'funding_signal', 'institutional_signal', 'comprehensive_signal']

    metrics = {}

    # 计算年化因子
    periods_per_year = 252 if frequency == 'daily' else 52
    total_periods = len(returns_df)
    years = total_periods / periods_per_year

    print(f"分析期间: {years:.2f}年")

    # 基准指标
    benchmark_ret = returns_df['benchmark_return']
    total_return = returns_df['benchmark_cumret'].iloc[-1]
    annualized_total_return = (1 + total_return) ** (1/years) - 1

    metrics['benchmark'] = {
        'total_return': total_return,
        'annualized_total_return': annualized_total_return,
        'annualized_volatility': benchmark_ret.std() * np.sqrt(periods_per_year),
        'sharpe_ratio': (benchmark_ret.mean() / benchmark_ret.std()) * np.sqrt(periods_per_year) if benchmark_ret.std() > 0 else 0,
        'max_drawdown': calculate_max_drawdown(returns_df['benchmark_cumret']),
        'win_rate': (benchmark_ret > 0).mean()
    }

    # 策略指标
    for strategy in strategies:
        if f'{strategy}_return' in returns_df.columns:
            strategy_ret = returns_df[f'{strategy}_return']
            total_return = returns_df[f'{strategy}_cumret'].iloc[-1]
            annualized_total_return = (1 + total_return) ** (1/years) - 1

            metrics[strategy] = {
                'total_return': total_return,
                'annualized_total_return': annualized_total_return,
                'annualized_volatility': strategy_ret.std() * np.sqrt(periods_per_year),
                'sharpe_ratio': (strategy_ret.mean() / strategy_ret.std()) * np.sqrt(periods_per_year) if strategy_ret.std() > 0 else 0,
                'max_drawdown': calculate_max_drawdown(returns_df[f'{strategy}_cumret']),
                'win_rate': (strategy_ret > 0).mean(),
                'excess_return': total_return - returns_df['benchmark_cumret'].iloc[-1],
                'annualized_excess_return': annualized_total_return - metrics['benchmark']['annualized_total_return'],
                'information_ratio': (strategy_ret - benchmark_ret).mean() / (strategy_ret - benchmark_ret).std() * np.sqrt(periods_per_year) if (strategy_ret - benchmark_ret).std() > 0 else 0
            }

    return metrics

def calculate_max_drawdown(cumret_series):
    """计算最大回撤"""
    peak = cumret_series.expanding().max()
    drawdown = (cumret_series - peak) / (1 + peak)
    return drawdown.min()

def create_bond_performance_charts(daily_returns, weekly_returns, output_dir):
    """创建债券绩效图表"""
    print(f"\n{'='*60}")
    print("📈 创建债券绩效图表")
    print(f"{'='*60}")

    # 1. 日频累计收益率图
    plt.figure(figsize=(16, 10))

    plt.plot(daily_returns['date'], daily_returns['benchmark_cumret'] * 100,
             label='基准收益率(买入持有30Y国债)', linewidth=3, color='black', linestyle='-')

    strategies = ['technical_signal', 'funding_signal', 'institutional_signal', 'comprehensive_signal']
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
    strategy_names = ['技术面择时策略', '资金面择时策略', '机构行为择时策略', '综合择时策略']

    for i, strategy in enumerate(strategies):
        if f'{strategy}_cumret' in daily_returns.columns:
            plt.plot(daily_returns['date'], daily_returns[f'{strategy}_cumret'] * 100,
                    label=strategy_names[i], linewidth=2, color=colors[i])

    plt.xlabel('日期', fontsize=14, fontweight='bold')
    plt.ylabel('累计收益率 (%)', fontsize=14, fontweight='bold')
    plt.title('日频30Y国债择时策略累计收益率对比', fontsize=16, fontweight='bold', pad=20)
    plt.legend(loc='upper left', fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)

    # 添加收益率统计信息
    final_benchmark = daily_returns['benchmark_cumret'].iloc[-1] * 100
    plt.text(0.02, 0.98, f'基准最终收益率: {final_benchmark:.2f}%',
             transform=plt.gca().transAxes, fontsize=12, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    plt.tight_layout()
    plt.savefig(f'{output_dir}/日频30Y国债择时策略累计收益率对比.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✓ 日频30Y国债择时策略累计收益率对比.png")

    # 2. 周频累计收益率图
    plt.figure(figsize=(16, 10))

    plt.plot(weekly_returns['date'], weekly_returns['benchmark_cumret'] * 100,
             label='基准收益率(买入持有30Y国债)', linewidth=3, color='black', linestyle='-')

    for i, strategy in enumerate(strategies):
        if f'{strategy}_cumret' in weekly_returns.columns:
            plt.plot(weekly_returns['date'], weekly_returns[f'{strategy}_cumret'] * 100,
                    label=strategy_names[i], linewidth=2, color=colors[i])

    plt.xlabel('日期', fontsize=14, fontweight='bold')
    plt.ylabel('累计收益率 (%)', fontsize=14, fontweight='bold')
    plt.title('周频30Y国债择时策略累计收益率对比', fontsize=16, fontweight='bold', pad=20)
    plt.legend(loc='upper left', fontsize=12)
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)

    # 添加收益率统计信息
    final_benchmark = weekly_returns['benchmark_cumret'].iloc[-1] * 100
    plt.text(0.02, 0.98, f'基准最终收益率: {final_benchmark:.2f}%',
             transform=plt.gca().transAxes, fontsize=12, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))

    plt.tight_layout()
    plt.savefig(f'{output_dir}/周频30Y国债择时策略累计收益率对比.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✓ 周频30Y国债择时策略累计收益率对比.png")

    # 3. 收益率分布对比图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 8))

    # 日频收益率分布
    ax1.hist(daily_returns['benchmark_return'] * 100, bins=50, alpha=0.7,
             label='基准收益率', color='black', density=True)

    for i, strategy in enumerate(strategies):
        if f'{strategy}_return' in daily_returns.columns:
            ax1.hist(daily_returns[f'{strategy}_return'] * 100, bins=50, alpha=0.6,
                    label=strategy_names[i], color=colors[i], density=True)

    ax1.set_xlabel('日收益率 (%)', fontsize=12)
    ax1.set_ylabel('密度', fontsize=12)
    ax1.set_title('日频收益率分布对比', fontsize=14, fontweight='bold')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 周频收益率分布
    ax2.hist(weekly_returns['benchmark_return'] * 100, bins=30, alpha=0.7,
             label='基准收益率', color='black', density=True)

    for i, strategy in enumerate(strategies):
        if f'{strategy}_return' in weekly_returns.columns:
            ax2.hist(weekly_returns[f'{strategy}_return'] * 100, bins=30, alpha=0.6,
                    label=strategy_names[i], color=colors[i], density=True)

    ax2.set_xlabel('周收益率 (%)', fontsize=12)
    ax2.set_ylabel('密度', fontsize=12)
    ax2.set_title('周频收益率分布对比', fontsize=14, fontweight='bold')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig(f'{output_dir}/收益率分布对比图.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✓ 收益率分布对比图.png")

def save_bond_performance_excel(daily_metrics, weekly_metrics, daily_returns, weekly_returns, output_dir):
    """保存债券绩效Excel文件"""
    print(f"\n{'='*60}")
    print("📊 保存债券绩效Excel文件")
    print(f"{'='*60}")

    with pd.ExcelWriter(f'{output_dir}/30Y国债择时策略绩效分析报告.xlsx') as writer:

        # 日频绩效指标
        daily_metrics_df = pd.DataFrame(daily_metrics).T
        daily_metrics_df.index.name = '策略名称'
        # 转换为百分比显示
        for col in ['total_return', 'annualized_total_return', 'annualized_volatility', 'max_drawdown', 'win_rate', 'excess_return', 'annualized_excess_return']:
            if col in daily_metrics_df.columns:
                daily_metrics_df[col] = daily_metrics_df[col] * 100
        daily_metrics_df.to_excel(writer, sheet_name='日频绩效指标')

        # 周频绩效指标
        weekly_metrics_df = pd.DataFrame(weekly_metrics).T
        weekly_metrics_df.index.name = '策略名称'
        # 转换为百分比显示
        for col in ['total_return', 'annualized_total_return', 'annualized_volatility', 'max_drawdown', 'win_rate', 'excess_return', 'annualized_excess_return']:
            if col in weekly_metrics_df.columns:
                weekly_metrics_df[col] = weekly_metrics_df[col] * 100
        weekly_metrics_df.to_excel(writer, sheet_name='周频绩效指标')

        # 日频累计收益率
        daily_cumret_df = daily_returns[['date', 'benchmark_cumret', 'technical_signal_cumret',
                                        'funding_signal_cumret', 'institutional_signal_cumret',
                                        'comprehensive_signal_cumret']].copy()
        daily_cumret_df.columns = ['日期', '基准累计收益率', '技术面策略累计收益率',
                                  '资金面策略累计收益率', '机构行为策略累计收益率', '综合策略累计收益率']
        # 转换为百分比
        for col in daily_cumret_df.columns[1:]:
            daily_cumret_df[col] = daily_cumret_df[col] * 100
        daily_cumret_df.to_excel(writer, sheet_name='日频累计收益率', index=False)

        # 周频累计收益率
        weekly_cumret_df = weekly_returns[['date', 'benchmark_cumret', 'technical_signal_cumret',
                                          'funding_signal_cumret', 'institutional_signal_cumret',
                                          'comprehensive_signal_cumret']].copy()
        weekly_cumret_df.columns = ['日期', '基准累计收益率', '技术面策略累计收益率',
                                   '资金面策略累计收益率', '机构行为策略累计收益率', '综合策略累计收益率']
        # 转换为百分比
        for col in weekly_cumret_df.columns[1:]:
            weekly_cumret_df[col] = weekly_cumret_df[col] * 100
        weekly_cumret_df.to_excel(writer, sheet_name='周频累计收益率', index=False)

        # 30Y收益率数据
        bond_yield_df = daily_returns[['date', '30y']].copy()
        bond_yield_df.columns = ['日期', '30Y国债收益率(%)']
        bond_yield_df.to_excel(writer, sheet_name='30Y国债收益率', index=False)

    print("✓ 30Y国债择时策略绩效分析报告.xlsx")

# 计算债券收益率
daily_returns = calculate_bond_portfolio_returns(daily_signals, 'daily')
weekly_returns = calculate_bond_portfolio_returns(weekly_signals, 'weekly')

# 计算绩效指标
daily_metrics = calculate_bond_performance_metrics(daily_returns, 'daily')
weekly_metrics = calculate_bond_performance_metrics(weekly_returns, 'weekly')

# 创建图表
create_bond_performance_charts(daily_returns, weekly_returns, output_dir)

# 保存Excel文件
save_bond_performance_excel(daily_metrics, weekly_metrics, daily_returns, weekly_returns, output_dir)

def generate_bond_analysis_report(daily_metrics, weekly_metrics, daily_returns, weekly_returns, output_dir):
    """生成债券分析报告"""
    print(f"\n{'='*60}")
    print("📋 生成债券分析报告")
    print(f"{'='*60}")

    # 计算分析期间
    daily_years = len(daily_returns) / 252
    weekly_years = len(weekly_returns) / 52

    report = f"""
🏦 30Y国债择时策略专业分析报告
=====================================
基于修正久期的债券收益率计算

📊 分析概况
- 30Y国债修正久期: 18.5年
- 日频分析期间: {daily_years:.2f}年 ({len(daily_returns)}个交易日)
- 周频分析期间: {weekly_years:.2f}年 ({len(weekly_returns)}个交易周)
- 30Y收益率范围: {daily_returns['30y'].min():.3f}% - {daily_returns['30y'].max():.3f}%

💰 基准表现分析

日频基准(买入持有30Y国债):
- 累计收益率: {daily_metrics['benchmark']['total_return']*100:.2f}%
- 年化收益率: {daily_metrics['benchmark']['annualized_total_return']*100:.2f}%
- 年化波动率: {daily_metrics['benchmark']['annualized_volatility']*100:.2f}%
- 夏普比率: {daily_metrics['benchmark']['sharpe_ratio']:.3f}
- 最大回撤: {daily_metrics['benchmark']['max_drawdown']*100:.2f}%
- 胜率: {daily_metrics['benchmark']['win_rate']*100:.1f}%

周频基准(买入持有30Y国债):
- 累计收益率: {weekly_metrics['benchmark']['total_return']*100:.2f}%
- 年化收益率: {weekly_metrics['benchmark']['annualized_total_return']*100:.2f}%
- 年化波动率: {weekly_metrics['benchmark']['annualized_volatility']*100:.2f}%
- 夏普比率: {weekly_metrics['benchmark']['sharpe_ratio']:.3f}
- 最大回撤: {weekly_metrics['benchmark']['max_drawdown']*100:.2f}%
- 胜率: {weekly_metrics['benchmark']['win_rate']*100:.1f}%

🎯 择时策略表现分析

日频择时策略:
"""

    # 日频策略分析
    strategies = ['technical_signal', 'funding_signal', 'institutional_signal', 'comprehensive_signal']
    strategy_names = ['技术面择时策略', '资金面择时策略', '机构行为择时策略', '综合择时策略']

    best_daily_strategy = None
    best_daily_return = -999

    for i, strategy in enumerate(strategies):
        if strategy in daily_metrics:
            metrics = daily_metrics[strategy]
            report += f"""
{strategy_names[i]}:
- 累计收益率: {metrics['total_return']*100:.2f}%
- 年化收益率: {metrics['annualized_total_return']*100:.2f}%
- 年化波动率: {metrics['annualized_volatility']*100:.2f}%
- 夏普比率: {metrics['sharpe_ratio']:.3f}
- 最大回撤: {metrics['max_drawdown']*100:.2f}%
- 胜率: {metrics['win_rate']*100:.1f}%
- 超额收益: {metrics['excess_return']*100:.2f}%
- 信息比率: {metrics['information_ratio']:.3f}
"""
            if metrics['annualized_total_return'] > best_daily_return:
                best_daily_return = metrics['annualized_total_return']
                best_daily_strategy = strategy_names[i]

    report += f"""
周频择时策略:
"""

    # 周频策略分析
    best_weekly_strategy = None
    best_weekly_return = -999

    for i, strategy in enumerate(strategies):
        if strategy in weekly_metrics:
            metrics = weekly_metrics[strategy]
            report += f"""
{strategy_names[i]}:
- 累计收益率: {metrics['total_return']*100:.2f}%
- 年化收益率: {metrics['annualized_total_return']*100:.2f}%
- 年化波动率: {metrics['annualized_volatility']*100:.2f}%
- 夏普比率: {metrics['sharpe_ratio']:.3f}
- 最大回撤: {metrics['max_drawdown']*100:.2f}%
- 胜率: {metrics['win_rate']*100:.1f}%
- 超额收益: {metrics['excess_return']*100:.2f}%
- 信息比率: {metrics['information_ratio']:.3f}
"""
            if metrics['annualized_total_return'] > best_weekly_return:
                best_weekly_return = metrics['annualized_total_return']
                best_weekly_strategy = strategy_names[i]

    report += f"""
🏆 最佳策略识别

最佳日频策略: {best_daily_strategy}
年化收益率: {best_daily_return*100:.2f}%

最佳周频策略: {best_weekly_strategy}
年化收益率: {best_weekly_return*100:.2f}%

💡 专业投资建议

1. 债券久期风险管理:
   - 30Y国债修正久期18.5年，收益率每变动1%，债券价格反向变动约18.5%
   - 当前收益率水平下，久期风险较高，需要谨慎管理

2. 择时策略选择:
   - 基于分析结果，{best_weekly_strategy}在周频下表现最佳
   - 建议采用周频调仓，降低交易成本

3. 风险控制建议:
   - 设置止损线：单次亏损超过2%时减仓
   - 仓位管理：择时策略仓位不超过60%
   - 久期管理：根据收益率趋势动态调整久期暴露

4. 市场环境适应性:
   - 技术面策略在趋势市场中表现较好
   - 资金面策略在货币政策转向期敏感度高
   - 机构行为策略具有一定的前瞻性

⚠️ 风险提示

1. 久期风险: 30Y国债久期较长，利率敏感性高
2. 流动性风险: 择时策略可能面临流动性不足的风险
3. 模型风险: 历史表现不代表未来收益
4. 市场风险: 极端市场条件下策略可能失效

📈 收益率计算说明

债券价格收益率 = -修正久期 × 收益率变动幅度
例如：30Y收益率从2.000%下降到1.950%（下降0.05%）
债券价格收益率 = -18.5 × (-0.05%) = +0.925%

该专业分析为30Y国债投资提供了基于久期的科学决策框架！
"""

    # 保存报告
    with open(f'{output_dir}/30Y国债择时策略分析报告.txt', 'w', encoding='utf-8') as f:
        f.write(report)

    print(report)
    print(f"\n✅ 债券分析报告已保存: {output_dir}/30Y国债择时策略分析报告.txt")

# 生成债券分析报告
generate_bond_analysis_report(daily_metrics, weekly_metrics, daily_returns, weekly_returns, output_dir)

print(f"\n" + "="*100)
print(f"🏦 30Y国债择时策略专业分析完成！")
print(f"="*100)

# 输出关键结果
print(f"📊 分析结果概览:")
print(f"  日频数据样本: {len(daily_returns)}个交易日")
print(f"  周频数据样本: {len(weekly_returns)}个交易周")
print(f"  30Y国债修正久期: 18.5年")

# 找出最佳策略
strategies = ['technical_signal', 'funding_signal', 'institutional_signal', 'comprehensive_signal']
strategy_names_map = {
    'technical_signal': '技术面择时策略',
    'funding_signal': '资金面择时策略',
    'institutional_signal': '机构行为择时策略',
    'comprehensive_signal': '综合择时策略'
}

best_daily_strategy = None
best_daily_return = -999
for strategy in strategies:
    if strategy in daily_metrics and daily_metrics[strategy]['annualized_total_return'] > best_daily_return:
        best_daily_return = daily_metrics[strategy]['annualized_total_return']
        best_daily_strategy = strategy

best_weekly_strategy = None
best_weekly_return = -999
for strategy in strategies:
    if strategy in weekly_metrics and weekly_metrics[strategy]['annualized_total_return'] > best_weekly_return:
        best_weekly_return = weekly_metrics[strategy]['annualized_total_return']
        best_weekly_strategy = strategy

if best_daily_strategy:
    print(f"🏆 最佳日频策略: {strategy_names_map[best_daily_strategy]}")
    print(f"📈 日频年化收益率: {daily_metrics[best_daily_strategy]['annualized_total_return']*100:.2f}%")
    print(f"📊 日频夏普比率: {daily_metrics[best_daily_strategy]['sharpe_ratio']:.3f}")
    print(f"📉 日频最大回撤: {daily_metrics[best_daily_strategy]['max_drawdown']*100:.2f}%")

if best_weekly_strategy:
    print(f"🏆 最佳周频策略: {strategy_names_map[best_weekly_strategy]}")
    print(f"📈 周频年化收益率: {weekly_metrics[best_weekly_strategy]['annualized_total_return']*100:.2f}%")
    print(f"📊 周频夏普比率: {weekly_metrics[best_weekly_strategy]['sharpe_ratio']:.3f}")
    print(f"📉 周频最大回撤: {weekly_metrics[best_weekly_strategy]['max_drawdown']*100:.2f}%")

# 基准表现
print(f"📊 基准表现:")
print(f"  日频基准年化收益率: {daily_metrics['benchmark']['annualized_total_return']*100:.2f}%")
print(f"  周频基准年化收益率: {weekly_metrics['benchmark']['annualized_total_return']*100:.2f}%")

print(f"📁 输出目录: {output_dir}")
print(f"="*100)
print(f"🎯 生成的文件:")
print(f"  图表 (3张):")
print(f"    1. 日频30Y国债择时策略累计收益率对比.png")
print(f"    2. 周频30Y国债择时策略累计收益率对比.png")
print(f"    3. 收益率分布对比图.png")
print(f"  Excel (1个):")
print(f"    1. 30Y国债择时策略绩效分析报告.xlsx")
print(f"  报告 (1个):")
print(f"    1. 30Y国债择时策略分析报告.txt")
print(f"="*100)
