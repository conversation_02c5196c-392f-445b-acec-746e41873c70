import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy import stats
import warnings
import os
from datetime import datetime, timedelta
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.metrics import accuracy_score
from sklearn.preprocessing import StandardScaler
warnings.filterwarnings('ignore')

# 修复中文字体显示
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'Microsoft YaHei']
plt.rcParams['axes.unicode_minus'] = False
plt.rcParams['figure.figsize'] = (14, 10)
plt.rcParams['savefig.dpi'] = 300

# 创建输出文件夹
output_dir = '/Users/<USER>/Desktop/择时累计收益率'
optimized_dir = '/Users/<USER>/Desktop/择时累计收益率-优化'
for dir_path in [output_dir, optimized_dir]:
    if not os.path.exists(dir_path):
        os.makedirs(dir_path)

print("=" * 100)
print("🏦 固定收益债券基金经理 - 调集所有算力优化分析")
print("基于TOP因子的深度策略优化")
print("=" * 100)

# 加载数据
file_path = '/Users/<USER>/Desktop/择时胜率0527.xlsx'
try:
    df = pd.read_excel(file_path)
    print(f"✓ 数据加载成功，数据形状: {df.shape}")
except Exception as e:
    print(f"✗ 数据加载失败: {e}")
    exit()

# 数据预处理
df_processed = df.fillna(method='ffill').fillna(method='bfill').copy()
if 'Unnamed: 0' in df_processed.columns:
    df_processed = df_processed.rename(columns={'Unnamed: 0': '日期'})

# 确保日期列为datetime格式
if '日期' in df_processed.columns:
    df_processed['日期'] = pd.to_datetime(df_processed['日期'])
    df_processed = df_processed.sort_values('日期').reset_index(drop=True)
else:
    df_processed['日期'] = pd.date_range(start='2024-01-02', periods=len(df_processed), freq='D')

print(f"数据时间范围: {df_processed['日期'].min()} 到 {df_processed['日期'].max()}")
print(f"30Y收益率范围: {df_processed['30y'].min():.3f}% 到 {df_processed['30y'].max():.3f}%")

class OptimizedBondStrategyManager:
    """优化的债券策略管理器"""

    def __init__(self, data, duration_30y=18.5):
        """
        初始化优化策略管理器
        duration_30y: 30年国债的修正久期
        """
        self.data = data.copy()
        self.duration_30y = duration_30y
        self.daily_data = None
        self.weekly_data = None
        self.optimized_signals = {}
        self.performance_metrics = {}

        # TOP因子定义（基于您提供的排名）
        self.top_factors = {
            'institutional_factors': ['农商行30Y净买入', '公募30Y净买入'],
            'technical_factors': ['偏离度（偏离均线）'],
            'engineered_factors': ['偏离度动量', '收益率曲线斜率动量', '商品波动率_10日',
                                 '极端偏离状态', '30y动量_3日', '资金面利差', '基本面技术面交互']
        }

        print(f"使用30年国债修正久期: {self.duration_30y}年")
        print(f"TOP因子分类: {len(self.top_factors)}类")

    def prepare_optimized_data(self):
        """准备优化的数据和工程因子"""
        print(f"\n{'='*60}")
        print("🔬 准备优化数据和高级工程因子")
        print(f"{'='*60}")

        df = self.data.copy()

        # 计算30Y收益率变动和债券收益率
        df['30y_change'] = df['30y'].diff()
        df['bond_return'] = -self.duration_30y * df['30y_change'] / 100

        # 创建高级工程因子（基于TOP因子）
        print("创建高级工程因子...")

        # 1. 偏离度系列增强
        if '偏离度（偏离均线）' in df.columns:
            df['偏离度动量'] = df['偏离度（偏离均线）'].diff(1)
            df['偏离度加速度'] = df['偏离度动量'].diff(1)
            df['偏离度绝对值'] = abs(df['偏离度（偏离均线）'])
            df['偏离度标准化'] = df['偏离度（偏离均线）'] / df['偏离度（偏离均线）'].rolling(20).std()
            df['极端偏离状态'] = (abs(df['偏离度（偏离均线）']) > df['偏离度绝对值'].rolling(20).quantile(0.8)).astype(int)

        # 2. 30y动量系列
        df['30y动量_3日'] = df['30y'].pct_change(3)
        df['30y动量_5日'] = df['30y'].pct_change(5)
        df['30y动量_10日'] = df['30y'].pct_change(10)

        # 3. 收益率曲线因子
        if '10y' in df.columns and '30y' in df.columns:
            df['收益率曲线斜率'] = df['30y'] - df['10y']
            df['收益率曲线斜率动量'] = df['收益率曲线斜率'].diff(1)
            df['收益率曲线斜率波动率'] = df['收益率曲线斜率'].rolling(10).std()

        # 4. 商品波动率因子
        commodity_cols = ['水泥价格指数', '建材综合指数', '南华金属指数', '南华能化指数', '南华工业品指数']
        available_commodity = [col for col in commodity_cols if col in df.columns]
        if len(available_commodity) >= 2:
            df['商品综合指数'] = df[available_commodity].mean(axis=1)
            df['商品波动率_5日'] = df['商品综合指数'].rolling(5).std()
            df['商品波动率_10日'] = df['商品综合指数'].rolling(10).std()
            df['商品波动率_20日'] = df['商品综合指数'].rolling(20).std()

        # 5. 资金面利差因子
        if 'R007' in df.columns and 'DR007' in df.columns:
            df['资金面利差'] = df['R007'] - df['DR007']
            df['资金面利差动量'] = df['资金面利差'].diff(1)
            df['资金面紧张度'] = (df['R007'] - df['R007'].rolling(20).mean()) / df['R007'].rolling(20).std()

        # 6. 基本面技术面交互因子
        if '水泥价格指数' in df.columns and '偏离度（偏离均线）' in df.columns:
            df['基本面技术面交互'] = df['水泥价格指数'] * df['偏离度（偏离均线）']
            df['基本面技术面交互标准化'] = (df['基本面技术面交互'] - df['基本面技术面交互'].rolling(20).mean()) / df['基本面技术面交互'].rolling(20).std()

        # 7. 机构行为增强因子
        institutional_cols = [col for col in df.columns if '净买入' in col]
        if len(institutional_cols) >= 2:
            df['机构净买入总和'] = df[institutional_cols].sum(axis=1)
            df['机构净买入动量'] = df['机构净买入总和'].diff(1)
            df['机构行为一致性'] = (df[institutional_cols] > 0).sum(axis=1)
            df['机构行为强度'] = df['机构净买入总和'] / df['机构净买入总和'].rolling(20).mean()

        # 8. 市场状态因子
        df['高波动状态'] = (df['30y'].rolling(10).std() > df['30y'].rolling(10).std().rolling(20).quantile(0.7)).astype(int)
        df['趋势强度'] = abs(df['30y'].rolling(5).mean() - df['30y'].rolling(20).mean())

        # 删除包含NaN的行
        df = df.dropna()

        self.daily_data = df
        print(f"优化后日频数据样本数量: {len(df)}")
        print(f"债券收益率统计: 均值={df['bond_return'].mean()*100:.3f}%, 标准差={df['bond_return'].std()*100:.3f}%")

        # 准备周频数据
        self._prepare_weekly_data()

    def _prepare_weekly_data(self):
        """准备周频数据"""
        print("准备周频数据...")

        # 设置日期为索引
        df_daily = self.daily_data.set_index('日期')

        # 添加周标识
        df_daily['week'] = df_daily.index.to_series().dt.to_period('W')

        # 定义聚合方法
        agg_methods = {}
        price_factors = ['30y', '10y', '30-10y', 'R007', 'DR007', '偏离度（偏离均线）']
        volume_factors = ['30Y成交量', '成交量:DR007', '成交量:R007']
        institutional_factors = [col for col in df_daily.columns if '净买入' in col]

        for col in df_daily.columns:
            if col == 'week':
                continue
            elif col in price_factors:
                agg_methods[col] = 'last'
            elif col in volume_factors + institutional_factors:
                agg_methods[col] = 'sum'
            else:
                agg_methods[col] = 'last'

        # 按周聚合
        df_weekly = df_daily.groupby('week').agg(agg_methods)
        df_weekly.reset_index(inplace=True)
        df_weekly['日期'] = df_weekly['week'].dt.end_time
        df_weekly = df_weekly.drop('week', axis=1)

        # 计算周度债券收益率
        df_weekly['30y_change'] = df_weekly['30y'].diff()
        df_weekly['bond_return'] = -self.duration_30y * df_weekly['30y_change'] / 100

        # 删除包含NaN的行
        df_weekly = df_weekly.dropna()

        self.weekly_data = df_weekly
        print(f"周频数据样本数量: {len(df_weekly)}")

    def generate_optimized_signals(self):
        """生成优化的交易信号"""
        print(f"\n{'='*60}")
        print("🚀 生成优化交易信号")
        print(f"{'='*60}")

        # 日频优化信号
        daily_signals = self._generate_optimized_daily_signals()

        # 周频优化信号
        weekly_signals = self._generate_optimized_weekly_signals()

        self.optimized_signals = {
            'daily': daily_signals,
            'weekly': weekly_signals
        }

        return self.optimized_signals

    def _generate_optimized_daily_signals(self):
        """生成优化的日频交易信号"""
        print("生成优化日频信号...")

        df = self.daily_data.copy()

        # 策略1: TOP因子精准择时策略
        top_score = 0
        max_top_score = 0

        # 机构行为因子（权重最高：40%）
        if '农商行30Y净买入' in df.columns:
            factor_median = df['农商行30Y净买入'].median()
            signal = (df['农商行30Y净买入'] > factor_median).astype(int)
            top_score += signal * 8
            max_top_score += 8

        if '公募30Y净买入' in df.columns:
            factor_median = df['公募30Y净买入'].median()
            signal = (df['公募30Y净买入'] > factor_median).astype(int)
            top_score += signal * 6
            max_top_score += 6

        # 技术面因子（权重：30%）
        if '偏离度动量' in df.columns:
            factor_median = df['偏离度动量'].median()
            # 偏离度动量为负时看涨债券（收益率下降）
            signal = (df['偏离度动量'] <= factor_median).astype(int)
            top_score += signal * 6
            max_top_score += 6

        if '偏离度（偏离均线）' in df.columns:
            factor_median = df['偏离度（偏离均线）'].median()
            # 偏离度为正且较大时看涨债券
            signal = (df['偏离度（偏离均线）'] > factor_median).astype(int)
            top_score += signal * 4
            max_top_score += 4

        # 动量因子（权重：20%）
        if '30y动量_3日' in df.columns:
            factor_median = df['30y动量_3日'].median()
            # 收益率动量为负时看涨债券
            signal = (df['30y动量_3日'] <= factor_median).astype(int)
            top_score += signal * 4
            max_top_score += 4

        # 市场状态因子（权重：10%）
        if '极端偏离状态' in df.columns:
            signal = df['极端偏离状态']
            top_score += signal * 2
            max_top_score += 2

        # 策略2: 动态阈值优化策略
        volatility = df['30y'].rolling(10).std()
        high_vol_periods = volatility > volatility.median()

        dynamic_threshold_high = max_top_score * 0.65  # 高波动期提高阈值
        dynamic_threshold_low = max_top_score * 0.45   # 低波动期降低阈值

        dynamic_signal = np.where(
            high_vol_periods,
            (top_score > dynamic_threshold_high).astype(int),
            (top_score > dynamic_threshold_low).astype(int)
        )

        # 策略3: 机器学习增强策略
        ml_factors = ['农商行30Y净买入', '偏离度动量', '公募30Y净买入', '30y动量_3日', '偏离度（偏离均线）']
        available_ml_factors = [f for f in ml_factors if f in df.columns]

        if len(available_ml_factors) >= 3:
            X = df[available_ml_factors].fillna(method='ffill')
            y = (df['bond_return'] > 0).astype(int)  # 债券收益为正的信号

            # 时间序列分割
            train_size = int(len(X) * 0.7)
            X_train, X_test = X[:train_size], X[train_size:]
            y_train, y_test = y[:train_size], y[train_size:]

            # 标准化
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)

            # 随机森林模型
            rf_model = RandomForestClassifier(n_estimators=100, random_state=42, max_depth=5)
            rf_model.fit(X_train_scaled, y_train)

            # 预测
            ml_signal = np.zeros(len(df))
            ml_signal[train_size:] = rf_model.predict(X_test_scaled)
        else:
            ml_signal = np.zeros(len(df))

        # 策略4: 多时间框架融合策略
        short_term_signal = (df['30y动量_3日'] <= df['30y动量_3日'].median()).astype(int) if '30y动量_3日' in df.columns else 0
        medium_term_signal = (df['偏离度（偏离均线）'] > df['偏离度（偏离均线）'].median()).astype(int) if '偏离度（偏离均线）' in df.columns else 0
        long_term_signal = (df['机构净买入总和'] > df['机构净买入总和'].median()).astype(int) if '机构净买入总和' in df.columns else 0

        multi_timeframe_signal = (short_term_signal * 0.5 + medium_term_signal * 0.3 + long_term_signal * 0.2 > 0.5).astype(int)

        # 生成信号
        signals = {
            'top_factor_signal': (top_score > max_top_score/2).astype(int),
            'dynamic_threshold_signal': dynamic_signal,
            'ml_enhanced_signal': ml_signal,
            'multi_timeframe_signal': multi_timeframe_signal,
            'date': df['日期'],
            '30y': df['30y'],
            'bond_return': df['bond_return'],
            'top_score': top_score,
            'max_top_score': max_top_score,
            'volatility_regime': high_vol_periods.astype(int)
        }

        return pd.DataFrame(signals)

    def _generate_optimized_weekly_signals(self):
        """生成优化的周频交易信号"""
        print("生成优化周频信号...")

        df = self.weekly_data.copy()

        # 周频策略相对保守，使用更高的确信度
        weekly_score = 0
        max_weekly_score = 0

        # 机构行为因子（周度累计效应）
        if '农商行30Y净买入' in df.columns:
            factor_median = df['农商行30Y净买入'].median()
            signal = (df['农商行30Y净买入'] > factor_median).astype(int)
            weekly_score += signal * 10
            max_weekly_score += 10

        if '公募30Y净买入' in df.columns:
            factor_median = df['公募30Y净买入'].median()
            signal = (df['公募30Y净买入'] > factor_median).astype(int)
            weekly_score += signal * 8
            max_weekly_score += 8

        # 技术面因子
        if '偏离度（偏离均线）' in df.columns:
            factor_median = df['偏离度（偏离均线）'].median()
            signal = (df['偏离度（偏离均线）'] > factor_median).astype(int)
            weekly_score += signal * 6
            max_weekly_score += 6

        # 周频策略使用更高阈值（60%）
        weekly_signal = (weekly_score > max_weekly_score * 0.6).astype(int)

        # 生成信号
        signals = {
            'weekly_optimized_signal': weekly_signal,
            'date': df['日期'],
            '30y': df['30y'],
            'bond_return': df['bond_return'],
            'weekly_score': weekly_score,
            'max_weekly_score': max_weekly_score
        }

        return pd.DataFrame(signals)

# 初始化优化策略管理器
strategy_manager = OptimizedBondStrategyManager(df_processed, duration_30y=18.5)

# 准备优化数据
strategy_manager.prepare_optimized_data()

# 生成优化信号
optimized_signals = strategy_manager.generate_optimized_signals()

print(f"\n优化日频信号数据形状: {optimized_signals['daily'].shape}")
print(f"优化周频信号数据形状: {optimized_signals['weekly'].shape}")

# 显示优化信号统计
daily_signals = optimized_signals['daily']
weekly_signals = optimized_signals['weekly']

print(f"\n优化日频信号统计:")
for signal_type in ['top_factor_signal', 'dynamic_threshold_signal', 'ml_enhanced_signal', 'multi_timeframe_signal']:
    if signal_type in daily_signals.columns:
        signal_rate = daily_signals[signal_type].mean()
        print(f"  {signal_type}: {signal_rate:.3f} ({signal_rate*100:.1f}%)")

print(f"\n优化周频信号统计:")
for signal_type in ['weekly_optimized_signal']:
    if signal_type in weekly_signals.columns:
        signal_rate = weekly_signals[signal_type].mean()
        print(f"  {signal_type}: {signal_rate:.3f} ({signal_rate*100:.1f}%)")

def calculate_optimized_portfolio_returns(signals_df, frequency='daily'):
    """计算优化投资组合收益率"""
    print(f"\n{'='*60}")
    print(f"💰 计算优化{frequency}投资组合收益率")
    print(f"{'='*60}")

    df = signals_df.copy()

    # 基准收益率：买入持有30年国债
    df['benchmark_return'] = df['bond_return']

    # 优化策略收益率计算
    if frequency == 'daily':
        strategies = ['top_factor_signal', 'dynamic_threshold_signal', 'ml_enhanced_signal', 'multi_timeframe_signal']
    else:
        strategies = ['weekly_optimized_signal']

    for strategy in strategies:
        if strategy in df.columns:
            # 信号为1时做多债券，信号为0时空仓
            df[f'{strategy}_return'] = np.where(
                df[strategy] == 1,
                df['bond_return'],  # 做多债券
                0.0                 # 空仓
            )

    # 计算累计收益率（复利）
    df['benchmark_cumret'] = (1 + df['benchmark_return']).cumprod() - 1

    for strategy in strategies:
        if f'{strategy}_return' in df.columns:
            df[f'{strategy}_cumret'] = (1 + df[f'{strategy}_return']).cumprod() - 1

    return df

def calculate_optimized_performance_metrics(returns_df, frequency='daily'):
    """计算优化投资组合绩效指标"""
    print(f"\n{'='*60}")
    print(f"📊 计算优化{frequency}绩效指标")
    print(f"{'='*60}")

    if frequency == 'daily':
        strategies = ['top_factor_signal', 'dynamic_threshold_signal', 'ml_enhanced_signal', 'multi_timeframe_signal']
    else:
        strategies = ['weekly_optimized_signal']

    metrics = {}

    # 计算年化因子
    periods_per_year = 252 if frequency == 'daily' else 52
    total_periods = len(returns_df)
    years = total_periods / periods_per_year

    print(f"分析期间: {years:.2f}年")

    # 基准指标
    benchmark_ret = returns_df['benchmark_return']
    total_return = returns_df['benchmark_cumret'].iloc[-1]
    annualized_total_return = (1 + total_return) ** (1/years) - 1

    metrics['benchmark'] = {
        'total_return': total_return,
        'annualized_total_return': annualized_total_return,
        'annualized_volatility': benchmark_ret.std() * np.sqrt(periods_per_year),
        'sharpe_ratio': (benchmark_ret.mean() / benchmark_ret.std()) * np.sqrt(periods_per_year) if benchmark_ret.std() > 0 else 0,
        'max_drawdown': calculate_max_drawdown(returns_df['benchmark_cumret']),
        'win_rate': (benchmark_ret > 0).mean(),
        'calmar_ratio': annualized_total_return / abs(calculate_max_drawdown(returns_df['benchmark_cumret'])) if calculate_max_drawdown(returns_df['benchmark_cumret']) != 0 else 0
    }

    # 策略指标
    for strategy in strategies:
        if f'{strategy}_return' in returns_df.columns:
            strategy_ret = returns_df[f'{strategy}_return']
            total_return = returns_df[f'{strategy}_cumret'].iloc[-1]
            annualized_total_return = (1 + total_return) ** (1/years) - 1

            metrics[strategy] = {
                'total_return': total_return,
                'annualized_total_return': annualized_total_return,
                'annualized_volatility': strategy_ret.std() * np.sqrt(periods_per_year),
                'sharpe_ratio': (strategy_ret.mean() / strategy_ret.std()) * np.sqrt(periods_per_year) if strategy_ret.std() > 0 else 0,
                'max_drawdown': calculate_max_drawdown(returns_df[f'{strategy}_cumret']),
                'win_rate': (strategy_ret > 0).mean(),
                'excess_return': total_return - returns_df['benchmark_cumret'].iloc[-1],
                'annualized_excess_return': annualized_total_return - metrics['benchmark']['annualized_total_return'],
                'information_ratio': (strategy_ret - benchmark_ret).mean() / (strategy_ret - benchmark_ret).std() * np.sqrt(periods_per_year) if (strategy_ret - benchmark_ret).std() > 0 else 0,
                'calmar_ratio': annualized_total_return / abs(calculate_max_drawdown(returns_df[f'{strategy}_cumret'])) if calculate_max_drawdown(returns_df[f'{strategy}_cumret']) != 0 else 0,
                'sortino_ratio': (strategy_ret.mean() / strategy_ret[strategy_ret < 0].std()) * np.sqrt(periods_per_year) if len(strategy_ret[strategy_ret < 0]) > 0 else 0
            }

    return metrics

def calculate_max_drawdown(cumret_series):
    """计算最大回撤"""
    peak = cumret_series.expanding().max()
    drawdown = (cumret_series - peak) / (1 + peak)
    return drawdown.min()

def create_optimized_performance_charts(daily_returns, weekly_returns, output_dir):
    """创建优化绩效图表"""
    print(f"\n{'='*60}")
    print("📈 创建优化绩效图表")
    print(f"{'='*60}")

    # 1. 日频优化策略累计收益率图
    plt.figure(figsize=(18, 12))

    plt.plot(daily_returns['date'], daily_returns['benchmark_cumret'] * 100,
             label='基准收益率(买入持有30Y国债)', linewidth=4, color='black', linestyle='-')

    strategies = ['top_factor_signal', 'dynamic_threshold_signal', 'ml_enhanced_signal', 'multi_timeframe_signal']
    colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4']
    strategy_names = ['TOP因子精准择时', '动态阈值优化', '机器学习增强', '多时间框架融合']

    for i, strategy in enumerate(strategies):
        if f'{strategy}_cumret' in daily_returns.columns:
            plt.plot(daily_returns['date'], daily_returns[f'{strategy}_cumret'] * 100,
                    label=strategy_names[i], linewidth=3, color=colors[i])

    plt.xlabel('日期', fontsize=16, fontweight='bold')
    plt.ylabel('累计收益率 (%)', fontsize=16, fontweight='bold')
    plt.title('日频优化30Y国债择时策略累计收益率对比', fontsize=18, fontweight='bold', pad=20)
    plt.legend(loc='upper left', fontsize=14)
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)

    # 添加收益率统计信息
    final_benchmark = daily_returns['benchmark_cumret'].iloc[-1] * 100
    best_strategy_return = max([daily_returns[f'{s}_cumret'].iloc[-1] * 100 for s in strategies if f'{s}_cumret' in daily_returns.columns])
    plt.text(0.02, 0.98, f'基准最终收益率: {final_benchmark:.2f}%\n最佳策略收益率: {best_strategy_return:.2f}%',
             transform=plt.gca().transAxes, fontsize=14, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

    plt.tight_layout()
    plt.savefig(f'{output_dir}/日频优化30Y国债择时策略累计收益率对比.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✓ 日频优化30Y国债择时策略累计收益率对比.png")

    # 2. 周频优化策略累计收益率图
    plt.figure(figsize=(18, 12))

    plt.plot(weekly_returns['date'], weekly_returns['benchmark_cumret'] * 100,
             label='基准收益率(买入持有30Y国债)', linewidth=4, color='black', linestyle='-')

    if 'weekly_optimized_signal_cumret' in weekly_returns.columns:
        plt.plot(weekly_returns['date'], weekly_returns['weekly_optimized_signal_cumret'] * 100,
                label='周频优化择时策略', linewidth=3, color='#FF6B6B')

    plt.xlabel('日期', fontsize=16, fontweight='bold')
    plt.ylabel('累计收益率 (%)', fontsize=16, fontweight='bold')
    plt.title('周频优化30Y国债择时策略累计收益率对比', fontsize=18, fontweight='bold', pad=20)
    plt.legend(loc='upper left', fontsize=14)
    plt.grid(True, alpha=0.3)
    plt.xticks(rotation=45)

    # 添加收益率统计信息
    final_benchmark = weekly_returns['benchmark_cumret'].iloc[-1] * 100
    final_strategy = weekly_returns['weekly_optimized_signal_cumret'].iloc[-1] * 100 if 'weekly_optimized_signal_cumret' in weekly_returns.columns else 0
    plt.text(0.02, 0.98, f'基准最终收益率: {final_benchmark:.2f}%\n策略最终收益率: {final_strategy:.2f}%',
             transform=plt.gca().transAxes, fontsize=14, verticalalignment='top',
             bbox=dict(boxstyle='round', facecolor='lightgreen', alpha=0.8))

    plt.tight_layout()
    plt.savefig(f'{output_dir}/周频优化30Y国债择时策略累计收益率对比.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✓ 周频优化30Y国债择时策略累计收益率对比.png")

    # 3. 策略对比雷达图
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(20, 10), subplot_kw=dict(projection='polar'))

    # 日频策略雷达图
    daily_metrics = calculate_optimized_performance_metrics(daily_returns, 'daily')
    categories = ['年化收益率', '夏普比率', '胜率', '信息比率', 'Calmar比率']

    angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
    angles += angles[:1]  # 闭合图形

    for i, strategy in enumerate(strategies):
        if strategy in daily_metrics:
            values = [
                daily_metrics[strategy]['annualized_total_return'] * 100,
                daily_metrics[strategy]['sharpe_ratio'],
                daily_metrics[strategy]['win_rate'] * 100,
                daily_metrics[strategy]['information_ratio'],
                daily_metrics[strategy]['calmar_ratio']
            ]
            values += values[:1]  # 闭合图形

            ax1.plot(angles, values, 'o-', linewidth=2, label=strategy_names[i], color=colors[i])
            ax1.fill(angles, values, alpha=0.25, color=colors[i])

    ax1.set_xticks(angles[:-1])
    ax1.set_xticklabels(categories, fontsize=12)
    ax1.set_title('日频优化策略绩效雷达图', fontsize=16, fontweight='bold', pad=20)
    ax1.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    ax1.grid(True)

    # 周频策略雷达图
    weekly_metrics = calculate_optimized_performance_metrics(weekly_returns, 'weekly')

    if 'weekly_optimized_signal' in weekly_metrics:
        values = [
            weekly_metrics['weekly_optimized_signal']['annualized_total_return'] * 100,
            weekly_metrics['weekly_optimized_signal']['sharpe_ratio'],
            weekly_metrics['weekly_optimized_signal']['win_rate'] * 100,
            weekly_metrics['weekly_optimized_signal']['information_ratio'],
            weekly_metrics['weekly_optimized_signal']['calmar_ratio']
        ]
        values += values[:1]  # 闭合图形

        ax2.plot(angles, values, 'o-', linewidth=2, label='周频优化策略', color='#FF6B6B')
        ax2.fill(angles, values, alpha=0.25, color='#FF6B6B')

    ax2.set_xticks(angles[:-1])
    ax2.set_xticklabels(categories, fontsize=12)
    ax2.set_title('周频优化策略绩效雷达图', fontsize=16, fontweight='bold', pad=20)
    ax2.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
    ax2.grid(True)

    plt.tight_layout()
    plt.savefig(f'{output_dir}/优化策略绩效雷达图对比.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("✓ 优化策略绩效雷达图对比.png")

def save_optimized_performance_excel(daily_metrics, weekly_metrics, daily_returns, weekly_returns, output_dir):
    """保存优化绩效Excel文件"""
    print(f"\n{'='*60}")
    print("📊 保存优化绩效Excel文件")
    print(f"{'='*60}")

    with pd.ExcelWriter(f'{output_dir}/优化30Y国债择时策略绩效分析报告.xlsx') as writer:

        # 日频绩效指标
        daily_metrics_df = pd.DataFrame(daily_metrics).T
        daily_metrics_df.index.name = '策略名称'
        # 转换为百分比显示
        for col in ['total_return', 'annualized_total_return', 'annualized_volatility', 'max_drawdown', 'win_rate', 'excess_return', 'annualized_excess_return']:
            if col in daily_metrics_df.columns:
                daily_metrics_df[col] = daily_metrics_df[col] * 100
        daily_metrics_df.to_excel(writer, sheet_name='日频优化绩效指标')

        # 周频绩效指标
        weekly_metrics_df = pd.DataFrame(weekly_metrics).T
        weekly_metrics_df.index.name = '策略名称'
        # 转换为百分比显示
        for col in ['total_return', 'annualized_total_return', 'annualized_volatility', 'max_drawdown', 'win_rate', 'excess_return', 'annualized_excess_return']:
            if col in weekly_metrics_df.columns:
                weekly_metrics_df[col] = weekly_metrics_df[col] * 100
        weekly_metrics_df.to_excel(writer, sheet_name='周频优化绩效指标')

        # 日频累计收益率
        daily_cumret_cols = ['date', 'benchmark_cumret'] + [f'{s}_cumret' for s in ['top_factor_signal', 'dynamic_threshold_signal', 'ml_enhanced_signal', 'multi_timeframe_signal'] if f'{s}_cumret' in daily_returns.columns]
        daily_cumret_df = daily_returns[daily_cumret_cols].copy()
        daily_cumret_df.columns = ['日期', '基准累计收益率', 'TOP因子精准择时累计收益率', '动态阈值优化累计收益率', '机器学习增强累计收益率', '多时间框架融合累计收益率'][:len(daily_cumret_cols)]
        # 转换为百分比
        for col in daily_cumret_df.columns[1:]:
            daily_cumret_df[col] = daily_cumret_df[col] * 100
        daily_cumret_df.to_excel(writer, sheet_name='日频优化累计收益率', index=False)

        # 周频累计收益率
        weekly_cumret_cols = ['date', 'benchmark_cumret'] + [f'{s}_cumret' for s in ['weekly_optimized_signal'] if f'{s}_cumret' in weekly_returns.columns]
        weekly_cumret_df = weekly_returns[weekly_cumret_cols].copy()
        weekly_cumret_df.columns = ['日期', '基准累计收益率', '周频优化择时累计收益率'][:len(weekly_cumret_cols)]
        # 转换为百分比
        for col in weekly_cumret_df.columns[1:]:
            weekly_cumret_df[col] = weekly_cumret_df[col] * 100
        weekly_cumret_df.to_excel(writer, sheet_name='周频优化累计收益率', index=False)

        # 信号详情
        signal_detail_df = daily_returns[['date', '30y', 'top_factor_signal', 'dynamic_threshold_signal', 'ml_enhanced_signal', 'multi_timeframe_signal', 'top_score', 'max_top_score', 'volatility_regime']].copy()
        signal_detail_df.columns = ['日期', '30Y收益率', 'TOP因子信号', '动态阈值信号', '机器学习信号', '多时间框架信号', 'TOP因子得分', '最大得分', '波动率状态']
        signal_detail_df.to_excel(writer, sheet_name='信号详情', index=False)

    print("✓ 优化30Y国债择时策略绩效分析报告.xlsx")

# 计算优化收益率
daily_returns = calculate_optimized_portfolio_returns(daily_signals, 'daily')
weekly_returns = calculate_optimized_portfolio_returns(weekly_signals, 'weekly')

# 计算优化绩效指标
daily_metrics = calculate_optimized_performance_metrics(daily_returns, 'daily')
weekly_metrics = calculate_optimized_performance_metrics(weekly_returns, 'weekly')

# 创建优化图表
create_optimized_performance_charts(daily_returns, weekly_returns, output_dir)

# 保存优化Excel文件
save_optimized_performance_excel(daily_metrics, weekly_metrics, daily_returns, weekly_returns, output_dir)
